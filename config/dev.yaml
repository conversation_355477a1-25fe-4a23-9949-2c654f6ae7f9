# llm
llm:
  model: qwen32b                                      # 模型名称
  base_url: http://10.20.1.50:9032/v1                 # 模型地址 
  api_key: qwen2-5-api-sign-vllm-v5-8Gy19F2fa9T5      # 模型密钥
  temperature: 0                                    # 采样温度参数

# embedding
emb:
  model: stella                                       # 模型名称 
  base_url: http://10.20.1.51:8872                    # 模型地址 
  emb_key: EMB-DC60A938-89CF-1743-100C-F407A0D1B47F   # 模型密钥

semaphore: 30      # 注释生成的并发数
result_rows: 100    # 分析生成的条目数
history_num: 3      # 对话历史记录个数
refine_turns: 3     # 自动纠错最大轮数
few_shot_num: 5     # few shot  的数量

# table filter
chat_data_table_filter:   # 问数的表过滤参数
  table_emb_num: 5        # 表名向量个数
  column_emb_num: 30      # 列名向量个数
  final_num: 3            # 最终返回的表个数
  biz_emb_num: 1          # 业务向量个数
  llm_filter: none # none, all, (alone) 勿动

chat_dashboard_table_filter:  # 仪表盘表过滤参数
  table_emb_num: 20
  column_emb_num: 30
  final_num: 10
  biz_emb_num: 1
  llm_filter: none # none, all, (alone)

chat_dashboard_table_schema:  # 仪表盘分析参数
  limit_tokens: 7000          # 分析最大tokens


argodb_jdbc:
  jar_name: inceptor-driver-8.31.2.jar
  auth_mode: LDAP  # LDAP, Kerberos
  user_principal: user_principal
  keytab_path: keytab_path
  krb5_conf_path: krb5_conf_path


# 数据库
mongo:
  db_name: aidb_base
  ip: ************
  port: 30241
minio:
  bucket_name: aidb-files
  endpoint: ************:30292
  access_key: minio
  secret_key: vjM2YZyNRE6A
mysql:
  db_name: ovitboot-data
  host: ************
  port: 30202
  user: ovit
  password: <EMAIL>
milvus:
  db_name: aidb_milvus
  col_list:
    biz_relation: biz_relation
    table_info: table_info
    column_info: column_info
    few_shot: few_shot
  ip: ************
  port: 30059
redis:
  host: ************
  port: 30203
  password: 2TqYSGjvYT
  db: 15
kmp: https://dev.bowen-data.cnovit.com
