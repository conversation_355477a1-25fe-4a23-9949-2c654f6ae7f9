# 智数-后端
## 项目简介

智数-后端是一个基于FastAPI框架构建的后端服务，提供了多种数据源管理、聊天数据处理和聊天LLM（大语言模型）相关的API接口。

## 主要功能

- **数据源管理**：提供数据源的增删改查功能。
- **聊天数据处理**：处理和管理聊天相关的数据。
- **聊天LLM**：与大语言模型进行交互，提供智能聊天功能。
- **聊天仪表盘**：提供聊天数据的可视化和分析功能。

## 安装与运行

### 环境要求

- Python 3.11+
- FastAPI
- Uvicorn

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行服务

```bash
uvicorn aidb_app:app --host 0.0.0.0 --port 8795
```

或者使用以下命令在后台运行：

```bash
nohup python src/aidb_app.py > log/aidb_app_dev.log 2>&1 &
```

## API文档

启动服务后，可以通过访问 `http://localhost:8795/docs` 查看自动生成的API文档。
