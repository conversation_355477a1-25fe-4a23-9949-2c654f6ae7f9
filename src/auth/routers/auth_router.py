import os
import sys

from src.common.utils.logger import logger
from src.common.utils.config import config_data

from fastapi import APIRouter, Security
from fastapi.security import APIKeyHeader


router = APIRouter()

from src.auth.schemas.auth_schema import ValidateLogin
from src.auth.services.auth_service import validate_login


api_key_header = APIKeyHeader(name="X-Access-Token", auto_error=False)


@router.post("/validate_login")
async def _validate_login(api_key: str = Security(api_key_header)):
    """根据token获取用户权限，并存入redis数据"""

    response_data = await validate_login(
        api_key=api_key,
    )

    return response_data


from src.auth.schemas.auth_schema import UserPassword
from src.auth.services.auth_service import get_token


@router.post("/get_token")
async def _get_token(input_data: UserPassword):
    """测试使用"""

    user_id = input_data.user_id
    password = input_data.password
    
    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )
    
    response_data = await get_token(
        user_id=user_id,
        password=password,
    )

    return response_data
