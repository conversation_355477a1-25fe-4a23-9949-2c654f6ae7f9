from typing import *
from pydantic import BaseModel, Field


class ValidateLogin(BaseModel):
    user_id: str = Field(
        ...,
        title="用户ID",
        description="用户的唯一ID",
        examples=["wenwuq"],
    )


class UserPassword(BaseModel):
    user_id: str = Field(
        ...,
        title="用户ID",
        description="用户的唯一ID",
        examples=["wenwuq"],
    )
    password: str = Field(
        ..., title="密码", description="用户的密码", examples=["ovit@123"]
    )