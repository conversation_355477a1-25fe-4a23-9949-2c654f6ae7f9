import json
import requests
import jwt
import time

from fastapi import HTTPException

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.lifespan.db_client import db_client

import urllib3
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)

MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 0.05  # 每次重试之间的延迟（秒）

async def validate_login(api_key: str):
    try:
        redis_client = db_client["redis_client"]
        kmp_url = config_data["kmp"]

        # 调用kmp获取用户权限信息
        perm_url = f"{kmp_url}/kmp-api/sys/permission/getUserPermissionByToken"

        perm_headers = {
            "Accept": "application/json",
            "X-Access-Token": api_key,
            # "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        }
        
        for attempt in range(MAX_RETRIES):
            try:
                perm_data = requests.get(perm_url, headers=perm_headers, verify=False).json()
            except Exception as e:
                logger.error(f"获取用户权限信息失败，尝试第 {attempt + 1} 次: {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY)  # 重试前等待20毫秒
                else:
                    raise  # 如果是最后一次重试，抛出异常

    except Exception as e:
        logger.error(f"用户权限信息获取失败: {e}")
        return {
            "success": False,
            "message": f"用户权限信息获取失败: {e}",
            "code": 404,
            "result": None,
            "timestamp": time.time(),
        }

    try:
        if perm_data.get("success", False):
            login_info = jwt.decode(api_key, options={"verify_signature": False})
            user_id = login_info["username"]
            redis_client.set(user_id, json.dumps(perm_data))

    except Exception as e:
        logger.error(f"Redis操作时发生错误: {e}")
        return {
            "success": False,
            "message": f"redis数据存储失败: {e}",
            "code": 401,
            "result": None,
            "timestamp": time.time(),
        }

    return perm_data


import re
import json
import requests
from gmssl import sm2
from src.common.utils.config import python_config


async def get_token(user_id: str, password: str):
    try:
        sys_public_key = "04829831eae5fad46f3bc2d5102fb394647d8383c3c51373547192f677733729fa6c44aeb2e4a856310c299027202db92dc96d76662d2455b733bb3f3eb15d44aa"
        ticket_url = f"https://{python_config}.bowen-data.cnovit.com/kmp-api"
        uap_url = "https://uap.cnovit.com/uapcloud/login"

        # SM2 加密
        headers = {
            "Connection": "keep-alive",
            "Content-Type": "application/json",
            "Accept": "*/*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        }

        sm2_crypt = sm2.CryptSM2(public_key=sys_public_key, private_key=None, mode=1)
        encrypted_password = sm2_crypt.encrypt(password.encode("utf-8")).hex()

        # 第一步：登录并获取 ticket

        login_url = f"{ticket_url}/ovit/login"
        login_data = {
            "password": encrypted_password,
            "username": user_id,
            "extraMap": {"callBackUrl": uap_url},
        }
        try:
            login_response = requests.post(
                login_url, json=login_data, headers=headers, verify=False
            )
            login_response.raise_for_status()  # 确保请求成功
            login_response = login_response.json()
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"请求错误: {str(e)}")

        if login_response.get("code", 500) != 200:
            return login_response

        redirect_url = login_response.get("data", {}).get("redirect", "")
        ticket_match = re.search(r"ticket=([^&]+)", redirect_url)

        if not ticket_match:
            raise HTTPException(status_code=500, detail="无法获取ticket")

        # 第二步：验证登录并获取 token
        ticket = ticket_match.group(1)
        validate_url = f"{ticket_url}/sys/cas/client/validateLogin?ticket={ticket}&service={uap_url}"
        validate_headers = {
            "Accept": "application/json",
            "Referer": uap_url,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        }
        validate_response = requests.get(
            validate_url, headers=validate_headers, verify=False
        ).json()
        token = validate_response.get("result", {}).get("token")
        return {"code": 200, "data": token, "msg": "ok"}

    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
