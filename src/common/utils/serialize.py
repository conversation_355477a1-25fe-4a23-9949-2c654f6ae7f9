from datetime import datetime, date
from decimal import Decimal
from uuid import UUID
import math


def serialize(obj, raise_error=True):
    if isinstance(obj, (datetime, date)):
        return obj.strftime("%Y-%m-%d %H:%M:%S")
    if isinstance(obj, UUID):
        return str(obj)  # 将 UUID 转换为字符串
    if isinstance(obj, Decimal):
        return float(obj)
    if isinstance(obj, float):
        if math.isnan(obj) or math.isinf(obj):
            return None

    if raise_error:
        raise TypeError(f"{type(obj)} is not JSON serializable, {obj}")
    else:
        return obj
