ERROR_CODES = {
    # 数据库连接相关错误
    2000: "数据库连接失败",
    2001: "数据库地址错误",
    2002: "数据库连接被拒绝",
    2003: "数据库用户或密码错误",
    2004: "数据库实例不可用",
    2005: "数据库配置错误或版本不兼容",
    2006: "数据库格式不支持",
    # 插入（Insert）操作错误
    2100: "插入数据失败",
    2101: "插入失败，违反唯一性约束",
    2102: "插入数据的格式或结构不符合要求",
    # 更新（Update）操作错误
    2200: "更新数据失败",
    2201: "未找到需要更新的数据",
    2202: "更新失败，数据可能已被其他进程修改",
    # 删除（Delete）操作错误
    2300: "删除数据失败",
    2301: "未找到需要删除的数据",
    2302: "没有权限删除该数据",
    2303: "删除条件格式错误",
    # 查询（Query）操作错误
    2400: "查询数据失败",
    2401: "未找到匹配的数据",
    2402: "查询条件格式错误",
    ################################################################
    # 文件格式错误
    3000: "不支持的文件格式",
    3001: "文件损坏或无法识别",
    3002: "文件编码格式不支持",
    3003: "文件大小超过限制",
    3004: "文件内容格式不符合要求",
    # 文件上传错误
    3100: "文件上传失败",
    3101: "上传的文件超过系统限制",
    3102: "存储空间不足，无法上传",
    3103: "文件名冲突或已存在",
    # 文件下载错误
    3200: "文件下载失败",
    3201: "请求的文件不存在",
    3202: "下载超时，请重试",
    3203: "没有下载权限",
    # 文件读取错误
    3300: "文件读取失败",
    3301: "文件被其他进程锁定",
    3302: "文件路径无效或不存在",
    # 文件保存错误
    3400: "文件保存失败",
    3401: "存储空间不足，无法保存",
    3402: "保存路径没有写入权限",
    3403: "文件名包含非法字符",
    ################################################################
    # 大模型错误
}


def return_error(code: int, msg: str = None, e=None) -> dict:
    # 获取错误信息，优先级：detail -> errmsg -> str(e)
    error_message = getattr(e, "detail", getattr(e, "errmsg", str(e))) if e else None

    # 获取返回的消息
    return_msg = msg or ERROR_CODES.get(code, "未知错误")

    if error_message:
        return_msg = f"{return_msg}: {error_message}"

    return {
        "code": code,
        "data": None,
        "msg": return_msg,
    }

