import copy


from datetime import datetime
from fastapi import HTTPException
from pymongo.errors import PyMongoError

from src.aagent.utils.utils import random_uuid
from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.object_id import convert_to_object_id
from src.common.utils.error_codes import ERROR_CODES

from src.common.lifespan.db_client import db_client


async def make_history(
    talk_id: str,
    qa_list: list,
    exclude_agent: list | None = None,
    check_talk_id=True,
):
    history = []
    exclude_agent = exclude_agent or []

    await convert_history(talk_id=talk_id, qa_list=qa_list, check_talk_id=check_talk_id)

    for i, record in enumerate(qa_list):
        for _, msg in record["message"].items():
            if msg["name"] not in exclude_agent:
                
                if msg["name"] == "GS_agent":
                    try:
                        ex_content = (
                            "\n生成 SQL:\n " + msg["metadata"]["struct_sql"]["data"]["sql"]
                        )
                    except:
                        ex_content = ""

                    content = msg["ans"]["content"] + ex_content
                    if not content:
                        continue

                    history.append({"role": "assistant", "content": content})
                elif msg["name"] == "RF_agent":
                    try:
                        ex_content = (
                            "\n纠错 SQL:\n " + msg["metadata"]["refine_sql"]["data"]["sql"]
                        )
                    except:
                        ex_content = ""
                    content = msg["ans"]["content"] + ex_content
                    if not content:
                        continue
                    history.append({"role": "assistant", "content": content})

                elif msg["name"] == "SS_agent":
                    try:
                        ex_content = (
                            "\n最终结果:\n"
                            + msg["metadata"]["final_sql_result"]["data"]["markdown_res"]
                        )
                    except:
                        ex_content = ""
                    content = msg["ans"]["content"] + ex_content
                    if not content:
                        continue
                    history.append({"role": "assistant", "content": content})
                else:
                    if msg_content := msg["ans"]["content"]:
                        if msg["ans"]["role"] != "tool":
                            history.append(
                                {"role": msg["ans"]["role"], "content": msg_content}
                            )

    return history


async def convert_history(
    talk_id: str,
    qa_list: list,
    check_talk_id=True,
):
    qa_list = copy.deepcopy(qa_list)
    for i, record in enumerate(qa_list):

        if check_talk_id:
            # 验证 talk_id 是否正确
            if record["talk_id"] != talk_id:
                logger.error(f"qa_list 中出现错误的 talk_id: {talk_id}")
                raise HTTPException(
                    status_code=500,
                    detail=f"qa_list 中出现错误的 talk_id: {talk_id}",
                )

        # 验证 talk_list 是否中间是否存在错误的连接
        if i > 0:  # 确保不是第一个元素
            previous_record = qa_list[i - 1]
            # 如果当前记录的 parent 不在上一条记录的 children 中
            if record["parent"] != previous_record["qa_id"]:
                logger.error(
                    f"talk_list 中存在错误的父子连接: {record['qa_id']} 的 parent {record['parent']} "
                    f"不指向上一条记录 {previous_record['qa_id']}"
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"talk_list 中存在错误的父子连接: {record['qa_id']} 的 parent {record['parent']} "
                    f"不指向上一条记录 {previous_record['qa_id']}",
                )

            if record["qa_id"] not in previous_record["children"]:
                logger.error(
                    f"talk_list 中存在错误的父子连接: {record['qa_id']} 不存在于"
                    f"上一条记录 {previous_record['qa_id']} 的 children {previous_record['children']} 中"
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"talk_list 中存在错误的父子连接: {record['qa_id']} 不存在于"
                    f"上一条记录 {previous_record['qa_id']} 的 children {previous_record['children']} 中",
                )


async def update_qa_children(user_id: str, qa_id: str, children_id: str):
    object_qa_id = convert_to_object_id(qa_id)
    try:
        result = await db_client["mongo_col"].qa_records.update_one(
            {"user_id": user_id, "_id": object_qa_id, "status": 0},
            {"$push": {"children": children_id}},
            upsert=False,
        )
    except PyMongoError as e:
        raise HTTPException(status_code=2200, detail=e)
    if not result.matched_count > 0:
        raise HTTPException(status_code=2201, detail=ERROR_CODES[2201])
    if not result.modified_count > 0:
        raise HTTPException(status_code=2202, detail=ERROR_CODES[2202])


async def create_user_resp(
    user_ask: str,
    metadata: dict = None,
):
    metadata = metadata or {}

    # 构造 user ask 记录
    user_record_id = "chatcmpl-" + random_uuid()
    user_record = {
        user_record_id: {
            "name": "USER_agent",
            "description": "用户提问",
            "ans": {
                "role": "user",
                "content": user_ask,
            },
            "metadata": metadata,
        }
    }

    return user_record


async def create_qa_record(
    user_id: str,
    talk_id: str,
    qa_list: list,
    save_data=True,
):
    # 获取父记录
    parent = ""
    if qa_list:
        parent = qa_list[-1]["qa_id"]

    if save_data:
        now_time = datetime.now()
        qa_record = {
            "talk_id": talk_id,
            "user_id": user_id,
            "message": {},
            "create_time": now_time,
            "update_time": now_time,
            "status": 0,
            "parent": parent,
            "children": [],
        }
        # 插入 QA 记录
        try:
            result = await db_client["mongo_col"].qa_records.insert_one(qa_record)
            qa_id = str(result.inserted_id)
        except PyMongoError as e:
            raise HTTPException(status_code=2100, detail=e)
        if not qa_id:
            raise HTTPException(status_code=2100, detail=ERROR_CODES[2100])

        # 更新 parent 的 children
        if parent:
            await update_qa_children(
                user_id=user_id, qa_id=parent, children_id=str(result.inserted_id)
            )

        return qa_id
    else:
        return random_uuid()


async def build_record_data(
    user_id: str,
    talk_id: str,
    qa_list: list,
    qa_id: str,
    resp_raw: dict,
):
    """构造 msg"""

    # 获取父记录
    parent = ""
    if qa_list:
        parent = qa_list[-1]["qa_id"]

    # 构建返回的字典
    now_time = datetime.now()
    return {
        "qa_id": qa_id,
        "talk_id": talk_id,
        "user_id": user_id,
        "message": resp_raw,
        "create_time": now_time,
        "update_time": now_time,
        "status": 0,
        "parent": parent,
        "children": [],
    }


async def save_qa_record(
    user_id: str,
    talk_id: str,
    qa_id: str,
    qa_record: dict,
):
    """保存更新 qa record"""
    # 获取现有的 QA 记录
    object_qa_id = convert_to_object_id(qa_id)

    try:
        existing_record = await db_client["mongo_col"].qa_records.find_one(
            {"user_id": user_id, "_id": object_qa_id, "status": 0}
        )
    except PyMongoError as e:
        raise HTTPException(status_code=2400, detail=e)
    if not existing_record:
        raise HTTPException(status_code=2401, detail=ERROR_CODES[2401])

    # 将现有的 message 和新的 message 合并
    user_message = existing_record.get("message", {})
    updated_message = {**user_message, **qa_record["message"]}  # 合并 message 字典

    # 使用 $set 更新 QA 记录中的 message 字段
    now_time = datetime.now()

    try:
        result = await db_client["mongo_col"].qa_records.update_one(
            {"user_id": user_id, "_id": object_qa_id, "status": 0},
            {"$set": {"message": updated_message, "update_time": now_time}},
            upsert=False,
        )
    except PyMongoError as e:
        raise HTTPException(status_code=2200, detail=e)
    if not result.matched_count > 0:
        raise HTTPException(status_code=2201, detail=ERROR_CODES[2201])
    if not result.modified_count > 0:
        raise HTTPException(status_code=2202, detail=ERROR_CODES[2202])

    # 更新 talk_id 的时间
    object_talk_id = convert_to_object_id(talk_id)

    try:
        result = await db_client["mongo_col"].talk_records.update_one(
            {"user_id": user_id, "_id": object_talk_id},
            {"$set": {"update_time": now_time}},
            upsert=False,
        )
    except PyMongoError as e:
        raise HTTPException(status_code=2200, detail=e)
    if not result.matched_count > 0:
        raise HTTPException(status_code=2201, detail=ERROR_CODES[2201])
    if not result.modified_count > 0:
        raise HTTPException(status_code=2202, detail=ERROR_CODES[2202])
