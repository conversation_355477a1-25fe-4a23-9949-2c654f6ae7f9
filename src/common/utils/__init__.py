import sys
import os

sys.path.append(os.getcwd().split("/src")[0])

[
    {"role": "user", "content": "列出所有已发货订单的详细信息"},
    {
        "role": "assistant",
        "content": '目标表：orders\n字段：order_id, customer_id, product_id, quantity, order_date, order_status\n过滤条件：order_status = "已发货"\n排序逻辑：按order_date升序排列订单信息。',
    },
    {
        "role": "assistant",
        "content": "\n生成 SQL:\n SELECT order_id AS '订单ID', customer_id AS '客户ID', product_id AS '产品ID', quantity AS '数量', order_date AS '下单日期', order_status AS '订单状态'\nFROM orders\nWHERE order_status = \"已发货\"\nORDER BY order_date ASC\nLIMIT 100;",
    },
    {
        "role": "assistant",
        "content": "\n最终结果:\n| 订单ID | 客户ID | 产品ID | 数量 | 下单日期 | 订单状态 |\n| --- | --- | --- | --- | --- | --- |\n| 43 | 38 | 2 | 5 | 2023-07-08 | 已发货 |\n| 149 | 16 | 2 | 1 | 2023-07-08 | 已发货 |\n| 157 | 44 | 8 | 10 | 2023-07-10 | 已发货 |\n| 40 | 23 | 8 | 10 | 2023-07-15 | 已发货 |\n| 20 | 19 | 5 | 10 | 2023-07-16 | 已发货 |\n| 91 | 50 | 8 | 7 | 2023-07-28 | 已发货 |\n| 88 | 11 | 2 | 7 | 2023-08-01 | 已发货 |\n| 60 | 35 | 1 | 8 | 2023-08-02 | 已发货 |\n| 191 | 9 | 3 | 2 | 2023-08-21 | 已发货 |\n| 358 | 38 | 16 | 3 | 2023-08-23 | 已发货 |\n| 450 | 15 | 28 | 8 | 2023-08-25 | 已发货 |\n| 65 | 46 | 5 | 8 | 2023-08-27 | 已发货 |\n| 140 | 50 | 10 | 5 | 2023-08-31 | 已发货 |\n| 346 | 24 | 20 | 4 | 2023-09-02 | 已发货 |\n| 115 | 16 | 8 | 1 | 2023-09-03 | 已发货 |\n| 323 | 46 | 15 | 10 | 2023-09-06 | 已发货 |\n| 103 | 1 | 9 | 5 | 2023-09-07 | 已发货 |\n| 152 | 18 | 1 | 8 | 2023-09-12 | 已发货 |\n| 176 | 8 | 3 | 4 | 2023-09-12 | 已发货 |\n| 385 | 8 | 15 | 5 | 2023-09-13 | 已发货 |\n| 500 | 38 | 25 | 7 | 2023-09-14 | 已发货 |\n| 599 | 27 | 23 | 9 | 2023-09-14 | 已发货 |\n| 352 | 3 | 11 | 2 | 2023-09-18 | 已发货 |\n| 21 | 43 | 3 | 1 | 2023-09-20 | 已发货 |\n| 198 | 11 | 5 | 5 | 2023-09-21 | 已发货 |\n| 249 | 2 | 16 | 1 | 2023-09-22 | 已发货 |\n| 113 | 40 | 6 | 7 | 2023-09-23 | 已发货 |\n| 121 | 25 | 1 | 7 | 2023-09-24 | 已发货 |\n| 102 | 33 | 1 | 8 | 2023-09-25 | 已发货 |\n| 111 | 24 | 8 | 7 | 2023-09-28 | 已发货 |\n| 548 | 39 | 20 | 9 | 2023-09-30 | 已发货 |\n| 467 | 10 | 26 | 7 | 2023-10-01 | 已发货 |\n| 588 | 39 | 24 | 1 | 2023-10-01 | 已发货 |\n| 596 | 22 | 22 | 6 | 2023-10-01 | 已发货 |\n| 306 | 30 | 12 | 2 | 2023-10-01 | 已发货 |\n| 117 | 16 | 9 | 8 | 2023-10-07 | 已发货 |\n| 491 | 45 | 27 | 5 | 2023-10-08 | 已发货 |\n| 499 | 50 | 21 | 3 | 2023-10-08 | 已发货 |\n| 108 | 26 | 4 | 4 | 2023-10-09 | 已发货 |\n| 550 | 22 | 30 | 4 | 2023-10-10 | 已发货 |\n| 587 | 13 | 23 | 3 | 2023-10-16 | 已发货 |\n| 527 | 32 | 20 | 10 | 2023-10-19 | 已发货 |\n| 144 | 28 | 5 | 8 | 2023-10-19 | 已发货 |\n| 294 | 39 | 20 | 6 | 2023-10-19 | 已发货 |\n| 379 | 48 | 15 | 8 | 2023-10-23 | 已发货 |\n| 275 | 7 | 18 | 4 | 2023-10-25 | 已发货 |\n| 30 | 46 | 1 | 9 | 2023-10-26 | 已发货 |\n| 11 | 11 | 5 | 5 | 2023-11-01 | 已发货 |\n| 310 | 33 | 19 | 10 | 2023-11-02 | 已发货 |\n| 259 | 28 | 14 | 1 | 2023-11-02 | 已发货 |",
    },
]
