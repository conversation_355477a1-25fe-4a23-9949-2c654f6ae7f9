from typing import Union, List, Dict
from pathlib import Path


class FileTypeChecker:
    # 默认的支持类型
    default_supported_types = {
        "text": [".txt"],
        "eml": [".eml", ".msg"],
        "csv": [".csv"],
        "excel": [".xlsx", ".xls"],
        "json": [".json"],
        "xml": [".xml"],
        "epub": [".epub"],
        "html": [".html"],
        "pdf": [".pdf"],
        "doc": [".docx", ".doc"],
        "ppt": [".ppt", ".pptx"],
        "markdown": [".md"],
        "image": [
            ".jpg",
            ".jpeg",
            ".png",
            ".bmp",
            ".tif",
            ".raw",
            ".gif",
            ".webp",
        ],
    }

    def __init__(
        self,
        required_categories: Union[str, List[str]] | None = None,
        excluded_categories: Union[str, List[str]] | None = "image",
    ):
        """初始化，可设置类型和排除类型

        Args:
            required_categories: 检查的类型. 例如，["text", "eml", "csv", "excel", "json", "xml", "epub", "html", "pdf", "doc", "ppt", "markdown", "image"]. 默认为 None.
            excluded_categories: 排除的类型. 默认为 "image".
        """
        if isinstance(required_categories, str):
            required_categories = [required_categories]
        if isinstance(excluded_categories, str):
            excluded_categories = [excluded_categories]

        # 初始化所需分类和排除分类
        self.required_categories = required_categories or list(
            FileTypeChecker.default_supported_types.keys()
        )
        self.excluded_categories = excluded_categories

        # 设置支持的类型
        self.supported_types = {
            category: extensions
            for category, extensions in FileTypeChecker.default_supported_types.items()
            if category in self.required_categories
        }

        # 排除指定的分类
        self.supported_types = {
            category: extensions
            for category, extensions in self.supported_types.items()
            if category not in self.excluded_categories
        }

    @staticmethod
    def get_suffix(file_path: str) -> str:
        """返回文件扩展名

        Args:
            file_path (str): 文件地址

        Returns:
            str: 文件的扩展名，带'.'号
        """
        return Path(file_path).suffix.lower()

    @staticmethod
    def get_filename(file_path: str) -> str:
        """返回文件名（不包含路径和扩展名）

        Args:
            file_path (str): 文件地址

        Returns:
            str: 文件名
        """
        return Path(file_path).stem

    @staticmethod
    def get_file_path(file_path: str) -> str:
        """返回文件路径（不包含文件名）

        Args:
            file_path (str): 文件地址

        Returns:
            str: 文件路径
        """
        return str(Path(file_path).parent)

    def get_type(self, file_path: str) -> str:
        """返回文件的类型（分类）

        Args:
            file_path (str): 文件地址

        Returns:
            str: 返回文件的分类
        """
        ext = self.get_suffix(file_path)
        for type_name, extensions in self.supported_types.items():
            if ext in extensions:
                return type_name
        return "unknown"

    def check(self, file_path: str) -> bool:
        """检查文件是否支持

        Args:
            file_path (str): 文件地址

        Returns:
            bool: 支持与否
        """
        file_type = self.get_type(file_path)
        return file_type in self.supported_types

    def supported_types(self) -> Dict[str, List[str]]:
        """返回所有支持的文件类型及其扩展名

        Returns:
            Dict[str, List[str]]: 获得所有的支持类型和扩展名
        """
        return self.supported_types

    def supported_suffix(self) -> List[str]:
        """返回所有支持的文件扩展名

        Returns:
            List[str]: 所有支持的文件扩展名
        """
        file_extensions = set()
        for extensions in self.supported_types.values():
            file_extensions.update(extensions)
        return [f"'{ext}'" for ext in file_extensions]
