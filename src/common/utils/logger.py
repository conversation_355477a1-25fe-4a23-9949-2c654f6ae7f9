import os
import sys
import time
import inspect
import uuid
import contextvars
from typing import Optional

from functools import wraps
from loguru import logger as loguru_logger
from src.common.utils.config import python_config

# 创建请求ID的上下文变量
request_id_var = contextvars.ContextVar("request_id", default=None)


def get_request_id() -> str:
    """获取当前请求ID，如果不存在则创建一个新的"""
    request_id = request_id_var.get()
    if request_id is None:
        request_id = str(uuid.uuid4())
        request_id_var.set(request_id)
    return request_id


def set_request_id(request_id: Optional[str] = None) -> None:
    """设置请求ID到上下文变量"""
    if request_id is None:
        request_id = str(uuid.uuid4())
    request_id_var.set(request_id)


# 设置 logger
def setup_logger(log_dir):
    # 移除默认的loguru handler
    loguru_logger.remove()

    os.makedirs(log_dir, exist_ok=True)
    # 添加请求ID和函数名到日志格式
    formatter = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</> | <level>{level}</> | <yellow>{extra[request_id]}</> | <cyan>{file.path}:{line}</> | <blue>{function}</> | <level>{message}</>"

    # 添加控制台日志
    loguru_logger.add(
        sys.stdout,
        level="INFO",
        format=formatter,
        colorize=True,
    )

    # info.log
    loguru_logger.add(
        os.path.join(log_dir, "info.log"),
        level="INFO",
        format=formatter,
        rotation="1 day",  # 每天生成一个新日志文件
        encoding="utf-8",  # 设置保存格式
        compression="zip",  # 压缩旧日志文件
        filter=lambda record: record["level"].no >= logger.level("INFO").no,
        enqueue=True,  # 使用线程安全的方式将日志事件放入队列
    )
    # error.log
    loguru_logger.add(
        os.path.join(log_dir, "error.log"),
        level="ERROR",
        format=formatter,
        rotation="1 day",
        encoding="utf-8",
        compression="zip",
        filter=lambda record: record["level"].no >= logger.level("ERROR").no,
        enqueue=True,
    )

    # critical.log
    loguru_logger.add(
        os.path.join(log_dir, "critical.log"),
        level="CRITICAL",
        format=formatter,
        rotation="1 day",
        encoding="utf-8",
        compression="zip",
        filter=lambda record: record["level"].no >= logger.level("CRITICAL").no,
        enqueue=True,
    )

    # warning.log
    loguru_logger.add(
        os.path.join(log_dir, "warning.log"),
        level="WARNING",
        format=formatter,
        rotation="1 day",
        encoding="utf-8",
        compression="zip",
        filter=lambda record: record["level"].no >= logger.level("WARNING").no,
        enqueue=True,
    )

    # debug.log 仅 dev 环境开启
    if python_config == "dev":
        loguru_logger.add(
            os.path.join(log_dir, "debug.log"),
            level="DEBUG",
            format=formatter,
            rotation="1 day",
            encoding="utf-8",
            compression="zip",
            filter=lambda record: record["level"].no >= logger.level("DEBUG").no,
            enqueue=True,  # 使用线程安全的方式将日志事件放入队列
            backtrace=True,  # 显示完整的错误调用栈
            diagnose=True,  # 日志中打印变量的详细类型和值
        )

    # 使用拦截器为每条日志添加请求ID
    def request_id_filter(record):
        record["extra"]["request_id"] = get_request_id()
        return True

    patched_logger = loguru_logger.patch(request_id_filter)
    return patched_logger


def get_logger(logger_dir: str = "log"):
    return setup_logger(logger_dir)


def log_runtime(func):
    """装饰器：记录函数运行时间并使用loguru.debug输出"""
    if inspect.iscoroutinefunction(func):

        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start = time.perf_counter()
            result = await func(*args, **kwargs)
            elapsed = time.perf_counter() - start
            logger.debug(
                f"Function '{func.__name__}' executed in {elapsed:.4f} seconds"
            )
            return result

        return async_wrapper
    else:

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start = time.perf_counter()
            result = func(*args, **kwargs)
            elapsed = time.perf_counter() - start
            logger.debug(
                f"Function '{func.__name__}' executed in {elapsed:.4f} seconds"
            )
            return result

        return sync_wrapper


# 初始化 logger
logger = get_logger()
