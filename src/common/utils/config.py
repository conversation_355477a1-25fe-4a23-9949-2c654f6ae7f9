import os
import sys

from pathlib import Path
from bigtools import load_yaml

# 获取当前工作目录
current_dir = os.getcwd()

# 获取 /src 同级的父级目录路径
root_dir = current_dir.split("/src")[0]

# 读取配置文件
# root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


config_data = load_yaml(
    os.path.join(
        os.path.join(root_dir, "config"), os.getenv("PYTHON_CONFIG", "dev") + ".yaml"
    )
)

python_config = os.getenv("PYTHON_CONFIG", "dev")

FILES_DIR = Path(os.getcwd()) / "src/files"
