import httpx
import asyncio
from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.lifespan.db_client import db_client

from fastapi import HTTPException


async def text_to_dense_vector(texts, n_dims=1024):
    emb_config = config_data["emb"]
    url = emb_config["base_url"] + "/text2vec"
    payload = {"sign": emb_config["emb_key"], "texts": texts, "n_dims": n_dims}

    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=payload)

    if response.status_code == 200:
        return response.json().get("data", [])
    else:
        raise HTTPException(status_code=response.status_code, detail=response.text)


# async def text_to_sparse_vector(texts):
#     try:
#         logger.info(texts)
#         bm25_ef = db_client["bm25_ef"]
#         docs_embeddings = bm25_ef.encode_documents(texts)

#         return docs_embeddings

#     except HTTPException as e:
#         logger.error(f"HTTP错误: {str(e)}")
#         raise e
#     except Exception as e:
#         logger.exception(f"发生错误: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
