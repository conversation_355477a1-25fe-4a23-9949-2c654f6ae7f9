from bson import ObjectId
from fastapi import HTTPException
from pymongo.errors import PyMongoError


def convert_to_object_id(id: str) -> ObjectId:
    """验证 id 是否为合法的 ObjectId 格式"""
    # 验证 db_id 是否为合法的 ObjectId 格式
    try:
        object_id = ObjectId(id)
        return object_id
    except Exception:
        raise HTTPException(status_code=2402, detail="")


async def resolve_parent_folder_id(
    user_id: str, parent_dir_id: str, data_dir_col
) -> ObjectId | str:
    """验证文件夹 ID 是否有效"""
    if parent_dir_id == "":  # 空字符串表示根目录
        return ""

    # 如果 parent_dir_id 不为空字符串，执行验证
    try:
        parent_dir_object_id = convert_to_object_id(parent_dir_id)
    except HTTPException as e:
        raise e

    try:
        # 检查文件夹是否存在
        folder = await data_dir_col.find_one(
            {"user_id": user_id, "_id": parent_dir_object_id, "status": 0}
        )
    except PyMongoError as e:
        raise HTTPException(status_code=2400, detail=e)
    if not folder:
        raise HTTPException(status_code=2401, detail=parent_dir_id)
    return parent_dir_object_id
