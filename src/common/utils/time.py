from datetime import datetime
import time

def get_current_time_with_weekday() -> str:
    """
    获取当前时间的详细信息，格式更友好易懂。
    """
    current_time = datetime.now()
    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 星期几
    week_days = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
    day_of_week = week_days[current_time.weekday()]

    # 闰年判断
    year = current_time.year
    is_leap = (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)
    leap_status = "闰年" if is_leap else "平年"

    # 一年中的第几天和第几周
    day_of_year = current_time.timetuple().tm_yday
    week_number = current_time.isocalendar().week

    # 当前季度
    quarter = (current_time.month - 1) // 3 + 1

    # 是否夏令时
    is_dst = time.localtime().tm_isdst
    dst_status = "夏令时" if is_dst == 1 else "非夏令时"

    # UTC偏移
    utc_offset = current_time.astimezone().strftime("UTC%z")

    # 优化后的输出格式
    return (
        f"当前时间：{formatted_time}（{day_of_week}）\n"
        f"年份类型：{year}年为{leap_status}\n"
        f"日期信息：第 {day_of_year} 天 / 第 {week_number} 周 / 第 {quarter} 季度\n"
        f"时区信息：{utc_offset}，{dst_status}\n"
    )

