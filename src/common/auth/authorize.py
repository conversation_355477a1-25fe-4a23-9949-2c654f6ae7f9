import jwt
import json
from requests import Request

from bigtools import generate_hash_value
from fastapi import HTTPException, status, Security
from fastapi.security import APIKeyHeader

from src.common.lifespan.db_client import db_client
from src.common.utils.logger import logger
from src.common.utils.config import config_data

from src.common.auth.mysql_utils import get_user_info_from_db

"""
用户授权
"""

# 创建一个用于解析路径传参的对象
api_key_header = APIKeyHeader(name="X-Access-Token", auto_error=False)


async def validate_token(token: str):
    try:
        # 解码 JWT Token，跳过签名验证
        login_info = jwt.decode(token, options={"verify_signature": False})
        username = login_info["username"]

        # 从数据库获取用户信息
        user_info = await get_user_info_from_db(username)
        jwt.decode(token, user_info["password"], algorithms=["HS256"])

        return username
    except Exception as e:
        logger.error(f"Token validation failed: {e}")
        return None


from fastapi import Request


async def get_user_id(request: Request, api_key: str = Security(api_key_header)) -> str:
    user_id = await validate_token(api_key)

    if user_id:
        allowed_routes = await get_user_permissions(user_id)

        # 判断请求路径是否以用户允许的权限路径为前缀
        if not any(request.url.path.startswith(route) for route in allowed_routes):
            logger.warning(f"Permission denied for {request.url.path}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Permission denied"
            )

        logger.info(
            f"{user_id:<15} | Token  | {api_key}"
        )

        return user_id

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid or missing API Key"
    )


async def get_user_permissions(user_id: str):
    redis_client = db_client["redis_client"]

    # 将前端功能映射到后端路由
    router_reflect = {
        "home": ["/v1/chat_llm", "/v1/chat_data"],
        "analysis-databoard": ["/v1/chat_dashboard", "/v1/report_manage"],
        "question": ["/v1/data_source", "/v1/semantic_layer"],
        "question-idiom": ["/v1/jargon"],
        "question-relation": ["/v1/biz_relation"],
        "question-casebase": ["/v1/few_shot"],
    }

    permission_info = redis_client.get(user_id)
    if permission_info:
        permission_data = json.loads(permission_info)
    else:
        return []

    router_list = []
    if permission_data["success"] and permission_data["code"] == 0:
        menu = permission_data["result"]["menu"]
        for item in menu:
            name = item["name"]
            if name in router_reflect:
                router_list.extend(router_reflect[name])
                
            # 遍历子菜单
            for child in item.get("children", []):
                child_name = child["name"]
                if child_name in router_reflect:
                    router_list.extend(router_reflect[child_name])

    # 去重并保留顺序
    router_list = list(sorted(set(router_list), key=router_list.index))
    return router_list