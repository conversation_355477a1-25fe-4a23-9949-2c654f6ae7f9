import os
import sys

from src.common.utils.logger import logger
from src.common.utils.config import config_data, python_config

from src.common.lifespan.db_client import db_client


async def get_user_info_from_db(username: str):
    """查询用户的信息"""

    async with db_client["mysql_pool"].acquire() as db:  # 获取连接
        async with db.cursor() as cursor:
            sql = "SELECT * FROM sys_user WHERE username=%s;"
            await cursor.execute(sql, (username,))
            user_info = await cursor.fetchone()

    return user_info


async def get_user_partial_info_from_db(username: str, only_name: bool = False):
    """查询用户的真实姓名、部门与职位"""

    async with db_client["mysql_pool"].acquire() as conn:
        async with conn.cursor() as cursor:
            # 查询用户ID和真实姓名
            sql = "SELECT id, realname, post FROM sys_user WHERE username=%s;"
            await cursor.execute(sql, (username,))
            user_data = await cursor.fetchone()
            if not user_data:
                raise ValueError(f"User {username} not found.")

            user_id = user_data["id"]
            realname = user_data["realname"]
            post_code = user_data["post"]
            user_info = {"realname": realname}

            # 如果需要部门信息，进行查询
            if not only_name:
                # 查询部门信息
                sql = (
                    f"SELECT depart_name FROM sys_depart WHERE id IN "
                    "(SELECT dep_id FROM sys_user_depart WHERE user_id=%s)"
                )
                await cursor.execute(sql, (user_id,))
                depart_name_data = await cursor.fetchall()

                if depart_name_data:
                    # 可能属于多个部门
                    depart_names = [row["depart_name"] for row in depart_name_data]
                    user_info["depart_name"] = depart_names

                # 查询职位信息
                if post_code:
                    sql = "SELECT name FROM sys_position WHERE code=%s;"
                    await cursor.execute(sql, (post_code,))
                    position_data = await cursor.fetchone()

                    if position_data:
                        user_info["position_name"] = position_data["name"]
                    else:
                        user_info["position_name"] = "员工"  # 如果找不到职位信息
                else:
                    user_info["position_name"] = "员工"

    return user_info


######### 以下暂未使用


# async def insert_login_info(username, token, create_time):
#     """
#     插入登录信息
#     :param username: 用户名
#     :param token:
#     :param create_time: 创建时间
#     :return:
#     """
#     db = await aiomysql.create_pool(
#         host=host, port=port, user=user, password=password, db="kgmt"
#     )
#     async with db.acquire() as conn:
#         async with conn.cursor() as cursor:
#             # 使用参数化查询插入数据，防止SQL注入
#             sql = "INSERT INTO login (username, token, create_time) VALUES (%s, %s, %s)"
#             await cursor.execute(sql, (username, token, create_time))
#             await conn.commit()

#     db.close()


# async def execute_sql(
#     database: str, sql: str, dict_cursor: bool = False, fetchall: bool = True
# ):
#     """执行sql语句查询数据，（内部函数）"""
#     if dict_cursor:
#         db = await aiomysql.create_pool(
#             host=host,
#             port=port,
#             user=user,
#             password=password,
#             db=database,
#             cursorclass=aiomysql.DictCursor,
#         )
#     else:
#         db = await aiomysql.create_pool(
#             host=host, port=port, user=user, password=password, db=database
#         )
#     async with db.acquire() as conn:
#         async with conn.cursor() as cursor:
#             await cursor.execute(sql)
#             if fetchall:
#                 data = await cursor.fetchall()
#             else:
#                 data = await cursor.fetchone()

#     db.close()
#     return data


# async def get_user_name_from_db():
#     """查询所有用户的登录名和真实姓名"""
#     sql = "select username, realname from sys_user"
#     user_info = await execute_sql("ovitboot", sql)
#     return user_info


# async def count_user_num_from_db():
#     """统计所有用户数量"""
#     sql = "select count(username) from sys_user"
#     user_num = await execute_sql("ovitboot", sql, fetchall=False)
#     user_num = user_num[0]
#     return user_num


# async def get_all_user_from_db(
#     member_user_id: str, member_name: str, page_num: int, page_size: int
# ):
#     """分页查询所有用户的信息"""
#     start_num, end_num = (page_num - 1) * page_size, page_num * page_size
#     sql = f"select * from sys_user where username like '%{member_user_id}%' and realname like '%{member_name}%' limit {start_num}, {end_num}"
#     user_info = await execute_sql("ovitboot", sql, dict_cursor=True)
#     sql = f"select count(username) from sys_user where username like '%{member_user_id}%' and realname like '%{member_name}%'"
#     total_num = await execute_sql("ovitboot", sql, fetchall=False)
#     total_num = total_num[0]
#     return total_num, user_info


# async def get_user_permission_from_db(username: str):
#     """查询用户权限菜单"""
#     sql = f"select id from sys_user where username='{username}'"
#     user_info = await execute_sql("ovitboot", sql, fetchall=False)
#     user_id = user_info[0]
#     sql = f"select id, role_id from sys_user_role where user_id='{user_id}'"
#     user_role = await execute_sql("ovitboot", sql)
#     role_id = []
#     for i in user_role:
#         role_id.append(i[1])
#     role_id_str = "','".join(role_id)
#     sql = f"select permission_id from sys_role_permission where role_id in ('{role_id_str}')"
#     role_permission = await execute_sql("ovitboot", sql)
#     per_list = []
#     for i in role_permission:
#         ii = i[0]
#         if ii not in per_list:
#             per_list.append(ii)
#     per_str = "','".join(per_list)
#     sql = f"select url from sys_permission where id in ('{per_str}')"
#     permission_1 = await execute_sql("ovitboot", sql)
#     sql = f"select url from sys_permission_v2 where id in ('{per_str}')"
#     permission_2 = await execute_sql("ovitboot", sql)
#     all_permission = [i[0] for i in permission_1 if i[0]] + [
#         i[0] for i in permission_2 if i[0]
#     ]
#     return list(set(all_permission))


if __name__ == "__main__":

    pass
