import os
import sys

from pymilvus import MilvusClient, AsyncMilvusClient, DataType, Function, FunctionType
from pymilvus import connections, db

from src.common.utils.logger import logger
from src.common.utils.config import config_data, python_config


async def load_milvus_client():
    try:
        milvus_config = config_data["milvus"]

        # 设置 MongoDB URI
        ip = milvus_config.get("ip", None)
        port = milvus_config.get("port", None)
        database = milvus_config.get("db_name", None)

        if python_config == "dev":
            database += "_dev"
        elif python_config == "test":
            database += "_test"
        else:
            database += "_prod"

        # 连接到默认数据库
        if ip and port and database:
            uri = f"http://{ip}:{port}"
        else:
            logger.error(f"{'Milvus'.ljust(10)}" + "| " + "milvus_config 不全")
            raise RuntimeError(
                f"{'Milvus'.ljust(10)}" + "| " + "Please check milvus_config"
            )

        logger.info(f"{'Milvus'.ljust(10)}" + "| " + f"正在连接: '{uri}/{database}'")

        # 准备初始化数据库, 连接到默认数据库
        conn = connections.connect(host=ip, port=port)
        milvus_db = db.list_database()
        if database not in milvus_db:
            logger.info(
                f"{'Milvus'.ljust(10)}"
                + "| "
                + f"database '{database}' 不存在，正在创建"
            )
            db.create_database(database)
            logger.info(
                f"{'Milvus'.ljust(10)}" + "| " + f"database '{database}' 创建成功"
            )
        else:
            logger.info(
                f"{'Milvus'.ljust(10)}" + "| " + f"database '{database}' 已存在"
            )

        # 断开连接
        connections.disconnect(alias="default")

        milvus_client = MilvusClient(uri=uri, db_name=database)

        # config 检查
        required_cols = [
            "table_info",
            "column_info",
            "biz_relation",
            "few_shot",
        ]  # 需要检查的 col
        missing_cols = [
            col for col in required_cols if col not in milvus_config["col_list"]
        ]

        if missing_cols:
            logger.error(
                f"{'Milvus'.ljust(10)}"
                + "| "
                + f"col_list 中缺少: {', '.join(missing_cols)}"
            )
            raise RuntimeError(
                f"{'Milvus'.ljust(10)}"
                + "| "
                + f"col_list 中缺少: {', '.join(missing_cols)}"
            )

        # 检查是否存在指定的 col
        for col in milvus_config["col_list"]:
            col_name = milvus_config["col_list"][col]

            # 查询是否已有 collection
            if not milvus_client.has_collection(col_name):
                logger.info(
                    f"{'Milvus'.ljust(10)}"
                    + "| "
                    + f"Col '{col_name}' 不存在，正在创建"
                )
                create_collection(milvus_client, col_name)
            else:
                logger.info(
                    f"{'Milvus'.ljust(10)}"
                    + "| "
                    + f"Col '{col_name}' 已存在，正在连接"
                )

        milvus_client.close()

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"连接 client 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"连接 client 错误: {str(e)}"
        )

    # 异步连接
    try:

        # 连接 async_client
        async_client = AsyncMilvusClient(uri=uri, db_name=database)
        logger.info(f"{'Milvus'.ljust(10)}" + "| " + f"Async Client 连接成功")

        return async_client

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(
            f"{'Milvus'.ljust(10)}" + "| " + f"连接 async client 错误: {str(e)}"
        )
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"连接 async client 错误: {str(e)}"
        )


def create_collection(milvus_client, col_name: str):
    try:
        if "biz_relation" in col_name:
            schema = biz_schema(milvus_client)
            index_params = biz_index(milvus_client)
        elif "table_info" in col_name:
            schema = table_schema(milvus_client)
            index_params = table_index(milvus_client)
        elif "column_info" in col_name:
            schema = column_schema(milvus_client)
            index_params = column_index(milvus_client)
        elif "few_shot" in col_name:
            schema = few_shot_schema(milvus_client)
            index_params = few_shot_index(milvus_client)
        else:
            logger.error(
                f"{'Milvus'.ljust(10)}" + "| " + f"错误的 col_name: {col_name}"
            )
            raise RuntimeError(
                f"{'Milvus'.ljust(10)}" + "| " + f"错误的 col_name: {col_name}"
            )

        # 设置环境

        milvus_client.create_collection(
            collection_name=col_name,
            schema=schema,
            index_params=index_params,
            consistency_level="Strong",
        )

        if milvus_client.has_collection(col_name):
            logger.info(f"{'Milvus'.ljust(10)}" + "| " + f"Col '{col_name}' 创建成功")
        else:
            logger.error(f"{'Milvus'.ljust(10)}" + "| " + f"Col '{col_name}' 创建失败")
            raise RuntimeError(
                f"{'Milvus'.ljust(10)}" + "| " + f"Col '{col_name}' 创建失败"
            )

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 col 错误: {str(e)}")
        raise RuntimeError(f"{'Milvus'.ljust(10)}" + "| " + f"创建 col 错误: {str(e)}")

    try:
        milvus_client.load_collection(col_name)
        logger.info(
            f"{'Milvus'.ljust(10)}"
            + "| "
            + f"load state: {milvus_client.get_load_state(col_name)}"
        )
    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"加载 col 错误: {str(e)}")
        raise RuntimeError(f"{'Milvus'.ljust(10)}" + "| " + f"加载 col 错误: {str(e)}")


def biz_schema(milvus_client):
    try:
        schema = milvus_client.create_schema(auto_id=False, description="业务关联表")

        schema.add_field("biz_id", DataType.VARCHAR, max_length=1024, is_primary=True)
        schema.add_field(
            "user_id", DataType.VARCHAR, max_length=2048, description="用户ID"
        )
        schema.add_field(
            "data_id", DataType.VARCHAR, max_length=2048, description="数据源ID"
        )

        analyzer_params = {
            "type": "chinese",  # Uses the chinese built-in analyzer
        }
        schema.add_field(
            "ask_text",
            DataType.VARCHAR,
            max_length=32678,
            description="业务问题",
            analyzer_params=analyzer_params,
            enable_analyzer=True,
            enable_match=True,
        )

        schema.add_field(
            "desc_text",
            DataType.VARCHAR,
            max_length=32678,
            description="业务文本",
            analyzer_params=analyzer_params,
            enable_analyzer=True,
            enable_match=True,
        )

        schema.add_field(
            "desc_sparse_vector",
            DataType.SPARSE_FLOAT_VECTOR,
            description="描述的稀疏向量",
        )
        schema.add_field(
            "desc_dense_vector",
            DataType.FLOAT_VECTOR,
            dim=1024,
            description="描述的稠密向量",
        )

        desc_functions = Function(
            name="desc_bm25",
            function_type=FunctionType.BM25,
            input_field_names=["desc_text"],
            output_field_names="desc_sparse_vector",
        )
        schema.add_function(desc_functions)

        schema.add_field(
            "ask_sparse_vector",
            DataType.SPARSE_FLOAT_VECTOR,
            description="问题的稀疏向量",
        )
        schema.add_field(
            "ask_dense_vector",
            DataType.FLOAT_VECTOR,
            dim=1024,
            description="问题的稠密向量",
        )

        ask_functions = Function(
            name="ask_bm25",
            function_type=FunctionType.BM25,
            input_field_names=["ask_text"],
            output_field_names="ask_sparse_vector",
        )
        schema.add_function(ask_functions)

        return schema

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}"
        )


def biz_index(milvus_client):
    try:
        index_params = milvus_client.prepare_index_params()

        index_params.add_index(field_name="biz_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="user_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="data_id", index_type="AUTOINDEX")

        index_params.add_index(
            field_name="desc_sparse_vector",
            index_type="SPARSE_INVERTED_INDEX",
            metric_type="BM25",
        )
        index_params.add_index(
            field_name="desc_dense_vector", index_type="AUTOINDEX", metric_type="IP"
        )

        index_params.add_index(
            field_name="ask_sparse_vector",
            index_type="SPARSE_INVERTED_INDEX",
            metric_type="BM25",
        )
        index_params.add_index(
            field_name="ask_dense_vector", index_type="AUTOINDEX", metric_type="IP"
        )

        return index_params

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}"
        )


def table_schema(milvus_client):
    try:
        schema = milvus_client.create_schema(
            auto_id=False,
            description="数据表语义层",
        )

        schema.add_field(
            "table_id",
            datatype=DataType.VARCHAR,
            is_primary=True,
            max_length=8192,
        )

        # ID
        schema.add_field(
            "user_id", DataType.VARCHAR, max_length=2048, description="用户ID"
        )
        schema.add_field(
            "data_id", DataType.VARCHAR, max_length=2048, description="数据源ID"
        )
        schema.add_field(
            "table_name", DataType.VARCHAR, max_length=2048, description="表名"
        )

        # 混合文本
        analyzer_params = {
            "type": "chinese",  # Uses the chinese built-in analyzer
        }
        schema.add_field(
            "text",
            DataType.VARCHAR,
            max_length=32678,
            description="混合文本",
            analyzer_params=analyzer_params,
            enable_analyzer=True,
            enable_match=True,
        )

        # 向量
        schema.add_field(
            "sparse_vector",
            DataType.SPARSE_FLOAT_VECTOR,
            description="稀疏向量",
        )
        schema.add_field(
            "dense_vector",
            DataType.FLOAT_VECTOR,
            dim=1024,
            description="稠密向量",
        )

        functions = Function(
            name="bm25",
            function_type=FunctionType.BM25,
            input_field_names=["text"],
            output_field_names="sparse_vector",
        )
        schema.add_function(functions)

        return schema

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}"
        )


def table_index(milvus_client):
    try:
        index_params = milvus_client.prepare_index_params()

        index_params.add_index(field_name="table_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="user_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="data_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="table_name", index_type="AUTOINDEX")

        index_params.add_index(
            field_name="sparse_vector",
            index_type="SPARSE_INVERTED_INDEX",
            metric_type="BM25",
        )
        index_params.add_index(
            field_name="dense_vector", index_type="AUTOINDEX", metric_type="IP"
        )

        return index_params

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}"
        )


def column_schema(milvus_client):
    try:
        schema = milvus_client.create_schema(
            auto_id=False,
            description="数据列语义层",
        )

        schema.add_field(
            "column_id",
            datatype=DataType.VARCHAR,
            is_primary=True,
            max_length=8192,
        )

        # ID
        schema.add_field(
            "user_id", DataType.VARCHAR, max_length=2048, description="用户ID"
        )
        schema.add_field(
            "data_id", DataType.VARCHAR, max_length=2048, description="数据源ID"
        )
        schema.add_field(
            "table_name", DataType.VARCHAR, max_length=2048, description="表名"
        )
        schema.add_field(
            "column_name", DataType.VARCHAR, max_length=2048, description="列名"
        )

        analyzer_params = {
            "type": "chinese",  # Uses the chinese built-in analyzer
        }

        schema.add_field(
            "text",
            DataType.VARCHAR,
            max_length=32678,
            description="混合文本",
            analyzer_params=analyzer_params,
            enable_analyzer=True,
            enable_match=True,
        )

        schema.add_field(
            "sparse_vector",
            DataType.SPARSE_FLOAT_VECTOR,
            description="稀疏向量",
        )
        schema.add_field(
            "dense_vector",
            DataType.FLOAT_VECTOR,
            dim=1024,
            description="稠密向量",
        )

        functions = Function(
            name="bm25",
            function_type=FunctionType.BM25,
            input_field_names=["text"],
            output_field_names="sparse_vector",
        )
        schema.add_function(functions)

        return schema

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}"
        )


def column_index(milvus_client):
    try:
        index_params = milvus_client.prepare_index_params()

        index_params.add_index(field_name="column_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="user_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="data_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="table_name", index_type="AUTOINDEX")
        index_params.add_index(field_name="column_name", index_type="AUTOINDEX")

        index_params.add_index(
            field_name="sparse_vector",
            index_type="SPARSE_INVERTED_INDEX",
            metric_type="BM25",
        )
        index_params.add_index(
            field_name="dense_vector", index_type="AUTOINDEX", metric_type="IP"
        )

        return index_params

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}"
        )


def few_shot_schema(milvus_client):
    try:
        schema = milvus_client.create_schema(
            auto_id=False,
            description="问答范例库",
        )

        schema.add_field(
            "few_shot_id",
            datatype=DataType.VARCHAR,
            is_primary=True,
            max_length=1024,
        )

        # ID
        schema.add_field(
            "user_id", DataType.VARCHAR, max_length=2048, description="用户ID"
        )
        schema.add_field(
            "data_ids",
            DataType.ARRAY,
            element_type=DataType.VARCHAR,
            max_capacity=2048,
            max_length=512,
            description="数据源ID",
        )
        schema.add_field(
            "example_type", DataType.VARCHAR, max_length=2048, description="类型"
        )

        zh_analyzer_params = {
            "type": "chinese",  # Uses the chinese built-in analyzer
        }

        # 语义
        schema.add_field(
            "example",
            DataType.VARCHAR,
            max_length=32678,
            description="例子",
            analyzer_params=zh_analyzer_params,
            enable_analyzer=True,
            enable_match=True,
        )

        schema.add_field(
            "user_ask",
            DataType.VARCHAR,
            max_length=32678,
            description="用户问题",
            analyzer_params=zh_analyzer_params,
            enable_analyzer=True,
            enable_match=True,
        )

        schema.add_field(
            "example_sparse_vector",
            DataType.SPARSE_FLOAT_VECTOR,
            description="example 稀疏向量",
        )
        schema.add_field(
            "example_dense_vector",
            DataType.FLOAT_VECTOR,
            dim=1024,
            description="example 稠密向量",
        )

        example_functions = Function(
            name="example_bm25",
            function_type=FunctionType.BM25,
            input_field_names=["example"],
            output_field_names="example_sparse_vector",
        )
        schema.add_function(example_functions)

        schema.add_field(
            "ask_sparse_vector",
            DataType.SPARSE_FLOAT_VECTOR,
            description="问题稀疏向量",
        )
        schema.add_field(
            "ask_dense_vector",
            DataType.FLOAT_VECTOR,
            dim=1024,
            description="问题稠密向量",
        )

        ask_functions = Function(
            name="ask_bm25",
            function_type=FunctionType.BM25,
            input_field_names=["user_ask"],
            output_field_names="ask_sparse_vector",
        )
        schema.add_function(ask_functions)

        return schema

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}"
        )


def few_shot_index(milvus_client):
    try:
        index_params = milvus_client.prepare_index_params()

        index_params.add_index(field_name="few_shot_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="user_id", index_type="AUTOINDEX")
        index_params.add_index(field_name="data_ids", index_type="AUTOINDEX")
        index_params.add_index(field_name="example_type", index_type="AUTOINDEX")

        index_params.add_index(
            field_name="example_sparse_vector",
            index_type="SPARSE_INVERTED_INDEX",
            metric_type="BM25",
        )
        index_params.add_index(
            field_name="example_dense_vector", index_type="AUTOINDEX", metric_type="IP"
        )
        index_params.add_index(
            field_name="ask_sparse_vector",
            index_type="SPARSE_INVERTED_INDEX",
            metric_type="BM25",
        )
        index_params.add_index(
            field_name="ask_dense_vector", index_type="AUTOINDEX", metric_type="IP"
        )

        return index_params

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}")
        raise RuntimeError(
            f"{'Milvus'.ljust(10)}" + "| " + f"创建 schema 错误: {str(e)}"
        )
