import os
import sys

import aiomysql

from src.common.utils.logger import logger
from src.common.utils.config import config_data, python_config


async def load_mysql_client():
    try:
        mysql_config = config_data["mysql"]
        
        host = mysql_config["host"]
        port = mysql_config["port"]
        user = mysql_config["user"]
        password = mysql_config["password"]
        db_name = mysql_config["db_name"]
        
        logger.info(f"{'Mysql'.ljust(10)}" + "| " + f"正在连接: '{host}:{port}/{db_name}'")
        
        mysql_pool = await aiomysql.create_pool(
            host=host,
            port=port,
            user=user,
            password=password,
            db=db_name,
            cursorclass=aiomysql.DictCursor,
            minsize=1,  # 最小连接数
            maxsize=20, # 最大连接数，允许更多并发连接
        )
    
        return mysql_pool
    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Mysql'.ljust(10)}" + "| " + f"加载 pool 错误: {str(e)}")
        raise RuntimeError(f"{'Mysql'.ljust(10)}" + "| " + f"加载 pool 错误: {str(e)}")
