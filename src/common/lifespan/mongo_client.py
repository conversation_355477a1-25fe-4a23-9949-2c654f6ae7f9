import os
import sys
from fastapi import HTTPException

from motor.motor_asyncio import AsyncIOMotorClient

from src.common.utils.logger import logger
from src.common.utils.config import config_data, python_config


async def load_mongo_client():
    try:
        mongo_config = config_data["mongo"]

        # 设置 MongoDB URI
        ip = mongo_config.get("ip", None)
        port = mongo_config.get("port", None)
        user = mongo_config.get("user", None)
        password = mongo_config.get("password", None)

        if ip and port:
            if user and password:
                uri = f"mongodb://{user}:{password}@{ip}:{port}"
            else:
                uri = f"mongodb://{ip}:{port}"
        else:
            logger.error(f"{'Mongo'.ljust(10)}" + "| " + "ip 与 port 不全")
            raise RuntimeError(
                f"{'Mongo'.ljust(10)}" + "| " + "Please check ip and port"
            )
        
        

        # 返回连接 MongoDB 的客户端实例，并设置超时参数
        mongo_client = AsyncIOMotorClient(uri)

        if python_config == "dev":
            mongo_db_name = mongo_config["db_name"] + "_dev"
        elif python_config == "test":
            mongo_db_name = mongo_config["db_name"] + "_test"
        else:
            mongo_db_name = mongo_config["db_name"] + "_prod"

        logger.info(f"{'Mongo'.ljust(10)}" + "| " + f"正在连接: '{uri}/{mongo_db_name}'")

        return mongo_client, mongo_db_name
    
    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Mongo'.ljust(10)}" + "| " + f"连接 client 错误: {str(e)}")
        raise RuntimeError(
            f"{'Mongo'.ljust(10)}" + "| " + f"连接 client 错误: {str(e)}"
        )
