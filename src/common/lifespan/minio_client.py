import io
import os
import sys
import json

from minio import Minio
from minio.error import S3Error

from src.common.utils.logger import logger
from src.common.utils.config import config_data, python_config


async def load_minio_client():
    try:
        minio_config = config_data["minio"]
        endpoint = minio_config["endpoint"]
        bucket_name = minio_config["bucket_name"]
        access_key = minio_config["access_key"]
        secret_key = minio_config["secret_key"]

        if python_config == "dev":
            bucket_name += "-dev"
        elif python_config == "test":
            bucket_name += "-test"
        else:
            bucket_name += "-prod"

        logger.info(
            f"{'Minio'.ljust(10)}" + "| " + f"正在连接: '{endpoint}/{bucket_name}'"
        )

        minio_client = Minio(
            endpoint=endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=False,
        )
        if not minio_client.bucket_exists(bucket_name):
            logger.info(
                f"{'Minio'.ljust(10)}"
                + "| "
                + f"Bucket '{bucket_name}' 不存在，正在创建"
            )
            create_bucket(minio_client, bucket_name)
        else:
            logger.info(
                f"{'Minio'.ljust(10)}"
                + "| "
                + f"Bucket '{bucket_name}' 已存在"
            )
        return minio_client

    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Minio'.ljust(10)}" + "| " + f"发生错误: {str(e)}")
        raise RuntimeError(f"{'Minio'.ljust(10)}" + "| " + f"发生错误: {str(e)}")


def create_bucket(minio_client, bucket_name: str):
    try:
        minio_client.make_bucket(bucket_name)

        if minio_client.bucket_exists(bucket_name):
            logger.info(
                f"{'Minio'.ljust(10)}" + "| " + f"bucket '{bucket_name}' 创建成功"
            )
        else:
            logger.error(
                f"{'Minio'.ljust(10)}" + "| " + f"bucket '{bucket_name}' 创建失败"
            )
            raise RuntimeError(
                f"{'Minio'.ljust(10)}" + "| " + f"bucket '{bucket_name}' 创建失败"
            )

    except RuntimeError as e:
        raise e
    except S3Error as e:
        logger.exception(f"{'Minio'.ljust(10)}" + "| " + f"创建 bucket 错误: {str(e)}")
        raise RuntimeError(
            f"{'Minio'.ljust(10)}" + "| " + f"创建 bucket 错误: {str(e)}"
        )

    try:
        policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {"AWS": "*"},
                    "Action": ["s3:GetBucketLocation", "s3:ListBucket"],
                    "Resource": f"arn:aws:s3:::{bucket_name}",
                },
                {
                    "Effect": "Allow",
                    "Principal": {"AWS": "*"},
                    "Action": "s3:GetObject",
                    "Resource": f"arn:aws:s3:::{bucket_name}/*",
                },
            ],
        }
        minio_client.set_bucket_policy(bucket_name, json.dumps(policy))
        logger.info(f"{'Minio'.ljust(10)}" + "| " + f"修改 policy 成功")

    except RuntimeError as e:
        raise e
    except S3Error as e:
        logger.exception(f"{'Minio'.ljust(10)}" + "| " + f"修改 policy 错误: {str(e)}")
        raise RuntimeError(
            f"{'Minio'.ljust(10)}" + "| " + f"修改 policy 错误: {str(e)}"
        )
