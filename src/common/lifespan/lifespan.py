import jieba

from src.common.utils.logger import logger

from src.common.lifespan.mongo_client import load_mongo_client
from src.common.lifespan.minio_client import load_minio_client
from src.common.lifespan.milvus_client import load_milvus_client
from src.common.lifespan.mysql_client import load_mysql_client
from src.common.lifespan.redis_client import load_redis_client

from src.common.lifespan.db_client import db_client
from src.common.utils.config import python_config, config_data

import sys
import os

cmd_path = os.getcwd().split("/src")[0]


async def lifespan_startup():
    try:
        logger.info(f"{'Lifespan'.ljust(10)}" + "| " + "start")

        logger.info(f"{'ENV'.ljust(10)}" + "| " + f"PYTHON_ENV: {python_config}")
        logger.info(f"{'CONFIG'.ljust(10)}" + "| " + f"CONFIG_DATA: {config_data}")
        
        
        ### 初始化 Mongo ###
        mongo_client, mongo_db_name = await load_mongo_client()

        db_client["mongo_col"] = mongo_client[mongo_db_name]
        logger.info(f"{'Mongo'.ljust(10)}" + "| " + "loaded")

        ### 初始化 Minio ###

        minio_client = await load_minio_client()

        db_client["minio_client"] = minio_client
        logger.info(f"{'Minio'.ljust(10)}" + "| " + "loaded")

        ### 初始化 Milvus ###

        milvus_client = await load_milvus_client()

        db_client["milvus_client"] = milvus_client
        logger.info(f"{'Milvus'.ljust(10)}" + "| " + "loaded")

        ### 初始化 Mysql ###

        mysql_pool = await load_mysql_client()

        db_client["mysql_pool"] = mysql_pool
        logger.info(f"{'Mysql'.ljust(10)}" + "| " + "loaded")

        ### 初始化 Redis ###

        redis_client = await load_redis_client()

        db_client["redis_client"] = redis_client
        logger.info(f"{'Redis'.ljust(10)}" + "| " + "loaded")
        
        ### 全部加载完毕
        logger.info(f"{'Lifespan'.ljust(10)}" + "| " + "over")

    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise RuntimeError(f"发生未知错误: {str(e)}")


async def lifespan_shutdown():
    try:
        logger.info(f"{'Lifespan'.ljust(10)}" + "| " + "end")

        ### 结束 Mongo ###
        if "mongo_col" in db_client:
            db_client["mongo_col"].client.close()
            logger.info(f"{'Mongo'.ljust(10)}" + "| " + "closed")

        ### 结束 Minio ###
        if "minio_client" in db_client:
            # Minio 没有关闭函数，但可以做一些资源清理
            logger.info(f"{'Minio'.ljust(10)}" + "| " + "no close method")

        ### 结束 Milvus ###
        if "milvus_client" in db_client:
            await db_client["milvus_client"].close()
            logger.info(f"{'Milvus'.ljust(10)}" + "| " + "close")

        ### 结束 Mysql ###
        if "mysql_pool" in db_client:
            db_client["mysql_pool"].close()
            logger.info(f"{'Mysql'.ljust(10)}" + "| " + "close")

        ### 结束 Redis ###
        if "redis_client" in db_client:
            db_client["redis_client"].close()
            logger.info(f"{'Redis'.ljust(10)}" + "| " + "close")

        ### 结束 db_client ###
        db_client.clear()
        logger.info(f"{'db_client'.ljust(10)}" + "| " + "clear")

    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise RuntimeError(f"发生未知错误: {str(e)}")
