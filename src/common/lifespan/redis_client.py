import os
import sys

from redis import Redis

from src.common.utils.logger import logger
from src.common.utils.config import config_data, python_config


async def load_redis_client():
    """redis"""
    try:
        redis_config = config_data["redis"]
        
        host=redis_config["host"]
        port=redis_config["port"]
        password=redis_config["password"]
        db=redis_config["db"]
        
        logger.info(f"{'Redis'.ljust(10)}" + "| " + f"正在连接: '{host}:{port}/{db}'")
        
        redis_client = Redis(
            host=host,
            port=port,
            password=password,
            db=db,
            decode_responses=True,
        )

        return redis_client
    except RuntimeError as e:
        raise e
    except Exception as e:
        logger.exception(f"{'Redis'.ljust(10)}" + "| " + f"加载错误: {str(e)}")
        raise RuntimeError(f"{'Redis'.ljust(10)}" + "| " + f"加载错误: {str(e)}")
