import asyncio
from typing import List, Optional
from .default_prompts import (
    DEFAULT_IS_DATE_TIME_FIELD_PROMPT,
    DEFAULT_NUMBER_CATEGORY_FIELD_PROMPT,
    DEFAULT_STRING_CATEGORY_FIELD_PROMPT,
    DEFAULT_UNKNOWN_FIELD_PROMPT,
    DEFAULT_COLUMN_DESC_GEN_CHINESE_PROMPT,
    DEFAULT_COLUMN_DESC_GEN_ENGLISH_PROMPT,
    DEFAULT_TABLE_DESC_GEN_CHINESE_PROMPT,
    DEFAULT_TABLE_DESC_GEN_ENGLISH_PROMPT,
    DEFAULT_UNDERSTAND_FIELDS_BY_CATEGORY_PROMPT,
    DEFAULT_UNDERSTAND_DATABASE_PROMPT,
    DEFAULT_GET_DOMAIN_KNOWLEDGE_PROMPT,
    DEFAULT_DATE_TIME_MIN_GRAN_PROMPT,
)
from .call_llm import call_llm
from .type_engine import TypeEngine

from src.common.utils.extract_json import ej_agent
from .logger_util import *


def clean_string(text: str) -> str:
    cleaned_text = (
        text.replace('"', "").replace("“", "").replace("”", "").replace("**", "")
    )

    # 去除中文冒号
    if cleaned_text.startswith(":") or cleaned_text.startswith("："):
        cleaned_text = cleaned_text[1:].strip()

    # 如果以句号结尾，去掉句号
    if cleaned_text.endswith("。"):
        cleaned_text = cleaned_text[:-1].strip()

    # 只保留第一行
    # return cleaned_text.split("\n")[0].strip()
    return cleaned_text


async def understand_date_time_min_gran(field_info_str: str = "", agent=None):
    """
    Determine the minimum granularity of date and time fields.
    """
    res = await call_llm(
        prompt=DEFAULT_DATE_TIME_MIN_GRAN_PROMPT,
        agent=agent,
        field_info_str=field_info_str,
    )
    return res.upper().strip()


async def understand_database(db_mschema: str = "", agent=None):
    """
    Database understanding.
    """

    db_info1, db_info2 = await asyncio.gather(
        call_llm(DEFAULT_UNDERSTAND_DATABASE_PROMPT, agent, db_mschema=db_mschema),
        call_llm(DEFAULT_GET_DOMAIN_KNOWLEDGE_PROMPT, agent, db_mschema=db_mschema),
    )

    data_json, sccess = await ej_agent.get_json(db_info1)
    data_rename = ""
    data_desc = ""
    if sccess:
        data_rename = clean_string(data_json.get("chinese_name", ""))
        data_desc = clean_string(data_json.get("chinese_desc", ""))

    return data_rename, data_desc, db_info2


async def generate_column_desc(
    field_name: str,
    field_info_str: str = "",
    table_mschema: str = "",
    agent=None,
    sql: Optional[str] = None,
    sql_res: Optional[str] = None,
    supp_info: Optional[str] = None,
    language: Optional[str] = "CN",
    reference_info: Optional[str] = None,
):

    if language == "CN":
        prompt = DEFAULT_COLUMN_DESC_GEN_CHINESE_PROMPT
    elif language == "EN":
        prompt = DEFAULT_COLUMN_DESC_GEN_ENGLISH_PROMPT
    else:
        raise NotImplementedError(f"Unsupported language {language}.")

    column_resp = await call_llm(
        prompt,
        agent,
        table_mschema=table_mschema,
        sql=sql,
        sql_res=sql_res,
        field_name=field_name,
        field_info_str=field_info_str,
        supp_info=supp_info,
        reference_info=reference_info,
    )
    column_json, sccess = await ej_agent.get_json(column_resp)
    column_rename = ""
    column_desc = ""

    if sccess:
        if language == "CN":
            column_desc = clean_string(column_json.get("chinese_desc", ""))
            column_rename = clean_string(column_json.get("chinese_name", ""))
        elif language == "EN":
            column_desc = clean_string(column_json.get("english_desc", ""))

    return column_rename, column_desc


async def generate_table_desc(
    table_name: str,
    table_mschema: str = "",
    agent=None,
    sql: Optional[str] = None,
    sql_res: Optional[str] = None,
    language: Optional[str] = "CN",
):
    if language == "CN":
        prompt = DEFAULT_TABLE_DESC_GEN_CHINESE_PROMPT
    elif language == "EN":
        prompt = DEFAULT_TABLE_DESC_GEN_ENGLISH_PROMPT
    else:
        raise NotImplementedError(f"Unsupported language {language}.")

    table_json = await call_llm(
        prompt,
        agent,
        table_name=table_name,
        table_mschema=table_mschema,
        sql=sql,
        sql_res=sql_res,
    )

    table_json = table_json.strip()
    table_json, sccess = await ej_agent.get_json(table_json)

    table_rename = ""
    table_desc = ""
    if sccess:
        if language == "CN":
            table_desc = clean_string(table_json.get("chinese_desc", ""))
            table_rename = clean_string(table_json.get("chinese_name", ""))
        elif language == "EN":
            table_desc = clean_string(table_json.get("english_desc", ""))

    return table_rename, table_desc


async def understand_fields_by_category(
    db_info: str,
    table_name: str,
    table_mschema: str = "",
    agent=None,
    sql: Optional[str] = None,
    sql_res: Optional[str] = None,
    fields: Optional[List] = None,
    dim_or_meas: str = "",
):
    fields = fields or []
    text = await call_llm(
        DEFAULT_UNDERSTAND_FIELDS_BY_CATEGORY_PROMPT,
        agent,
        db_info=db_info,
        table_name=table_name,
        table_mschema=table_mschema,
        sql=sql,
        sql_res=sql_res,
        fields="、".join([f"{field}" for field in fields]),
        category=dim_or_meas,
    )
    return text


async def field_category(
    field_type_cate: str,
    type_engine: TypeEngine,
    agent=None,
    field_info_str: str = "",
):
    """
    区分字段类别是维度还是度量。
    is_unique_pk_cons: 是否为主键、外键或者唯一键（包含与其他字段共同构成联合的主键）
    """
    code_res = {
        "category": type_engine.field_category_code_label,
        "dim_or_meas": type_engine.dimension_label,
    }
    enum_res = {
        "category": type_engine.field_category_enum_label,
        "dim_or_meas": type_engine.dimension_label,
    }
    date_res = {
        "category": type_engine.field_category_date_label,
        "dim_or_meas": type_engine.dimension_label,
    }
    measure_res = {
        "category": type_engine.field_category_measure_label,
        "dim_or_meas": type_engine.measure_label,
    }
    text_res = {
        "category": type_engine.field_category_text_label,
        "dim_or_meas": type_engine.dimension_label,
    }

    if field_type_cate == type_engine.field_type_date_label:
        return date_res
    elif field_type_cate == type_engine.field_type_bool_label:
        return enum_res
    else:
        # 判断是否是时间日期类字段
        kwargs = {"agent": agent, "field_info_str": field_info_str}
        is_date_time = await call_llm(DEFAULT_IS_DATE_TIME_FIELD_PROMPT, **kwargs)
        is_date_time = is_date_time.strip()
        if is_date_time == "是":
            return date_res
        else:
            if field_type_cate == type_engine.field_type_string_label:
                # 非时间日期类字符串，判断是code、text还是enum
                res = await call_llm(DEFAULT_STRING_CATEGORY_FIELD_PROMPT, **kwargs)
                res = res.strip().lower()
                if res == "enum":
                    return enum_res
                elif res == "text":
                    return text_res
                else:
                    return code_res
            elif field_type_cate == type_engine.field_type_number_label:
                # 非时间日期类的数值，判断是code、measure还是enum
                res = await call_llm(DEFAULT_NUMBER_CATEGORY_FIELD_PROMPT, **kwargs)
                res = res.strip().lower()
                if res == "enum":
                    return enum_res
                elif res == "measure":
                    return measure_res
                else:
                    return code_res
            else:
                res = await call_llm(DEFAULT_UNKNOWN_FIELD_PROMPT, **kwargs)
                res = res.strip().lower()
                if res == "enum":
                    return enum_res
                elif res == "measure":
                    return measure_res
                elif res == "text":
                    return text_res
                else:
                    return code_res
