from pyhive.sqlalchemy_hive import HiveDialect
from pyhive.sqlalchemy_hive import _type_map
from sqlalchemy import types
from sqlalchemy import util

import copy
import re

_type_map = copy.deepcopy(_type_map)
_type_map["varchar2"] = types.String

# 继承自 pyhive 的 HiveDialect
class ArgodbDialect(HiveDialect):
    def get_columns(self, connection, table_name, schema=None, **kw):
        rows = self._get_table_columns(connection, table_name, schema)
        # Strip whitespace
        rows = [[col.strip() if col else None for col in row] for row in rows]
        # Filter out empty rows and comment
        rows = [row for row in rows if row[0] and row[0] != "# col_name"]
        result = []
        for col_name, col_type, default, nullable, is_identity, _comment in rows:
            if col_name == "# Partition Information":
                break
            # Take out the more detailed type information
            # e.g. 'map<int,int>' -> 'map'
            #      'decimal(10,1)' -> decimal
            col_type = re.search(r"^\w+", col_type)
            col_type = col_type.group(0)
            try:
                if col_type:
                    coltype = _type_map[col_type]
                else:
                    coltype = types.NullType
            except KeyError:
                util.warn(
                    "Did not recognize type '%s' of column '%s'" % (col_type, col_name)
                )
                coltype = types.NullType

            result.append(
                {
                    "name": col_name,
                    "type": coltype,
                    "nullable": nullable,
                    "default": default,
                    "autoincrement": is_identity is not None,
                    "comment": _comment,
                }
            )
        return result

    def get_indexes(self, connection, table_name, schema=None, **kw):
        rows = self._get_table_columns(connection, table_name, schema)
        # Strip whitespace
        rows = [[col.strip() if col else None for col in row] for row in rows]
        # Filter out empty rows and comment
        rows = [row for row in rows if row[0] and row[0] != "# col_name"]
        for i, (col_name, col_type, default, nullable, _, _comment) in enumerate(rows):
            if col_name == "# Partition Information":
                break
        # Handle partition columns
        col_names = []
        for col_name, col_type, default, nullable, _, _comment in rows[i + 1 :]:
            col_names.append(col_name)
        if col_names:
            return [
                {
                    "name": "partition",
                    "column_names": col_names,
                    "unique": False,
                }
            ]
        else:
            return []

    def get_foreign_keys(self, connection, table_name, schema=None, **kw):
        # Hive has no support for foreign keys.
        return []

    def get_pk_constraint(self, connection, table_name, schema=None, **kw):
        # Hive has no support for primary keys.
        return {"constrained_columns": [], "name": None}

    def get_unique_constraints(self, connection, table_name, schema=None, **kw):
        # Hive has no support for unique.
        return []


# 注册自定义 dialect
from sqlalchemy.dialects import registry

registry.register("argodb", __name__, "ArgodbDialect")
