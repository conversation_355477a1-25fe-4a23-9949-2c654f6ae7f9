from llama_index.core.prompts import BasePromptTemplate
import time


async def call_llm(
    prompt: BasePromptTemplate, agent=None, max_try=3, sleep=1, **prompt_args
) -> str:
    prompt = prompt.format(**prompt_args)
    messages = [{"role": "user", "content": prompt}]

    for try_idx in range(max_try):
        try:
            res = await agent._run(messages, stream=False)
            res_msg = res.choices[0].message.model_dump()
            return res_msg["content"]
        except:
            time.sleep(sleep)
    return ""
