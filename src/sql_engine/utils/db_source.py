import time
import asyncio
import pandas as pd
from src.aagent.agent.aagent import AAgent
from src.common.utils.config import config_data

from typing import Any, Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, TimeoutError

from llama_index.core import SQLDatabase
from sqlalchemy import MetaData, Table, select, text
from sqlalchemy.engine import Engine

from .db_config import DBConfig
from .db_mschema import MSchema
from .logger_util import *
from .db_util import examples_to_str, preprocess_sql_query, BackStatusEnum
from .type_engine import TypeEngine
from .components import (
    field_category,
    generate_column_desc,
    generate_table_desc,
    understand_fields_by_category,
    understand_database,
    understand_date_time_min_gran,
)


class HITLSQLDatabase(SQLDatabase):
    def __init__(
        self,
        engine: Engine,
        db_config: DBConfig,
        schema: Optional[str] = None,
        metadata: Optional[MetaData] = None,
        ignore_tables: Optional[List[str]] = None,
        include_tables: Optional[List[str]] = None,
        sample_rows_in_table_info: int = 3,
        indexes_in_table_info: bool = False,
        custom_table_info: Optional[dict] = None,
        view_support: bool = False,
        max_string_length: int = 300,
        mschema: Optional[MSchema] = None,
        comment_mode: Optional[str] = None,
    ):
        super().__init__(
            engine,
            schema,
            metadata,
            ignore_tables,
            include_tables,
            sample_rows_in_table_info,
            indexes_in_table_info,
            custom_table_info,
            view_support,
            max_string_length,
        )
        self._db_name = db_config.db_name
        self._db_type = db_config.db_type
        self._dialect = engine.dialect.name

        self._usable_tables = [
            table_name
            for table_name in self._usable_tables
            if self._inspector.has_table(table_name, schema)
        ]

        self._type_engine = TypeEngine()
        assert (
            self._dialect in self._type_engine.supported_dialects
        ), "Unsupported dialect {}.".format(self._dialect)

        self._mschema = mschema
        self.comment_mode = comment_mode or "separate_merge"
        model_config = {
            "model": config_data["llm"]["model"],
            "api_key": config_data["llm"]["api_key"],
            "base_url": config_data["llm"]["base_url"],
        }
        self._agent = AAgent(model_config=model_config)

        semaphore = config_data.get("semaphore", 30)
        self.semaphore = asyncio.Semaphore(semaphore)

        # 共享池用于存储已生成的列注释信息
        self._column_ref_pool = {}
        # 格式: {field_name: {table_name:{'remark': xxx, 'rename': xxx, 'comment': 'xxx'}, ...}}

    @log_runtime
    async def async_init(self, get_examples=True, get_statistics=True):
        # 异步初始化操作
        if self._mschema is None:
            self._mschema = MSchema(
                db_name=self._db_name,
                db_type=self._db_type,
                schema=self._schema,
                type_engine=self._type_engine,
            )
            await self.init_mschema(get_examples, get_statistics)
        else:
            # 如果如果mschema已经存在，则检查是否需要更新

            # 获取 mschema 的表名
            mschema_table_names = self._mschema.tables.keys()

            # 删除的表 和 缺少的表
            tables_to_add = list(set(self.get_table_names()) - set(mschema_table_names))
            tables_to_delete = list(
                set(mschema_table_names) - set(self.get_table_names())
            )

            for table_name in tables_to_delete:
                del self._mschema.tables[table_name]

            tasks = []
            for table_name in tables_to_add:
                tasks.append(self.init_table(table_name, get_examples, get_statistics))
            await asyncio.gather(*tasks)

    @property
    def schema(self) -> str:
        """Return schema"""
        return self._schema

    @property
    def mschema(self) -> MSchema:
        """Return M-Schema"""
        return self._mschema

    @property
    def type_engine(self) -> TypeEngine:
        """Return TypeEngine"""
        return self._type_engine

    @property
    def db_name(self) -> str:
        """Return db_name"""
        return self._db_name

    async def get_pk_constraint(self, table_name: str) -> Dict:
        """获取指定表的主键约束信息"""
        return self._inspector.get_pk_constraint(table_name, self._schema)[
            "constrained_columns"
        ]

    def get_table_names(self):
        """获取全部的表名"""
        return self._usable_tables

    async def get_table_comment(self, table_name: str):
        """获取指定表的注释信息"""
        try:
            return self._inspector.get_table_comment(table_name, self._schema)["text"] or ""
        except:  # sqlite不支持添加注释
            return ""

    async def get_table_comments(self):
        """获取所有表的注释信息"""
        task = []
        for table_name in self.get_table_names():
            task.append(self.get_table_comment(table_name))
        results = await asyncio.gather(*task)

        table_comments = {}
        for table_name, table_comment in zip(self.get_table_names(), results):
            table_comments[table_name] = table_comment

        return table_comments

    async def default_schema_name(self) -> Optional[str]:
        """获取数据库的默认模式名称"""
        return self._inspector.default_schema_name

    async def get_schema_names(self) -> List[str]:
        """获取数据库的模式名称"""
        return self._inspector.get_schema_names()

    async def get_table_options(self, table_name: str) -> Dict[str, Any]:
        """获取指定表的选项信息"""
        return self._inspector.get_table_options(table_name, self._schema)

    async def get_foreign_keys(self, table_name: str):
        """获取指定表的外键信息"""
        return self._inspector.get_foreign_keys(table_name, self._schema)

    async def get_unique_constraints(self, table_name: str):
        """获取指定表的唯一约束信息"""
        return self._inspector.get_unique_constraints(table_name, self._schema)

    async def get_indexes(self, table_name: str):
        """获取指定表的索引信息"""
        return self._inspector.get_indexes(table_name, self._schema)

    async def get_columns(self, table_name: str):
        """获取指定表的字段信息"""
        return self._inspector.get_columns(table_name, self._schema)

    async def fectch_distinct_values(
        self, table_name: str, column_name: str, max_num: int = 5
    ):
        """从指定表中获取指定列的 distinct 值

        Args:
            table_name: 表名
            column_name: 列名
            max_num: 最大数量. Defaults to 5.

        Returns:
            一个包含 distinct 值的列表
        """
        table = Table(table_name, self.metadata_obj, autoload_with=self._engine)
        # 构建 SELECT DISTINCT 查询
        query = select(table.c[column_name]).distinct().limit(max_num)
        values = []
        with self._engine.connect() as connection:
            result = connection.execute(query)
            distinct_values = result.fetchall()
            for value in distinct_values:
                if value[0] is not None and value[0] != "":
                    values.append(value[0])
        return values


    async def get_top_rows(
        self,
        table_name: str,
        top: Optional[int] = 20,
    ) -> Dict[str, List]:
        """获取指定表的列值示例"""
        sql = "select * from {} limit {};".format(
            self.get_protected_table_name(table_name),
            top,
        )
        res = await self.fetch_truncated(sql, None, None)
        data = res["truncated_results"]
        column_names = res["fields"]
        data_with_column_names = [dict(zip(column_names, r)) for r in data]
        if data is not None:
            return column_names, data_with_column_names
        else:
            return [], []


    async def fetch(self, sql_query: str):
        sql_query = preprocess_sql_query(sql_query)

        with self._engine.begin() as connection:
            try:
                cursor = connection.execute(text(sql_query))
                records = cursor.fetchall()
                records = [tuple(row) for row in records]
                return records, True
            except Exception as e:
                records = str(e)
            return records, False

    async def fetch_with_column_name(self, sql_query: str):
        sql_query = preprocess_sql_query(sql_query)

        with self._engine.begin() as connection:
            try:
                cursor = connection.execute(text(sql_query))
                columns = cursor.keys()
                records = cursor.fetchall()
            except Exception as e:
                records = None
                columns = []
            return records, columns

    async def fetch_with_error_info(self, sql_query: str) -> Tuple[List, str]:
        info = ""
        sql_query = preprocess_sql_query(sql_query)
        with self._engine.begin() as connection:
            try:
                cursor = connection.execute(text(sql_query))
                records = cursor.fetchall()
            except Exception as e:
                info = str(e)
                records = None
        return records, info

    async def fetch_truncated(
        self, sql_query: str, max_rows: Optional[int] = None, max_str_len: Optional[int] = 30
    ) -> Dict:
        sql_query = preprocess_sql_query(sql_query)
        with self._engine.begin() as connection:
            try:
                cursor = connection.execute(text(sql_query))
                result = cursor.fetchall()
                truncated_results = []
                if max_rows:
                    result = result[:max_rows]
                if max_str_len:
                    for row in result:
                        truncated_row = tuple(
                            self.truncate_word(column, length=max_str_len) for column in row
                        )
                        truncated_results.append(truncated_row)
                else:
                    truncated_results = result
                return {
                    "truncated_results": truncated_results,
                    "fields": list(cursor.keys()),
                }
            except Exception as e:
                records = str(e)
                return {"truncated_results": records, "fields": []}

    async def fetch_truncated_pd(
        self, sql_query: str, max_rows: Optional[int] = None, max_str_len: int = 30
    ) -> Dict:
        sql_query = preprocess_sql_query(sql_query)
        with self._engine.begin() as connection:
            try:
                cursor = connection.execute(text(sql_query))
                columns = cursor.keys()
                result = cursor.fetchall()

                # 全部数据
                data_frame = pd.DataFrame(result, columns=columns)

                truncated_results = []
                if max_rows:
                    result = result[:max_rows]
                for row in result:
                    truncated_row = tuple(
                        self.truncate_word(column, length=max_str_len) for column in row
                    )
                    truncated_results.append(truncated_row)
                return {
                    "truncated_results": truncated_results,
                    "data_frame": data_frame,
                    "fields": list(cursor.keys()),
                }
            except Exception as e:
                records = str(e)
                return {
                    "truncated_results": records,
                    "data_frame": None,
                    "fields": [],
                }

    @staticmethod
    def trunc_result_to_markdown(sql_res: Dict) -> str:
        """
        数据库查询结果转换成markdown格式
        """
        truncated_results = sql_res.get("truncated_results", [])
        fields = sql_res.get("fields", [])

        if not isinstance(truncated_results, list):
            return str(truncated_results)

        header = "| " + " | ".join(fields) + " |"
        separator = "| " + " | ".join(["---"] * len(fields)) + " |"
        rows = []
        for row in truncated_results:
            rows.append("| " + " | ".join(str(value) for value in row) + " |")
        markdown_table = "\n".join([header, separator] + rows)
        return markdown_table

    async def execute(self, sql_query: str, timeout=10) -> Any:
        sql_query = self.add_semicolon_to_sql(sql_query)

        def run_query():
            with self._engine.begin() as connection:
                cursor = connection.execute(text(sql_query))
                return True

        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(run_query)
            try:
                result = future.result(timeout=timeout)
                return result
            except TimeoutError:
                logger.warning(f"SQL执行超时（{timeout}秒）{sql_query}")
                return None
            except Exception as e:
                logger.warning(f"执行SQL时发生异常: {e}")
                return None

    def get_protected_table_name(self, table_name: str) -> str:
        """
        根据不同的数据库方言，返回适当保护的表名。

        此函数旨在确保在不同数据库中使用时，表名能够被正确地引用，以避免潜在的命名冲突或SQL注入风险。
        它根据数据库方言（dialect）的不同，返回用特定字符包裹的表名。
        """
        if (
            self._dialect == self._type_engine.mysql_dialect
            or self._dialect == self._type_engine.sqlite_dialect
        ):
            return f"`{table_name}`"
        elif self._dialect == self._type_engine.postgres_dialect:
            if self._schema:
                return f'"{self._schema}"."{table_name}"'
            else:
                return f'"{table_name}"'
        else:
            return f"{table_name}"

    def get_protected_field_name(self, field_name: str) -> str:
        """
        根据不同的数据库方言，返回适当保护的字典名。

        此函数旨在确保在不同数据库中使用时，字典名能够被正确地引用，以避免潜在的命名冲突或SQL注入风险。
        它根据数据库方言（dialect）的不同，返回用特定字符包裹的字典名。
        """
        if (
            self._dialect == self._type_engine.mysql_dialect
            or self._dialect == self._type_engine.sqlite_dialect
        ):
            return f"`{field_name}`"
        elif self._dialect == self._type_engine.postgres_dialect:
            return f'"{field_name}"'
        else:
            return f"{field_name}"

    async def init_table_comment(self, table_name):
        table_comment = await self.get_table_comment(table_name)
        table_comment = "" if table_comment is None else table_comment.strip()
        self._mschema.set_table_property(table_name, "comment", table_comment)

    async def init_unique_keys(self, table_name):
        unique_keys = []

        pks = await self.get_pk_constraint(table_name)
        unique_constraints = await self.get_unique_constraints(table_name)
        for u_con in unique_constraints:
            column_names = u_con["column_names"]
            unique_keys.append(column_names)
        self._mschema.tables[table_name]["unique_keys"] = unique_keys

        return pks, unique_keys

    async def init_indexe_keys(self, table_name):
        indexes = await self.get_indexes(table_name)
        indexe_keys = []
        for index in indexes:
            indexe_keys.append(index["column_names"])
        self._mschema.tables[table_name]["indexs"] = indexe_keys

    async def init_foreign_keys(self, table_name):
        fks = await self.get_foreign_keys(table_name)
        # constrained_columns = []
        for fk in fks:
            referred_schema = fk["referred_schema"]
            for c, r in zip(fk["constrained_columns"], fk["referred_columns"]):
                self._mschema.add_foreign_key(
                    table_name, c, referred_schema, fk["referred_table"], r
                )
                # constrained_columns.append(c)

    async def init_field_statistics(
        self, table_name: str, field_name: str, field_type: str = None
    ):
        try:
            if not field_type:
                fields = await self.get_columns(table_name)
                for field in fields:
                    if field["name"] == field_name:
                        field_type = f"{field['type']!s}"
                        break

            info_res = await asyncio.gather(
                self.get_column_unique_count(table_name, field_name),
                self.get_column_count(table_name, field_name),
                self.get_column_agg_value(table_name, field_name, field_type, "max"),
                self.get_column_agg_value(table_name, field_name, field_type, "min"),
                self.get_column_agg_value(table_name, field_name, field_type, "avg"),
                self.get_column_agg_char_length(table_name, field_name, "max"),
                self.get_column_agg_char_length(table_name, field_name, "min"),
            )

            statistics = {
                "unique_num": info_res[0],
                "total_num": info_res[1],
                "max_value": info_res[2],
                "min_value": info_res[3],
                "avg_value": info_res[4],
                "max_len": info_res[5],
                "min_len": info_res[6],
            }
            self._mschema.set_column_property(
                table_name, field_name, "statistics", statistics
            )
            self._mschema.set_column_property(
                table_name,
                field_name,
                "statistics_sync_status",
                BackStatusEnum.SUCCEEDED,
            )
        except Exception as e:
            logger.error(e)
            self._mschema.set_column_property(
                table_name, field_name, "statistics_sync_status", BackStatusEnum.FAILED
            )

    async def init_field_examples(self, table_name, field_name):
        try:
            examples = await self.get_column_value_examples(
                table_name, field_name, max_rows=10, max_str_len=30
            )
            examples = examples_to_str(examples, simple_examples=False)
            if None in examples:
                examples.remove(None)
            if "" in examples:
                examples.remove("")

            self._mschema.set_column_property(
                table_name, field_name, "examples", examples
            )
            self._mschema.set_column_property(
                table_name, field_name, "example_sync_status", BackStatusEnum.SUCCEEDED
            )
        except Exception as e:
            logger.error(e)
            self._mschema.set_column_property(
                table_name, field_name, "example_sync_status", BackStatusEnum.FAILED
            )

    async def init_field(
        self,
        table_name,
        field,
        pks,
        unique_keys,
        get_examples,
        get_statistics,
    ):
        field_type = f"{field['type']!s}"
        field_name = field["name"]

        task = []
        if get_examples:
            task.append(self.init_field_examples(table_name, field_name))
        if get_statistics:
            task.append(self.init_field_statistics(table_name, field_name, field_type))
        e_s_task = asyncio.gather(*task)

        if field_name in pks:
            primary_key = True
            if len(pks) == 1:
                is_unique = True
            else:
                is_unique = False
        else:
            primary_key = False
            if [field_name] in unique_keys:
                is_unique = True
            else:
                is_unique = False

        field_comment = field.get("comment", None)
        field_comment = "" if field_comment is None else field_comment.strip()
        autoincrement = field.get("autoincrement", False)
        default = field.get("default", None)
        if default is not None:
            default = f"{default}"

        self._mschema.add_field(
            table_name=table_name,
            field_name=field_name,
            field_type=field_type,
            primary_key=primary_key,
            nullable=field["nullable"],
            default=default,
            autoincrement=autoincrement,
            unique=is_unique,
            comment=field_comment,
        )

        await e_s_task

    async def init_table(self, table_name, get_examples, get_statistics):
        async with self.semaphore:
            # 初始化 table

            self._mschema.add_table(table_name)
            comment_task = asyncio.gather(
                self.init_table_comment(table_name),
                self.init_indexe_keys(table_name),
                self.init_foreign_keys(table_name),
            )

            fields = await self.get_columns(table_name)
            pks, unique_keys = await self.init_unique_keys(table_name)

            tasks = []
            for field in fields:
                tasks.append(
                    self.init_field(
                        table_name,
                        field,
                        pks,
                        unique_keys,
                        get_examples,
                        get_statistics,
                    )
                )
            await asyncio.gather(*tasks)
            await comment_task

    async def init_mschema(self, get_examples=True, get_statistics=True):
        tasks = []
        for table_name in self.get_table_names():
            tasks.append(self.init_table(table_name, get_examples, get_statistics))
        await asyncio.gather(*tasks)

    async def get_column_count(self, table_name: str, field_name: str) -> int:
        """获取指定表格中指定字段的计数"""
        sql = "select count({}) from {};".format(
            self.get_protected_field_name(field_name),
            self.get_protected_table_name(table_name),
        )
        r, succeed = await self.fetch(sql)
        if succeed and r[0][0] is not None:
            return str(r[0][0])
        else:
            return None

    async def get_column_unique_count(self, table_name: str, field_name: str) -> int:
        """获取指定表中指定字段的唯一值数量"""
        sql = "select count(distinct {}) from {};".format(
            self.get_protected_field_name(field_name),
            self.get_protected_table_name(table_name),
        )
        r, succeed = await self.fetch(sql)
        if succeed and r[0][0] is not None:
            return str(r[0][0])
        else:
            return None

    async def get_column_value_examples(
        self,
        table_name: str,
        field_name: str,
        max_rows: Optional[int] = None,
        max_str_len: int = 30,
    ) -> List:
        """获取指定表和字段中的列值示例"""
        sql = "select distinct {} from {} where {} is not null limit {};".format(
            self.get_protected_field_name(field_name),
            self.get_protected_table_name(table_name),
            self.get_protected_field_name(field_name),
            max_rows,
        )
        res = await self.fetch_truncated(sql, None, max_str_len)
        res = res["truncated_results"]
        if res is not None:
            return [r[0] for r in res]
        else:
            return []

    async def check_column_value_exist(
        self, table_name: str, field_name: str, value_name: str, is_string: bool
    ) -> bool:
        """检查在指定表的指定列中是否存在给定的值"""
        if is_string:
            sql = """select count(*) from {} where {} = '{}';""".format(
                self.get_protected_table_name(table_name),
                self.get_protected_field_name(field_name),
                value_name.replace("'", "''"),
            )
        else:
            sql = "select count(*) from {} where {} = {};".format(
                self.get_protected_table_name(table_name),
                self.get_protected_field_name(field_name),
                value_name,
            )
        r, succeed = await self.fetch(sql)
        if succeed and r[0][0] is not None:
            return r[0][0] > 0
        else:
            return False

    def check_agg_func(self, agg_func: str):
        """检查聚合函数是否有效"""
        assert agg_func.upper() in [
            "MAX",
            "MIN",
            "AVG",
            "SUM",
        ], "Invalid aggregate function {}.".format(agg_func)

    async def get_column_agg_value(
        self, table_name: str, field_name: str, field_type: str, agg_func: str
    ):
        """根据指定的聚合函数计算给定表和字段的聚合值"""

        self.check_agg_func(agg_func)
        type_cate = self._type_engine.field_type_cate(field_type)

        if type_cate not in [
            self._type_engine.field_type_number_label,
            self._type_engine.field_type_date_label,
        ] or (
            type_cate == self._type_engine.field_type_date_label and agg_func == "avg"
        ):
            return None

        sql = "select {}({}) from {} where {} is not null;".format(
            agg_func,
            self.get_protected_field_name(field_name),
            self.get_protected_table_name(table_name),
            self.get_protected_field_name(field_name),
        )

        r, succeed = await self.fetch(sql)
        if succeed and r[0][0] is not None:
            return str(r[0][0])
        else:
            return None

    async def get_column_agg_char_length(
        self, table_name: str, field_name: str, agg_func: str
    ) -> int:
        """计算指定字段的字符长度的聚合值"""
        if self._dialect == self._type_engine.mysql_dialect:
            snip = "{}".format(self.get_protected_field_name(field_name))
        elif self._dialect == self._type_engine.postgres_dialect:
            snip = "{}::TEXT".format(self.get_protected_field_name(field_name))
        elif self._dialect == self._type_engine.sqlite_dialect:
            snip = "CAST({} AS TEXT)".format(self.get_protected_field_name(field_name))
        else:
            snip = "{}".format(self.get_protected_field_name(field_name))

        self.check_agg_func(agg_func)
        if self._dialect == self._type_engine.sqlite_dialect:
            sql = "select {}(length({})) from {} where {} is not null;".format(
                agg_func,
                snip,
                self.get_protected_table_name(table_name),
                self.get_protected_field_name(field_name),
            )
        else:
            sql = "select {}(char_length({})) from {} where {} is not null;".format(
                agg_func,
                snip,
                self.get_protected_table_name(table_name),
                self.get_protected_field_name(field_name),
            )
        r, succeed = await self.fetch(sql)
        if succeed and r[0][0] is not None:
            return str(r[0][0])
        else:
            return None

    def get_all_field_examples_sql(
        self, table_name: str, max_rows: Optional[int] = None
    ):
        """根据给定的表名，生成一个SQL查询语句，用于获取该表中所有不同的行"""
        sql = f"""SELECT DISTINCT * FROM {self.get_protected_table_name(table_name)}"""  # group by {dimension_fields}
        if max_rows is not None and max_rows > 0:
            sql += " LIMIT {};".format(max_rows)
        return sql

    def get_single_field_info_str(self, table_name: str, field_name: str) -> str:
        """某一列的额外相关信息：列名、类型、列描述、是否主键、最大/最小值等"""
        field_info = self._mschema.get_field_info(table_name, field_name)
        field_type = field_info.get("type", "")
        comment = field_info.get("comment", "")
        primary_key = field_info.get("primary_key", False)
        nullable = field_info.get("nullable", True)
        statistics = field_info.get("statistics", {})
        date_min_gran = field_info.get("date_min_gran", None)
        dim_or_meas = field_info.get("dim_or_meas", "")
        date_range = field_info.get("date_range", None)

        unique_num = statistics.get("unique_num", None)
        total_num = statistics.get("total_num", None)
        max_value = statistics.get("max_value", None)
        min_value = statistics.get("min_value", None)
        avg_value = statistics.get("avg_value", None)
        max_len = statistics.get("max_len", None)
        min_len = statistics.get("min_len", None)

        field_info_str = [
            "【字段信息】",
            f"字段名称: {field_name}",
            f"字段类型: {field_type}",
        ]
        unique = field_info.get("unique", False)
        if primary_key:
            unique = True
        if len(comment) > 0:
            field_info_str.append(f"字段描述: {comment}")
        field_info_str.append(f"是否为主键(或者与其他字段组成联合主键): {primary_key}")
        field_info_str.append(f"UNIQUE: {unique}")
        field_info_str.append(f"NULLABLE: {nullable}")
        if total_num is not None:
            field_info_str.append(f"COUNT: {total_num}")
        if unique_num is not None:
            field_info_str.append(f"COUNT(DISTINCT): {unique_num}")
        if max_value is not None:
            field_info_str.append(f"MAX: {max_value}")
        if min_value is not None:
            field_info_str.append(f"MIN: {min_value}")
        if avg_value is not None:
            field_info_str.append(f"AVG: {avg_value}")
        if max_len is not None:
            field_info_str.append(f"MAX(CHAR_LENGTH): {max_len}")
        if min_len is not None:
            field_info_str.append(f"MIN(CHAR_LENGTH): {min_len}")
        if dim_or_meas in self._type_engine.dim_measure_labels:
            field_info_str.append(f"Dimension/Measure: {dim_or_meas}")
        if date_min_gran:
            field_info_str.append(
                f"该字段表示的语义可能与日期或时间有关，推测它表示的最小时间颗粒度是: {date_min_gran}"
            )
        if date_range:
            if len(date_range) == 2:
                field_info_str.append(f"数据范围: {date_range}")

        value_examples = self._mschema.tables[table_name]["fields"][field_name].get(
            "examples", []
        )

        if len(value_examples) > 0:
            field_info_str.append(f"Value Examples: {value_examples}")

        return "\n".join(field_info_str)

    async def single_field_category(self, table_name, field_name, field_type):
        try:
            field_type_cate = self._type_engine.field_type_cate(field_type)
            # 获取字段的相关信息，用于确定 category
            field_info_str = self.get_single_field_info_str(table_name, field_name)

            res = await field_category(
                field_type_cate,
                self._type_engine,
                self._agent,
                field_info_str=field_info_str,
            )
            self._mschema.set_column_property(
                table_name, field_name, "category", res["category"]
            )
            self._mschema.set_column_property(
                table_name, field_name, "dim_or_meas", res["dim_or_meas"]
            )

            category = res["category"]
            min_gran = ""

            # 启动任务
            # 对于日期类型的字段，获取它可能的最小时间颗粒度
            if category == self._type_engine.field_category_date_label:
                min_gran_task = asyncio.create_task(
                    understand_date_time_min_gran(field_info_str, agent=self._agent)
                )
            # 对于枚举类型的字段，获取它所有的枚举候选值
            if category == self._type_engine.field_category_enum_label:
                examples_task = asyncio.create_task(
                    self.get_column_value_examples(table_name, field_name)
                )

            # 收集结果
            if category == self._type_engine.field_category_date_label:
                min_gran = await min_gran_task
                if min_gran in self._type_engine.date_time_min_grans:
                    self._mschema.set_column_property(
                        table_name, field_name, "date_min_gran", min_gran
                    )

            if category == self._type_engine.field_category_enum_label:
                examples = await examples_task
                examples = [s for s in examples if len(str(examples)) > 0]
                self._mschema.set_column_property(
                    table_name, field_name, "examples", examples
                )
        except Exception as e:
            logger.error(e)

    async def single_table_category(self, table_name, column_names=None):
        # 使用信号量控制并发度
        async with self.semaphore:
            # 这是处理每个表的任务

            tables = self._mschema.tables
            fields = tables[table_name]["fields"]
            if column_names is not None:
                fields = {field_name: fields[field_name] for field_name in column_names}

            tasks = []
            for field_name, field_info in fields.items():
                field_type = field_info["type"]
                tasks.append(
                    self.single_field_category(table_name, field_name, field_type)
                )
            await asyncio.gather(*tasks)

    @log_runtime
    async def fields_category(self, table_names: list = None):
        tables = self._mschema.tables
        table_names = table_names or list(tables.keys())

        tasks = []
        for table_name in table_names:
            tasks.append(self.single_table_category(table_name))
        await asyncio.gather(*tasks)

    def _get_column_reference_info(
        self, table_name: str, field_name: str, ref_num: int = 3
    ):
        reference_info = ""
        refs = self._column_ref_pool.get(field_name, {})
        if not refs:
            return reference_info
        i = 0
        for ref_name, ref_info in refs.items():
            if i >= ref_num:
                break
            if ref_name == table_name:
                continue

            if "separate" in self.comment_mode:
                rename = ref_info.get("rename", "")
                remark = ref_info.get("remark", "")
                if rename or remark:
                    sep = ", " if rename and remark else ""
                    reference_info += (
                        f"表 {ref_name} 中的字段 {field_name}: {rename}{sep}{remark}\n"
                    )
            else:
                comment = ref_info.get("comment", "")
                if comment:
                    reference_info += (
                        f"表 {ref_name} 中的字段 {field_name}: {comment}\n"
                    )
            i += 1

        if reference_info:
            reference_info = "数据库中其他表的相同字段:\n" + reference_info

        return reference_info

    async def column_desc_generation(
        self,
        table_name,
        field_name,
        field_info,
        table_mschema,
        supp_info,
        sql,
        res,
        ref_num: int = 3,
        language: str = "CN",
    ):
        try:
            # 查找参考信息
            reference_info = self._get_column_reference_info(
                table_name, field_name, ref_num
            )
            field_info_str = self.get_single_field_info_str(table_name, field_name)
            dim_or_meas = field_info.get("dim_or_meas", "")
            field_desc = field_info.get("comment", "")
            field_remark = field_info.get("remark", "")
            field_rename = field_info.get("rename", "")

            # 根据 comment_mode 决定需要更新的属性
            update_props = None
            if "separate" in self.comment_mode:
                if not field_remark or not field_rename:
                    update_props = ("rename", "remark")
            elif not field_desc:
                update_props = ("comment",)
            if update_props:
                gen_rename, gen_remark = await generate_column_desc(
                    field_name,
                    field_info_str,
                    table_mschema,
                    self._agent,
                    sql,
                    res,
                    supp_info.get(dim_or_meas, ""),
                    reference_info=reference_info,  # 传入参考信息
                    language=language,
                )

                if "rename" in update_props:
                    self._column_ref_pool.setdefault(field_name, {}).setdefault(
                        table_name, {}
                    )["rename"] = gen_rename

                    self._mschema.set_column_property(
                        table_name, field_name, "rename", gen_rename
                    )
                if "remark" in update_props:
                    self._column_ref_pool.setdefault(field_name, {}).setdefault(
                        table_name, {}
                    )["remark"] = gen_remark

                    self._mschema.set_column_property(
                        table_name, field_name, "remark", gen_remark
                    )
                if "comment" in update_props:
                    self._column_ref_pool.setdefault(field_name, {}).setdefault(
                        table_name, {}
                    )["comment"] = gen_remark

                    self._mschema.set_column_property(
                        table_name, field_name, "comment", gen_remark
                    )

                self._mschema.set_column_property(
                    table_name,
                    field_name,
                    "desc_gen_status",
                    BackStatusEnum.SUCCEEDED,
                )
        except Exception as e:
            logger.error(e)
            self._mschema.set_column_property(
                table_name, field_name, "desc_gen_status", BackStatusEnum.FAILED
            )

    async def table_desc_generation(
        self,
        table_name,
        gen_table_desc: bool = True,
        column_names: list = None,
        ref_num: int = 3,
        language: str = "CN",
    ):
        async with self.semaphore:
            try:
                db_info = self._mschema.db_info
                table_info = self._mschema.tables[table_name]

                fields = table_info["fields"]
                if column_names is not None:
                    fields = {
                        field_name: fields[field_name] for field_name in column_names
                    }

                sql = self.get_all_field_examples_sql(table_name, max_rows=10)
                res = await self.fetch_truncated(sql, max_rows=10)
                res = self.trunc_result_to_markdown(res)

                """2、按照维度和度量分类，理解各个维度/度量字段之间的区别与联系，供参考"""
                table_mschema = self._mschema.single_table_mschema(table_name)
                dim_fields = self._mschema.get_dim_or_meas_fields(
                    self._type_engine.dimension_label, table_name
                )
                mea_fields = self._mschema.get_dim_or_meas_fields(
                    self._type_engine.measure_label, table_name
                )

                dim_or_meas_tasks = []
                dim_or_meas_type = []
                if len(dim_fields) > 0:
                    dim_or_meas_type.append(
                        (dim_fields, self._type_engine.dimension_label)
                    )
                if len(mea_fields) > 0:
                    dim_or_meas_type.append(
                        (mea_fields, self._type_engine.measure_label)
                    )

                dim_or_meas_tasks = [
                    understand_fields_by_category(
                        db_info,
                        table_name,
                        table_mschema,
                        self._agent,
                        sql,
                        res,
                        fields,
                        label,
                    )
                    for fields, label in dim_or_meas_type
                ]

                supp_info_res = await asyncio.gather(*dim_or_meas_tasks)
                supp_info = {
                    label: result
                    for (_, label), result in zip(dim_or_meas_type, supp_info_res)
                }

                """3、对每一列生成列描述"""
                field_task = []
                for field_name, field_info in fields.items():
                    field_task.append(
                        self.column_desc_generation(
                            table_name,
                            field_name,
                            field_info,
                            table_mschema,
                            supp_info,
                            sql,
                            res,
                            ref_num=ref_num,
                            language=language,
                        )
                    )
                await asyncio.gather(*field_task)

                """4、表描述生成"""
                if gen_table_desc:
                    # merge table comment
                    table_comment = table_info.get("comment", "")
                    table_rename = table_info.get("rename", "")
                    table_remark = table_info.get("remark", "")

                    # 根据 comment_mode 决定需要更新的属性
                    update_props = None
                    if "separate" in self.comment_mode:
                        if not table_rename or not table_remark:
                            update_props = ("rename", "remark")
                    elif len(table_comment) <= 10:
                        update_props = ("comment",)

                    if update_props:
                        gen_rename, gen_remark = await self.only_table_desc_generation(
                            table_name,
                            sql=sql,
                            res=res,
                            language=language,
                        )

                        if "rename" in update_props:
                            self._mschema.set_table_property(
                                table_name, "rename", gen_rename
                            )
                        if "remark" in update_props:
                            self._mschema.set_table_property(
                                table_name, "remark", gen_remark
                            )
                        if "comment" in update_props:
                            self._mschema.set_table_property(
                                table_name, "comment", gen_remark
                            )
            except Exception as e:
                logger.error(e)
                self._mschema.set_table_property(
                    table_name, "desc_gen_status", BackStatusEnum.FAILED
                )

    async def only_table_desc_generation(
        self,
        table_name: str,
        sql: Optional[str] = None,
        res: Optional[str] = None,
        language: Optional[str] = "CN",
    ):
        try:
            table_mschema = self._mschema.single_table_mschema(table_name)

            if not sql:
                sql = self.get_all_field_examples_sql(table_name, max_rows=10)
            if not res:
                res = await self.fetch_truncated(sql, max_rows=10)
                res = self.trunc_result_to_markdown(res)

            gen_rename, gen_remark = await generate_table_desc(
                table_name,
                table_mschema,
                self._agent,
                sql,
                res,
                language=language,
            )

            self._mschema.set_table_property(
                table_name, "desc_gen_status", BackStatusEnum.SUCCEEDED
            )

            return gen_rename, gen_remark
        except Exception as e:
            logger.error(e)
            self._mschema.set_table_property(
                table_name, "desc_gen_status", BackStatusEnum.FAILED
            )

    async def _init_column_ref_pool(self):
        """
        初始化列参考池，从已有的表字段注释中收集信息
        这样在并发生成注释时，相同字段名可以直接从共享池中获取参考
        """
        for table_name, table_info in self._mschema.tables.items():
            fields = table_info.get("fields", {})
            for field_name, field_info in fields.items():
                ref_info = {
                    key: field_info.get(key, "").strip()
                    for key in ("rename", "remark", "comment")
                }
                ref_info = {k: v for k, v in ref_info.items() if v}  # 只保留非空的字段
                if ref_info:
                    self._column_ref_pool.setdefault(field_name, {})[
                        table_name
                    ] = ref_info

    async def gen_data_desc(self, return_format: bool = True):
        db_mschema = self._mschema.to_mschema(selected_columns=[])
        data_rename, data_desc, db_info = await understand_database(
            db_mschema, self._agent
        )

        if return_format:
            return (data_rename + data_desc + "\n" + db_info).strip()
        else:
            return data_rename, data_desc, db_info

    @log_runtime
    async def desc_generation(
        self,
        table_names: list = None,
        ref_num: int = 3,
        language: str = "CN",
    ):
        """
        description genration

        四种模式：
        no_comment: 不带任何描述信息
        origin: 跟数据库中保持一致
        generation: 清除已有的描述信息，完全由模型生成
        merge: 没有描述的生成描述信息；已经有描述信息的，不再生成
        separate: 使用另外的键值对（如: remark 字段）来单独存储描述信息
        separate_merge: 保留已有的描述信息，仅对空值进行补充
        """
        if self.comment_mode == "origin":
            return
        elif self.comment_mode == "merge":
            pass
        elif self.comment_mode == "generation":
            self._mschema.erase_all_column_comment()
            self._mschema.erase_all_table_comment()
        elif self.comment_mode == "no_comment":
            self._mschema.erase_all_column_comment()
            self._mschema.erase_all_table_comment()
            return
        elif self.comment_mode == "separate":
            self._mschema.erase_all_column_remark()
            self._mschema.erase_all_table_remark()
        elif self.comment_mode == "separate_merge":
            pass
        else:
            raise NotImplementedError(f"Unsupported comment mode {self.comment_mode}.")

        # 预填充列参考池
        await self._init_column_ref_pool()

        """1、初步理解数据库的基本信息和每张表的内容"""
        db_info = await self.gen_data_desc()
        self._mschema.db_info = db_info
        table_names = table_names or list(self._mschema.tables.keys())

        task = []
        for table_name in table_names:
            task.append(
                self.table_desc_generation(
                    table_name, ref_num=ref_num, language=language
                )
            )
        await asyncio.gather(*task)

    async def set_table_status(
        self,
        table_names: list = None,
        status_type: str = "desc_gen_status",
        status: BackStatusEnum = BackStatusEnum.WORKING,
    ):
        table_names = table_names or list(self._mschema.tables.keys())

        for table_name in table_names:
            if status_type == "desc_gen_status":
                if self.comment_mode == "separate":
                    self.mschema.set_table_property(table_name, "rename", "")
                    self.mschema.set_table_property(table_name, "remark", "")
                elif self.comment_mode == "separate_merge":
                    table_info = self._mschema.tables[table_name]
                    table_rename = table_info.get("rename", "")
                    table_remark = table_info.get("remark", "")

                    if table_rename and table_remark:
                        continue

            self.mschema.set_table_property(table_name, status_type, status)

    async def set_column_status(
        self,
        table_name: str,
        column_names: list = None,
        status_type: str = "desc_gen_status",
        status: BackStatusEnum = BackStatusEnum.WORKING,
    ):
        table_info = self._mschema.tables[table_name]
        fields = table_info["fields"]
        if column_names is not None:
            fields = {field_name: fields[field_name] for field_name in column_names}

        for field_name, field_info in fields.items():
            if status_type == "desc_gen_status":
                if self.comment_mode == "separate":
                    self.mschema.set_column_property(
                        table_name, field_name, "rename", ""
                    )
                    self.mschema.set_column_property(
                        table_name, field_name, "remark", ""
                    )
                elif self.comment_mode == "separate_merge":
                    field_rename = field_info.get("rename", "")
                    field_remark = field_info.get("remark", "")

                    if field_rename and field_remark:
                        continue

            self.mschema.set_column_property(
                table_name, field_name, status_type, status
            )

    async def sync_to_local(self, local_engine: Engine):
        """同步数据到本地数据库"""
        from sqlalchemy.orm import sessionmaker

        local_metadata = MetaData()

        # 连接到远程数据库
        remote_metadata = MetaData()
        remote_metadata.reflect(bind=self._engine)

        remote_metadata.create_all(bind=self._engine)

        # 同步表结构和数据
        for table_name in remote_metadata.tables:
            remote_table = Table(
                table_name, remote_metadata, autoload_with=self._engine
            )

            # 创建本地表
            remote_table.metadata = local_metadata
            local_metadata.drop_all(local_engine)
            local_metadata.create_all(local_engine, tables=[remote_table])

            # 将数据同步到本地
            Session = sessionmaker(bind=self._engine)
            session = Session()
            with local_engine.begin() as local_connection:
                data = session.query(remote_table).all()
                columns = remote_table.columns.keys()
                insert_data = [dict(zip(columns, d)) for d in data]
                local_connection.execute(remote_table.insert(), insert_data)

    def close(self):
        self._engine.dispose()


