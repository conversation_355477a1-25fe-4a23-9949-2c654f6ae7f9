from dataclasses import dataclass, field
from typing import Optional
from urllib.parse import quote_plus
from fastapi import HTTPEx<PERSON>


@dataclass
class DBConfig:
    db_type: str = "sqlite"
    db_path: Optional[str] = None  # 仅用于 SQLite
    db_name: Optional[str] = None  # MySQL/PostgreSQL 通用
    user_name: Optional[str] = None  # MySQL/PostgreSQL 通用
    db_pwd: Optional[str] = None  # MySQL/PostgreSQL 通用
    db_host: Optional[str] = None  # MySQL/PostgreSQL 通用
    port: Optional[int] = None  # MySQL/PostgreSQL 通用

    def __post_init__(self):
        if self.db_type == "sqlite":
            self.db_path = self.db_path or "book_1.sqlite"
        elif self.db_type in ["excel"]:
            pass
        elif self.db_type in [
            "mysql",
            "vastdata",
            "postgresql",
            "hive",
        ]:
            self.db_name = self.db_name or "default_db"
            self.user_name = (
                quote_plus(self.user_name) if self.user_name else "default_user"
            )
            self.db_pwd = quote_plus(self.db_pwd) if self.db_pwd else "default_password"
            self.db_host = self.db_host or "localhost"
            self.port = int(self.port) or (3306 if self.db_type == "mysql" else 5432)
        elif self.db_type in [
            "argodb",
        ]:
            self.db_name = self.db_name or "default_db"
            self.user_name = self.user_name or "default_user"
            self.db_pwd = self.db_pwd or "default_password"
            self.db_host = self.db_host or "localhost"
            self.port = int(self.port) or 10000
        else:
            raise HTTPException(status_code=1906, detail="")
