import os
from sqlalchemy.engine.url import URL
from pathlib import Path

from sqlalchemy import create_engine

from sqlalchemy.engine import Engine
from sqlalchemy.exc import OperationalError, ProgrammingError

from .db_config import DBConfig
from .db_source import HITLSQLDatabase

from fastapi import HTTPException
from .logger_util import *

# 在 db_init.py 或其他初始化脚本中
from .db_dialect.sqlalchemy_argodb import ArgodbDialect


@log_runtime
def init_db_conn(db_config: DBConfig, conn_init: bool = True, **kwargs) -> Engine:
    try:
        if db_config.db_type.lower() == "sqlite":
            db_engine = _connect_to_sqlite(db_config.db_path)
            if conn_init:
                data_conn = HITLSQLDatabase(db_engine, db_config=db_config, **kwargs)
                return data_conn
            else:
                return db_engine

        elif db_config.db_type.lower() == "mysql":
            db_engine = _connect_to_mysql(
                db_config.db_name,
                db_config.user_name,
                db_config.db_pwd,
                db_config.db_host,
                db_config.port,
            )
            if conn_init:
                data_conn = HITLSQLDatabase(db_engine, db_config=db_config, **kwargs)
                return data_conn
            else:
                return db_engine

        elif db_config.db_type.lower() in ["postgresql", "vastdata"]:
            db_engine = _connect_to_pg(
                db_config.db_name,
                db_config.user_name,
                db_config.db_pwd,
                db_config.db_host,
                db_config.port,
            )
            if conn_init:
                data_conn = HITLSQLDatabase(db_engine, db_config=db_config, **kwargs)
                return data_conn
            else:
                return db_engine

        elif db_config.db_type.lower() in ["hive"]:
            db_engine = _connect_to_hive(
                db_config.db_name,
                db_config.user_name,
                db_config.db_pwd,
                db_config.db_host,
                db_config.port,
            )
            if conn_init:
                data_conn = HITLSQLDatabase(db_engine, db_config=db_config, **kwargs)
                return data_conn
            else:
                return db_engine

        elif db_config.db_type.lower() in ["argodb"]:
            db_engine = _connect_to_argodb(
                db_config.db_name,
                db_config.user_name,
                db_config.db_pwd,
                db_config.db_host,
                db_config.port,
            )
            if conn_init:
                data_conn = HITLSQLDatabase(db_engine, db_config=db_config, **kwargs)
                return data_conn
            else:
                return db_engine

        elif db_config.db_type.lower() == "excel":
            db_engine = _connect_to_excel(db_config.db_name, db_config.db_path)
            if conn_init:
                data_conn = HITLSQLDatabase(db_engine, db_config=db_config, **kwargs)
                return data_conn
            else:
                return db_engine

        else:
            raise HTTPException(status_code=2006, detail="")

    except OperationalError as e:
        if "No route to host" in str(e) or "Name or service not known" in str(e):
            logger.warning(e)
            raise HTTPException(status_code=2001, detail="")
        elif "Connection refused" in str(e):
            logger.warning(e)
            raise HTTPException(status_code=2002, detail="")
        elif "Access denied" in str(e):
            logger.warning(e)
            raise HTTPException(status_code=2003, detail="")
        elif "Unknown database" in str(e):
            logger.warning(e)
            raise HTTPException(status_code=2004, detail="")
        else:
            logger.error(e)
            raise HTTPException(status_code=2000, detail="")
    except ProgrammingError as e:
        logger.warning(e)
        raise HTTPException(status_code=2005, detail="")
    except Exception as e:
        logger.exception(e)
        raise HTTPException(status_code=2000, detail="")


def _connect_to_sqlite(db_path: str) -> Engine:
    assert os.path.exists(db_path)
    db_engine = create_engine(f"sqlite:///{os.path.abspath(db_path)}")
    return db_engine


def _connect_to_mysql(db_name, user_name, db_pwd, db_host, port) -> Engine:
    db_engine = create_engine(
        f"mysql+pymysql://{user_name}:{db_pwd}@{db_host}:{port}/{db_name}"
    )
    return db_engine


def _connect_to_pg(db_name, user_name, db_pwd, db_host, port) -> Engine:
    db_engine = create_engine(
        f"postgresql+psycopg2://{user_name}:{db_pwd}@{db_host}:{port}/{db_name}"
    )
    return db_engine


def _connect_to_hive(db_name, user_name, db_pwd, db_host, port) -> Engine:
    db_engine = create_engine(
        f"hive://{user_name}:{db_pwd}@{db_host}:{port}/{db_name}",
        connect_args={"auth": "LDAP"},
    )

    return db_engine


def _connect_to_argodb(db_name, user_name, db_pwd, db_host, port) -> Engine:
    from sqlalchemy.engine.url import URL

    project_root = os.getcwd().split("/src")[0]
    jar_name = config_data["argodb_jdbc"]["jar_name"]
    jar_path = os.path.join(project_root, "src", "jdbc", "argodb", "jar", jar_name)

    host = f"{db_host}:{port}/{db_name}"

    if config_data["argodb_jdbc"]["auth_mode"] == "LDAP":
        eng_url = URL.create(
            drivername="sqlajdbc",
            host=host,
            query={
                "_driver": "transwarp2",
                "_class": "org.apache.hive.jdbc.HiveDriver",
                "_jars": jar_path,
                "_sep": ";",
                "_start": ";",
                "user": user_name,
                "password": db_pwd,
                "autoReconnect": "true?transaction.type=inceptor",
            },
        )
    elif config_data["argodb_jdbc"]["auth_mode"] == "Kerberos":
        eng_url = URL.create(
            drivername="sqlajdbc",
            host=host,
            query={
                "_driver": "transwarp2",
                "_class": "org.apache.hive.jdbc.HiveDriver",
                "_jars": jar_path,
                "_sep": ";",
                "_start": ";",
                # "password": db_pwd,
                "autoReconnect": "true?transaction.type=inceptor",
                "principal": f"{user_name}/node@TDH",
                "authentication": "kerberos",
                "kuser": config_data["argodb_jdbc"]["user_principal"],
                "keytab": config_data["argodb_jdbc"]["keytab_path"],
                "krb5conf": config_data["argodb_jdbc"]["krb5_conf_path"],
            },
        )
    else:
        raise HTTPException(status_code=2007, detail="error auth mode")

    engine = create_engine(eng_url)
    engine.dialect = ArgodbDialect()

    return engine


def _connect_to_excel(db_name, db_path) -> Engine:
    sheets_data = _read_excel(db_name, db_path)
    db_engine = create_engine(f"sqlite:///:memory:")
    for sheet_name, df in sheets_data.items():
        if df.empty:  # 如果数据框为空，则跳过
            continue
        df.to_sql(sheet_name, db_engine, index=False, if_exists="replace")

    return db_engine


import pandas as pd
import numpy as np


def _read_excel(file_name, file_path):
    """读取 Excel 文件的所有 Sheet，并存储到字典中"""

    file_name_without_extension, extension = os.path.splitext(
        os.path.basename(file_name)
    )
    extension = extension.lower()

    sheets_data = {}
    if extension in [".xlsx", ".xls"]:
        sheets = pd.read_excel(
            file_path,
            sheet_name=None,
        )
        for sheet_name, df in sheets.items():
            df.replace("", np.nan, inplace=True)
            df.dropna(how="all", inplace=True)
            df.dropna(how="all", axis=1, inplace=True)

            sheets_data[sheet_name] = df

    elif extension == ".csv":
        df = pd.read_csv(
            file_path,
            index_col=False,
        )
        df.replace("", np.nan, inplace=True)
        df.dropna(how="all", inplace=True)
        df.dropna(how="all", axis=1, inplace=True)
        sheets_data[file_name_without_extension] = df
    else:
        raise HTTPException(status_code=2100, detail="")

    return sheets_data
