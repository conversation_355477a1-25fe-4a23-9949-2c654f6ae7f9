import random
from .file_util import read_json_file, write_json_to_file, save_raw_text
from .db_util import examples_to_str
from .type_engine import TypeEngine
from typing import Any, Dict, Iterable, List, Optional, Tuple, Union


class MSchema:
    def __init__(
        self,
        db_name: str = "Anonymous",
        db_type: str = "mysql",
        db_info: str = "",
        type_engine: Optional[TypeEngine] = TypeEngine(),
        schema: Optional[str] = None,
    ):
        self.db_name = db_name
        self.db_type = db_type
        self.db_info = db_info
        self.schema = schema
        self.tables = {}
        self.foreign_keys = []
        self.type_engine = type_engine

    def add_table(
        self,
        name,
        fields={},
        comment: str = "",
        rename: str = "",
        remark: str = "",
    ):
        self.tables[name] = {
            "fields": fields.copy(),
            "examples": [],
            "comment": comment,
            "rename": rename,
            "remark": remark,
        }

    def add_field(
        self,
        table_name: str,
        field_name: str,
        field_type: str = "",
        primary_key: bool = False,
        nullable: bool = True,
        default: Any = None,
        autoincrement: bool = False,
        unique: bool = False,
        comment: str = "",
        category: str = "",
        dim_or_meas: Optional[str] = "",
        date_min_gran: str = "",
        examples: List[str] = [],
        statistics: Dict[str, Any] = {},
        rename: str = "",
        remark: str = "",
        data_range: List[str] = [],
        **kwargs,
    ):
        self.tables[table_name]["fields"][field_name] = {
            "type": field_type,
            "primary_key": primary_key,
            "nullable": nullable,
            "default": default if default is None else f"{default}",
            "autoincrement": autoincrement,
            "unique": unique,
            "comment": comment,
            "category": category,
            "dim_or_meas": dim_or_meas,
            "date_min_gran": date_min_gran,
            "examples": examples,
            "statistics": statistics,
            "rename": rename,
            "remark": remark,
            "data_range": data_range,
            **kwargs,
        }

    def add_foreign_key(
        self, table_name, field_name, ref_schema, ref_table_name, ref_field_name
    ):
        self.foreign_keys.append(
            [table_name, field_name, ref_schema, ref_table_name, ref_field_name]
        )

    def get_abbr_field_type(self, field_type, simple_mode=True) -> str:
        if not simple_mode:
            return field_type
        else:
            return field_type.split("(")[0]

    def erase_all_table_comment(self):
        """clear all table descriptions."""
        for table_name in self.tables.keys():
            self.tables[table_name]["comment"] = ""

    def erase_all_column_comment(self):
        """clear all column descriptions."""
        for table_name in self.tables.keys():
            fields = self.tables[table_name]["fields"]
            for field_name, field_info in fields.items():
                self.tables[table_name]["fields"][field_name]["comment"] = ""

    def erase_all_table_remark(self):
        """clear all table rename and remark."""
        for table_name in self.tables.keys():
            self.tables[table_name]["rename"] = ""
            self.tables[table_name]["remark"] = ""

    def erase_all_column_remark(self):
        """clear all column rename and remark."""
        for table_name in self.tables.keys():
            fields = self.tables[table_name]["fields"]
            for field_name, field_info in fields.items():
                self.tables[table_name]["fields"][field_name]["rename"] = ""
                self.tables[table_name]["fields"][field_name]["remark"] = ""

    def has_table(self, table_name: str) -> bool:
        """check if given table_name exists in M-Schema"""
        if table_name in self.tables.keys():
            return True
        else:
            return False

    def has_column(self, table_name: str, field_name: str) -> bool:
        if self.has_table(table_name):
            if field_name in self.tables[table_name]["fields"].keys():
                return True
            else:
                return False
        else:
            return False

    def set_table_property(self, table_name: str, key: str, value: Any):
        if not self.has_table(table_name):
            print("The table name {} does not exist in M-Schema.".format(table_name))
        else:
            self.tables[table_name][key] = value

    def set_column_property(
        self, table_name: str, field_name: str, key: str, value: Any
    ):
        """为 M-Schema 中的列设置特定属性"""
        if not self.has_column(table_name, field_name):
            print(
                "The table name {} or column name {} does not exist in M-Schema.".format(
                    table_name, field_name
                )
            )
        else:
            self.tables[table_name]["fields"][field_name][key] = value

    def get_field_info(self, table_name: str, field_name: str) -> Dict:
        try:
            return self.tables[table_name]["fields"][field_name]
        except:
            return {}

    def get_category_fields(self, category: str, table_name: str) -> List:
        """
        给定table_name和category，获取当前table下所有category类型的字段名称
        category: 从type_engine.field_category_all_labels中取值
        """
        assert (
            category in self.type_engine.field_category_all_labels
        ), "Invalid category {}".format(category)
        if self.has_table(table_name):
            res = []
            fields = self.tables[table_name]["fields"]
            for field_name, field_info in fields.items():
                _ = field_info.get("category", "")
                if _ == category:
                    res.append(field_name)
            return res
        else:
            return []

    def get_dim_or_meas_fields(self, dim_or_meas: str, table_name: str) -> List:
        """获取指定表中的维度或度量字段列表"""
        assert (
            dim_or_meas in self.type_engine.dim_measure_labels
        ), "Invalid dim_or_meas {}".format(dim_or_meas)
        if self.has_table(table_name):
            res = []
            fields = self.tables[table_name]["fields"]
            for field_name, field_info in fields.items():
                _ = field_info.get("dim_or_meas", "")
                if _ == dim_or_meas:
                    res.append(field_name)
            return res
        else:
            return []

    def get_mschema_table_info(self, table_name: str) -> str:
        """获取指定表的语义信息"""

        table_info = self.tables.get(table_name, {})

        table_rename = table_info.get("rename", "").strip()
        if self.db_type == "excel":
            table_rename = ""
        table_comment = table_info.get("comment", "").strip()
        if table_remark := table_info.get("remark", ""):
            table_comment = table_remark.strip()

        if (
            table_comment is not None
            and table_comment != "None"
            and len(table_comment) > 0
        ):
            if self.schema is not None and len(self.schema) > 0:
                return f"{self.schema}.{table_name}{f'({table_rename})' if table_rename else ''}, {table_comment}"
            else:
                return f"{table_name}{f'({table_rename})' if table_rename else ''}, {table_comment}"
        else:
            if self.schema is not None and len(self.schema) > 0:
                return f"{self.schema}.{table_name}{f'({table_rename})' if table_rename else ''}"
            else:
                return f"{table_name}{f'({table_rename})' if table_rename else ''}"

    def get_mschema_field_info(
        self, field_name, field_info, show_type_detail=False
    ) -> str:
        """获取指定字段的语义信息"""
        field_rename = field_info.get("rename", "").strip()
        if self.db_type == "excel":
            field_rename = ""
        field_comment = field_info.get("comment", "").strip()
        if field_remark := field_info.get("remark", ""):
            field_comment = field_remark.strip()

        # 初始化
        field_line = f"{field_name}{f'({field_rename})' if field_rename else ''}"
        # 增加类型标识
        raw_type = self.get_abbr_field_type(
            field_info["type"], not show_type_detail
        ).upper()
        field_line += f": {raw_type}"

        field_category = field_info.get("category", "").strip()
        is_primary_key = field_info.get("primary_key", False)
        is_unique = field_info.get("unique", False)

        # if field_dim_or_meas:
        # field_line += f"({field_dim_or_meas}"
        # field_line += f"("
        if is_primary_key:
            field_line += f", Primary Key"
        elif field_category:
            field_line += f", {field_category}"
        elif is_unique:
            field_line += f", Unique"
        # field_line += f")"

        # comment
        if field_comment:
            field_line += f", {field_comment}"
        return field_line

    def single_table_mschema(
        self,
        table_name: str,
        selected_columns: Optional[List] = None,
        example_num=3,
        show_type_detail=False,
        shuffle=True,
    ) -> str:
        output = []

        table_info = self.tables.get(table_name, {})
        output.append(f"# Table: {self.get_mschema_table_info(table_name)}")

        field_lines = []
        # 处理表中的每一个字段
        for field_name, field_info in table_info["fields"].items():
            if (
                selected_columns is not None
                and field_name.lower() not in selected_columns
            ):
                continue

            # 初始化
            field_line = f"("

            # 添加注释
            field_line += f"{self.get_mschema_field_info(field_name, field_info, show_type_detail)}"

            # 添加范围
            if field_info.get("data_range", None):
                data_range = field_info["data_range"]
                data_range_str = self.data_range_to_str(data_range)
                field_line += f", {data_range_str}"

            raw_type = self.get_abbr_field_type(
                field_info["type"], not show_type_detail
            ).upper()

            cate_type = self.type_engine.field_type_cate(raw_type)
            # 添加示例
            if len(field_info.get("examples", [])) > 0 and example_num > 0:
                examples = field_info["examples"]
                examples = [s for s in examples if s is not None]
                examples = examples_to_str(examples)
                if len(examples) > example_num:
                    examples = examples[:example_num]
                if examples:
                    max_examples_len = max([len(s) for s in examples])

                    if cate_type in [self.type_engine.field_type_date_label]:
                        examples = [examples[0]]
                    elif len(examples) > 0 and max_examples_len > 20:
                        if max_examples_len > 50:
                            examples = []
                        else:
                            examples = [examples[0]]
                    if len(examples) > 0:
                        example_str = ", ".join([str(example) for example in examples])
                        field_line += f", Examples: [{example_str}]"

            # 添加统计
            if field_info.get("statistics", {}):
                statistics = field_info["statistics"]
                statistics = {
                    key: value
                    for key, value in statistics.items()
                    if value and key != "unique_num"
                }

                if field_info.get("primary_key", False):
                    if total_num := statistics.get("total_num", None):
                        statistics = {"total_num": total_num}
                elif cate_type in [self.type_engine.field_type_string_label]:
                    pass
                elif cate_type in [self.type_engine.field_type_bool_label]:
                    if total_num := statistics.get("total_num", None):
                        statistics = {"total_num": total_num}
                elif cate_type in [self.type_engine.field_type_date_label]:
                    statistics = {
                        key: value
                        for key, value in statistics.items()
                        if key in ["min_value", "max_value"]
                    }
                elif cate_type in [self.type_engine.field_type_number_label]:
                    statistics = {
                        key: value
                        for key, value in statistics.items()
                        if key not in ["min_len", "max_len"]
                    }

                if statistics:
                    field_line += f", Statistics: {statistics}"

            field_line += ")"
            field_lines.append(field_line)

        if shuffle:
            random.shuffle(field_lines)

        if field_lines:
            output.append("[")
            output.append(",\n".join(field_lines))
            output.append("]")

        return "\n".join(output)

    def mschema_foreign_keys(self, selected_tables=None, check_selected=True):
        foreign_keys_str = []
        for fk in self.foreign_keys:
            ref_schema = fk[2]
            table1, column1, _, table2, column2 = fk
            if selected_tables is None or (
                (
                    table1.lower() in selected_tables
                    and table2.lower() in selected_tables
                )
                if check_selected
                else (
                    table1.lower() in selected_tables
                    or table2.lower() in selected_tables
                )
            ):
                if ref_schema == self.schema:
                    foreign_keys_str.append(f"{fk[0]}.{fk[1]}={fk[3]}.{fk[4]}")

        return foreign_keys_str

    def to_mschema(
        self,
        selected_tables: List = None,
        selected_columns: List = None,
        example_num=3,
        show_type_detail=False,
        shuffle=True,
    ) -> str:
        """
        convert to a MSchema string.
        selected_tables: 默认为None，表示选择所有的表
        selected_columns: 默认为None，表示所有列全选，格式['table_name.column_name']
        """
        output = []

        if selected_tables is not None:
            selected_tables = [s.lower() for s in selected_tables]
        if selected_columns is not None:
            if selected_columns != []:
                selected_columns = [s.lower() for s in selected_columns]
                selected_tables = [s.split(".")[0].lower() for s in selected_columns]

        # 依次处理每一个表
        for table_name, table_info in self.tables.items():
            if selected_tables is None or table_name.lower() in selected_tables:
                cur_table_type = table_info.get("type", "table")
                column_names = list(table_info["fields"].keys())
                if selected_columns is None:
                    cur_selected_columns = selected_columns
                elif selected_columns == []:
                    cur_selected_columns = selected_columns
                else:
                    cur_selected_columns = [
                        c
                        for c in column_names
                        if f"{table_name}.{c}".lower() in selected_columns
                    ]
                output.append(
                    self.single_table_mschema(
                        table_name,
                        cur_selected_columns,
                        example_num,
                        show_type_detail,
                        shuffle,
                    )
                )

        if shuffle:
            random.shuffle(output)

        output.insert(0, f"【DB_Name】 {self.db_name}")
        output.insert(1, f"【DB_Info】 {self.db_info}")
        output.insert(2, f"【Schema】")

        # 添加外键信息，选择table_type为view时不展示外键
        if self.foreign_keys:
            output.append("【Foreign keys】")
            fk_str_list = self.mschema_foreign_keys(
                selected_tables=selected_tables,
            )
            for fk_str in fk_str_list:
                output.append(fk_str)

        return "\n".join(output)

    def dump(self):
        schema_dict = {
            "db_name": self.db_name,
            "db_info": self.db_info,
            "schema": self.schema,
            "tables": self.tables,
            "foreign_keys": self.foreign_keys,
        }
        return schema_dict

    def save(self, file_path: str):
        schema_dict = self.dump()
        write_json_to_file(file_path, schema_dict, is_json_line=False)

    def load_from_file(self, file_path: str):
        data = read_json_file(file_path)
        self.db_name = data.get("db_name", "Anonymous")
        self.db_info = data.get("db_info", None)
        self.schema = data.get("schema", None)
        self.tables = data.get("tables", {})
        self.foreign_keys = data.get("foreign_keys", [])

    def load_from_dict(self, data: dict):
        self.db_name = data.get("db_name", "Anonymous")
        self.db_info = data.get("db_info", None)
        self.schema = data.get("schema", None)
        self.tables = data.get("tables", {})
        self.foreign_keys = data.get("foreign_keys", [])
