import re
import os
import datetime, decimal
from sqlalchemy import (
    create_engine,
    MetaData,
    Table,
    Column,
    String,
    Integer,
    select,
    text,
)
from sqlalchemy.engine import Engine
from enum import Enum


class BackStatusEnum(str, Enum):
    PENDING = "pending"
    WORKING = "working"
    SUCCEEDED = "succeeded"
    FAILED = "failed"


def remove_sql_comments(sql_query: str) -> str:
    # 正则表达式用于匹配 SQL 注释
    single_line_comment_pattern = r"--[^\n]*"
    multi_line_comment_pattern = r"/\*.*?\*/"

    # 删除单行注释
    sql_without_single_comments = re.sub(single_line_comment_pattern, "", sql_query)

    # 删除多行注释
    sql_without_comments = re.sub(
        multi_line_comment_pattern, "", sql_without_single_comments, flags=re.DOTALL
    )

    return sql_without_comments.strip()


def preprocess_sql_query(sql_query: str) -> str:
    # 删除注释，加上分号
    sql_query = remove_sql_comments(sql_query)
    if not sql_query.strip().endswith(";"):
        sql_query += ";"
    return sql_query


def is_email(string):
    pattern = r"^[\w\.-]+@[\w\.-]+\.\w+$"
    match = re.match(pattern, string)
    if match:
        return True
    else:
        return False


def examples_to_str(examples: list, simple_examples: bool = True) -> list[str]:
    """
    from examples to a list of str
    """
    values = examples
    if simple_examples:
        for i in range(len(values)):
            if isinstance(values[i], datetime.date):
                values = [values[i]]
                break
            elif isinstance(values[i], datetime.datetime):
                values = [values[i]]
                break
            elif isinstance(values[i], decimal.Decimal):
                values[i] = str(float(values[i]))
            elif is_email(str(values[i])):
                values = []
                break
            elif "http://" in str(values[i]) or "https://" in str(values[i]):
                values = []
                break
            elif values[i] is not None and not isinstance(values[i], str):
                pass
            elif values[i] is not None and ".com" in values[i]:
                pass

    return [str(v) for v in values if v is not None and len(str(v)) > 0]


def sql_fetcher(db_engine: Engine, sql_query: str):
    sql_query = preprocess_sql_query(sql_query)
    with db_engine.begin() as connection:
        try:
            cursor = connection.execute(text(sql_query))
            records = cursor.fetchall()
        except Exception as e:
            print("An exception occurred during SQL execution.\n", e)
            records = None
        return records
