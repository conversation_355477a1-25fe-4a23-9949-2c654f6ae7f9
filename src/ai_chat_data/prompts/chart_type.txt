{"Comparison": {"name": "比较", "description": "展示多个对象或类别之间的差异或相似性，通常按数值、字母或自定义标准排序，适合用于对比分析。", "data_requirements": "需要包含多个对象或类别的字段，且这些字段的值能进行直接比较或分类，支持排序操作"}, "Trend": {"name": "趋势", "description": "展示数据随时间或其他变量变化的趋势，通常按时间顺序排序，用于识别变化模式。", "data_requirements": "需要具有时间戳字段或顺序标识的数据，通常为时间序列数据，支持按时间顺序排序"}, "Distribution": {"name": "分布", "description": "展示数据的分布情况，如数据集中程度、偏斜度等，适合分析数据的波动性或集中性。", "data_requirements": "需要具有数值字段，并能够计算分布特征"}, "Rank": {"name": "排名", "description": "展示数据按某一指标的排序结果，通常按数值从大到小或从小到大排序，适用于展示优劣排序。", "data_requirements": "需要可排序的数值字段，支持按数值升序或降序排列"}, "Proportion": {"name": "占比", "description": "展示各部分数据在整体中所占的比例，适用于分析各部分占比情况。", "data_requirements": "需要包含多个部分的数值数据"}, "Composition": {"name": "组成", "description": "展示数据的组成部分及其变化，适合分析整体结构和各部分的变化趋势。", "data_requirements": "需要能够拆分为多个组成部分的数据字段，便于展示其相对比例或变化趋势"}, "IndicatorBoard": {"name": "指标板", "description": "展示关键指标的数值，最终聚合计算得到单一的结果，用于高层次的汇总分析。", "data_requirements": "需要包含关键指标字段，通常为聚合计算结果，如总和、平均值、最大值等，仅能有一个结果"}, "Table": {"name": "表格", "description": "以表格形式展示结构化的多维数据，支持按任意列排序，适用于展示复杂的多维数据。", "data_requirements": "需要具有多维结构的结构化数据，可以根据不同的列进行排序和筛选"}}