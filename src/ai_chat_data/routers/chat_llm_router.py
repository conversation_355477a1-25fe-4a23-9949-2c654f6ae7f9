from fastapi import APIRouter, Security

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.auth.authorize import get_user_id

router = APIRouter()


from src.ai_chat_data.schemas.chat_llm_schema import CreateTalk
from src.ai_chat_data.schemas.chat_llm_schema import CreateTalkResponse
from src.ai_chat_data.services.chat_llm_service import create_talk


@router.post(
    "/create_talk", response_model=CreateTalkResponse, summary="对话记录: 新建"
)
async def _create_talk(
    input_data: CreateTalk,
    user_id: str = Security(get_user_id),
):
    """
    status 值: 0:活动, 1:已删除, 2:已存档
    """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await create_talk(
        user_id=user_id,
        talk_type=input_data.talk_type,
        extra_body=input_data.extra_body,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_chat_data.schemas.chat_llm_schema import DeleteTalk
from src.ai_chat_data.schemas.chat_llm_schema import DeleteTalkResponse
from src.ai_chat_data.services.chat_llm_service import delete_talk


@router.post(
    "/delete_talk", response_model=DeleteTalkResponse, summary="对话记录: 删除"
)
async def _delete_talk(
    input_data: DeleteTalk,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await delete_talk(
        user_id=user_id,
        talk_id=input_data.talk_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_chat_data.schemas.chat_llm_schema import UpdateTalkName
from src.ai_chat_data.schemas.chat_llm_schema import UpdateTalkNameResponse
from src.ai_chat_data.services.chat_llm_service import update_talk_name


@router.post(
    "/update_talk_name",
    response_model=UpdateTalkNameResponse,
    summary="对话记录: 修改名称",
)
async def _update_talk_name(
    input_data: UpdateTalkName,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_talk_name(
        user_id=user_id,
        talk_id=input_data.talk_id,
        talk_name=input_data.talk_name,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_chat_data.schemas.chat_llm_schema import FetchTalkRecords
from src.ai_chat_data.schemas.chat_llm_schema import FetchTalkRecordsResponse
from src.ai_chat_data.services.chat_llm_service import fetch_talk_records


@router.post(
    "/fetch_talk_records",
    response_model=FetchTalkRecordsResponse,
    summary="对话记录: 查询",
)
async def _fetch_talk_records(
    input_data: FetchTalkRecords,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_talk_records(
        user_id=user_id,
        talk_types=input_data.talk_types,
        key_word=input_data.key_word,
        page_number=input_data.page_number,
        page_size=input_data.page_size,
        enable_page=input_data.enable_page,
        group=input_data.group,
        extra_body=input_data.extra_body,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_chat_data.schemas.chat_llm_schema import FetchQARecords
from src.ai_chat_data.schemas.chat_llm_schema import FetchQARecordsResponse
from src.ai_chat_data.services.chat_llm_service import fetch_qa_records


@router.post(
    "/fetch_qa_records",
    response_model=FetchQARecordsResponse,
    summary="问答记录: 查询",
)
async def _fetch_qa_records(
    input_data: FetchQARecords, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_qa_records(
        user_id,
        talk_id=input_data.talk_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_chat_data.schemas.chat_llm_schema import QuestionsSuggester
from src.ai_chat_data.services.chat_llm_service import questions_suggester


@router.post(
    "/questions_suggester",
    summary="对话通用: 问题推荐",
)
async def _questions_suggester(
    input_data: QuestionsSuggester, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await questions_suggester(
        user_id=user_id,
        talk_id=input_data.talk_id,
        data_ids=input_data.data_ids,
        dir_ids=input_data.dir_ids,
        qa_list=input_data.qa_list,
    )
    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data
