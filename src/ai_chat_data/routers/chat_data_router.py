import os
import sys
import json

from fastapi import APIRouter, Security
from fastapi.responses import StreamingResponse, JSONResponse

from src.common.utils.serialize import serialize
from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.auth.authorize import get_user_id

router = APIRouter()


from src.ai_chat_data.schemas.chat_data_schema import ChatSelectData
from src.ai_chat_data.services.chat_data_service import chat_select_data


@router.post("/chat_select_data", summary="数据源选取")
async def _chat_select_data(
    input_data: ChatSelectData, user_id: str = Security(get_user_id)
):
    """
    ## 1. FD_agent NS_agent
    ### **描述**
    给出选择后的数据源
    ### **metadata**
    ```json
    {
        "selected_data": {
            "code": 200,
            "data": {
                "saved_user_ask": saved_user_ask,
                "sorted_data_infos": sorted_data_infos
            },
            "msg": "成功选取数据源"
        }
    }
    ```
    ## 2. ND_agent
    ### **描述**
    范围内不存在数据源，引导用户进行配置
    """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_select_data(
        user_id=user_id,
        data_ids=input_data.data_ids,
        dir_ids=input_data.dir_ids,
        user_ask=input_data.user_ask,
        qa_list=input_data.qa_list,
        stream=input_data.stream,
        llm_data={},
    )

    if input_data.stream:

        async def generator(result):
            async for line in result:
                yield "data:" + json.dumps(line, default=serialize) + "\n\n"

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))

        return JSONResponse(result)


from src.ai_chat_data.schemas.chat_data_schema import ChatAskRefactor
from src.ai_chat_data.services.chat_data_service import chat_ask_refactor


@router.post("/chat_ask_refactor", summary="问题改写")
async def _chat_ask_refactor(
    input_data: ChatAskRefactor, user_id: str = Security(get_user_id)
):
    """
    ## 1. RA_agent
    ### **描述**
    给出改写后的用户问题
    ### **metadata**
    ```json
    {
        "raw_ask": {
            "code": 200,
            "data": {"user_ask": user_ask},
            "msg": "保存原始输入",
        }
    }
    ```
    """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_ask_refactor(
        user_id=user_id,
        user_ask=input_data.user_ask,
        data_id=input_data.data_id,
        qa_list=input_data.qa_list,
        stream=input_data.stream,
    )

    if input_data.stream:

        async def generator(result):
            async for line in result:
                yield "data:" + json.dumps(line, default=serialize) + "\n\n"

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))

        return JSONResponse(result)


from src.ai_chat_data.schemas.chat_data_schema import ChatSelectTable
from src.ai_chat_data.services.chat_data_service import chat_select_table


@router.post("/chat_select_table", summary="数据表选取")
async def _chat_select_table(
    input_data: ChatSelectTable, user_id: str = Security(get_user_id)
):
    """
    ## 1. FT_agent
    ### **描述**
    给出选定的相关数据表
    ### **metadata**
    ```json
    {
        "selected_tables": {
            "code": 200,
            "data": {"selected_tables": selected_tables},
            "msg": "成功选取数据表"
        }
    }
    ```
    """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_select_table(
        user_id=user_id,
        user_ask=input_data.user_ask,
        data_id=input_data.data_id,
        qa_list=input_data.qa_list,
        stream=input_data.stream,
    )

    if input_data.stream:

        async def generator(result):
            async for line in result:
                yield "data:" + json.dumps(line, default=serialize) + "\n\n"

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))

        return JSONResponse(result)


from src.ai_chat_data.schemas.chat_data_schema import ChatAnalysis
from src.ai_chat_data.services.chat_data_service import chat_analysis


@router.post("/chat_analysis", summary="SQL 分析")
async def _chat_analysis(
    input_data: ChatAnalysis, user_id: str = Security(get_user_id)
):
    """
    ## 1. AS_agent
    ### **描述**
    分析 SQL 生成逻辑
    ### **metadata**
    无
    """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_analysis(
        user_id=user_id,
        user_ask=input_data.user_ask,
        data_id=input_data.data_id,
        qa_list=input_data.qa_list,
        tables=input_data.table_names,
        stream=input_data.stream,
    )

    if input_data.stream:

        async def generator(result):
            async for line in result:
                yield "data:" + json.dumps(line, default=serialize) + "\n\n"

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))

        return JSONResponse(result)


from src.ai_chat_data.schemas.chat_data_schema import ChatGenSql
from src.ai_chat_data.services.chat_data_service import chat_gen_sql


@router.post("/chat_gen_sql", summary="SQL 生成")
async def _chat_gen_sql(input_data: ChatGenSql, user_id: str = Security(get_user_id)):
    """
    ## 1. GS_agent
    ### **描述**
    首次生成 SQL
    ### **metadata**
    ```json
    {
        "struct_sql": {
            "code": 200,
            "data": {
                "sql": sql,
                "data_frame": data_frame,
                "error": error,
            },
            "msg": "SQL 生成"
        }
    }
    ```
    """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_gen_sql(
        user_id=user_id,
        user_ask=input_data.user_ask,
        data_id=input_data.data_id,
        qa_list=input_data.qa_list,
        tables=input_data.table_names,
        stream=input_data.stream,
    )

    if input_data.stream:

        async def generator(result):
            async for line in result:
                yield "data:" + json.dumps(line, default=serialize) + "\n\n"

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))

        return JSONResponse(result)


from src.ai_chat_data.schemas.chat_data_schema import ChatRfSql
from src.ai_chat_data.services.chat_data_service import chat_refine_sql


@router.post("/chat_refine_sql", summary="SQL 纠错")
async def _chat_refine_sql(input_data: ChatRfSql, user_id: str = Security(get_user_id)):
    """
    ## 1. RF_agent
    ### **描述**
    SQL 纠错
    ### **metadata**
    ```json
    {
        "refine_sql": {
            "code": 500,
            "data": {
                "sql": sql,
                "data_frame": data_frame,
                "error": error,
            },
            "msg": "SQL 运行失败"
        }
    }
    ```
    """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_refine_sql(
        user_id=user_id,
        user_ask=input_data.user_ask,
        data_id=input_data.data_id,
        qa_list=input_data.qa_list,
        tables=input_data.table_names,
        sql_list=input_data.sql_list,
        stream=input_data.stream,
    )

    if input_data.stream:

        async def generator(result):
            async for line in result:
                yield "data:" + json.dumps(line, default=serialize) + "\n\n"

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))

        return JSONResponse(result)


from src.ai_chat_data.schemas.chat_data_schema import ChatSelectSql
from src.ai_chat_data.services.chat_data_service import chat_select_sql


@router.post("/chat_select_sql", summary="图表选取")
async def _chat_select_sql(
    input_data: ChatSelectSql, user_id: str = Security(get_user_id)
):
    """
    ## 1. SS_agent
    ### **描述**
    选择最终的sql以及图表
    ### **metadata**
    ```json
    {
        "final_sql_result": {
            "code": 500,
            "data": {
                "final_sql": final_sql_result["sql"],
                "final_result": final_sql_result["result"],
                "chart_type": final_chart_type
            },
            "msg": "成功选择图表"
        }
    }
    ```
    """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_select_sql(
        user_id=user_id,
        user_ask=input_data.user_ask,
        sql_list=input_data.sql_list,
        stream=input_data.stream,
    )

    if input_data.stream:

        async def generator(result):
            async for line in result:
                yield "data:" + json.dumps(line, default=serialize) + "\n\n"

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))

        return JSONResponse(result)


from src.ai_chat_data.schemas.chat_data_schema import ChatData
from src.ai_chat_data.services.chat_data_service import chat_data
from src.common.utils.msg_utils import (
    save_qa_record,
    create_qa_record,
    build_record_data,
)


@router.post("/chat_data", summary="完整流程")
async def _chat_data(input_data: ChatData, user_id: str = Security(get_user_id)):
    """ """

    logger.info(
            f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_data(
        user_id=user_id,
        data_ids=input_data.data_ids,
        dir_ids=input_data.dir_ids,
        user_ask=input_data.user_ask,
        talk_id=input_data.talk_id,
        qa_list=input_data.qa_list,
        stream=input_data.stream,
    )

    if input_data.stream:
        qa_id = await create_qa_record(
            user_id=user_id,
            talk_id=input_data.talk_id,
            qa_list=input_data.qa_list,
        )

        async def generator(result):
            async for resp_raw in result:
                resp_msg = await build_record_data(
                    user_id=user_id,
                    talk_id=input_data.talk_id,
                    qa_list=input_data.qa_list,
                    qa_id=qa_id,
                    resp_raw=resp_raw,
                )

                yield "data:" + json.dumps(resp_msg, default=serialize) + "\n\n"

            await save_qa_record(
                user_id=user_id,
                talk_id=input_data.talk_id,
                qa_id=qa_id,
                qa_record=resp_msg,
            )

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))
        return JSONResponse(result)
