import json

from pathlib import Path
from typing import List, Dict
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from pymongo.errors import PyMongoError
from datetime import datetime, timedelta


from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.object_id import convert_to_object_id
from src.common.utils.error_codes import return_error
from src.common.utils.time import get_current_time_with_weekday

from src.common.utils.extract_json import ej_agent
from src.common.lifespan.db_client import db_client

# 构建 prompt 文件夹的路径
current_dir = Path(__file__).resolve().parent.parent
prompt_dir = current_dir / "prompts"


async def create_talk(
    user_id: str,
    talk_type: str,
    extra_body: dict = None,
):
    try:
        extra_body = extra_body or {}

        now_time = datetime.now()
        try:
            talk_info = {
                "user_id": user_id,
                "talk_name": "",
                "talk_type": talk_type,
                "status": 0,
                "create_time": now_time,
                "update_time": now_time,
                **extra_body,
            }
            result = await db_client["mongo_col"].talk_records.insert_one(talk_info)
        except PyMongoError as e:
            logger.error(return_error(code=e.status_code, e=e))
            return return_error(code=2100, e=e)
        if not result.inserted_id:
            logger.warning(return_error(code=e.status_code))
            return return_error(code=2100)

        return {
            "code": 200,
            "data": {"talk_id": str(result.inserted_id)},
            "msg": "成功创建对话",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def delete_talk(user_id: str, talk_id: str):
    """对话记录: 删除"""
    try:
        object_id = convert_to_object_id(talk_id)

        # 删除 talk_records 集合中的记录
        try:
            result = await db_client["mongo_col"].talk_records.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": {"status": 1}},
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=e.status_code, e=e))
            return return_error(code=2300, e=e)
        if not result.matched_count > 0:
            logger.warning(return_error(code=e.status_code))
            return return_error(code=2301)
        if not result.modified_count > 0:
            logger.warning(return_error(code=e.status_code))
            return return_error(code=2300)

        # 删除 qa_records 集合中的记录
        try:
            result = await db_client["mongo_col"].qa_records.update_many(
                {"user_id": user_id, "talk_id": talk_id, "status": 0},
                {"$set": {"status": 1}},
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=e.status_code, e=e))
            return return_error(code=2300, e=e)

        return {
            "code": 200,
            "data": {"talk_id": talk_id},
            "msg": "成功删除对话",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def update_talk_name(user_id: str, talk_id: str, talk_name: str):
    """对话记录: 修改名称"""
    try:
        object_id = convert_to_object_id(talk_id)

        try:
            result = await db_client["mongo_col"].talk_records.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": {"talk_name": talk_name, "update_time": datetime.now()}},
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not result.matched_count > 0:
            return return_error(code=2201)
        if not result.modified_count > 0:
            return return_error(code=2202)

        return {
            "code": 200,
            "data": {"talk_id": talk_id},
            "msg": "成功修改对话名称",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_talk_records(
    user_id: str,
    talk_types: list,
    key_word: str,
    page_number: int,
    page_size: int,
    enable_page: bool = True,
    group: bool = True,
    extra_body: dict = None,
):
    """对话记录: 查询"""
    try:
        extra_body = extra_body or {}

        condition = {
            "user_id": user_id,
            "status": 0,
        }
        if key_word:
            condition["talk_name"] = {"$regex": key_word, "$options": "i"}
        if talk_types:
            condition["talk_type"] = {"$in": talk_types}

        # 处理 extra_body
        for key, value in extra_body.items():
            condition[key] = value

        try:
            query = (
                db_client["mongo_col"]
                .talk_records.find(condition)
                .sort({"update_time": -1})
            )

            # 分页
            if enable_page:
                skip_count = (page_number - 1) * page_size
                query = query.skip(skip_count).limit(page_size)

            # 获取数据
            talk_records = await query.to_list(length=None)
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        for item in talk_records:
            item["talk_id"] = str(item.pop("_id"))

        if group:
            talk_records = group_talk_records_by_time(talk_records)

        return {
            "code": 200,
            "data": {"talk_records": talk_records},
            "msg": "成功查询到对话历史",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


def group_talk_records_by_time(
    tmp_talk_records: List[Dict],
) -> Dict[str, Dict[str, List[Dict]]]:
    """
    将 `tmp_talk_records` 按今天、昨天、前7天、前30天、其他（按月份）进行时间分组。

    Args:
        tmp_talk_records (List[Dict]): 记录列表，每个记录应包含 `update_time` 字段。

    Returns:
        Dict[str, Dict[str, List[Dict]]]: 返回按时间段分组的记录字典。
    """
    # 获取当前时间
    now_time = datetime.now()

    # 计算不同时间段的日期
    today_start = datetime(now_time.year, now_time.month, now_time.day, 0, 0, 0)
    yesterday_start = today_start - timedelta(days=1)
    seven_days_ago = now_time - timedelta(days=7)
    thirty_days_ago = now_time - timedelta(days=30)

    # 按时间段分组
    time_groups = {
        "today": [],
        "yesterday": [],
        "last_7_days": [],
        "last_30_days": [],
        "other": {},
    }

    # 遍历记录，按时间段分组
    for record in tmp_talk_records:
        update_time = record.get("update_time")

        # 处理成 str
        record["create_time"] = record.get("create_time", datetime.now()).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        record["update_time"] = record.get("update_time", datetime.now()).strftime(
            "%Y-%m-%d %H:%M:%S"
        )

        if update_time >= today_start:
            time_groups["today"].append(record)
        elif update_time >= yesterday_start:
            time_groups["yesterday"].append(record)
        elif update_time >= seven_days_ago:
            time_groups["last_7_days"].append(record)
        elif update_time >= thirty_days_ago:
            time_groups["last_30_days"].append(record)
        else:
            # 其他情况下按月份归类
            month_key = update_time.strftime("%Y-%m")  # 按年-月格式
            if month_key not in time_groups["other"]:
                time_groups["other"][month_key] = []
            time_groups["other"][month_key].append(record)

    return time_groups


async def fetch_qa_records(user_id: str, talk_id: str):
    """问答记录：查询"""
    try:

        try:
            qa_list = (
                await db_client["mongo_col"]
                .qa_records.find({"user_id": user_id, "talk_id": talk_id, "status": 0})
                .to_list(None)
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        for item in qa_list:
            item["qa_id"] = str(item.pop("_id"))

        return {
            "code": 200,
            "data": {"qa_list": qa_list},
            "msg": "成功查询问答历史",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.aagent.agent.aagent import AAgent
from src.aagent.utils.load_prompt import load_prompt
from src.common.utils.msg_utils import make_history
from src.ai_chat_data.services.chat_data_service import _get_data_ids_set_list
from src.ai_data_source.services.data_source_service import fetch_data_source_dy_id
from src.sql_engine.utils.db_mschema import MSchema
from pydantic import BaseModel


class QuestionsSuggest(BaseModel):
    question_1: str
    question_2: str
    question_3: str


async def questions_suggester(
    user_id: str,
    data_ids: list,
    dir_ids: list,
    talk_id: str,
    qa_list: list,
):
    try:
        data_list = await _get_data_ids_set_list(user_id, dir_ids, data_ids)
        current_time = get_current_time_with_weekday()
            
        if qa_list and len(data_list) == 1:

            data_id = data_list[0]

            data_info_res = await fetch_data_source_dy_id(
                user_id=user_id, data_id=data_id, mschema=True
            )
            if data_info_res["code"] != 200:
                raise HTTPException(
                    status_code=data_info_res["code"],
                    detail=data_info_res["msg"],
                )
            data_info = data_info_res["data"]["data_info"]

            last_msgs = qa_list[-1]["message"]
            selected_tables = None
            selected_columns = []
            for msg in last_msgs.values():
                if msg["name"] == "FT_agent":
                    selected_tables = (
                        msg["metadata"]
                        .get("selected_tables", {})
                        .get("data", {})
                        .get("selected_tables", [])
                    )
                    selected_columns = None
                    break

            data_mschema_dict = data_info.get("mschema", {})
            data_mschema = MSchema()
            data_mschema.load_from_dict(data_mschema_dict)
            table_mschema = data_mschema.to_mschema(
                selected_tables=selected_tables, selected_columns=selected_columns
            )

            # 仅保留最近 history_num 个 qa
            history_num = config_data.get("history_num", 1)
            qa_list = qa_list[-int(history_num) :]

            history = await make_history(
                talk_id=talk_id,
                qa_list=qa_list,
                exclude_agent=["FT_agent", "FD_agent"],
                check_talk_id=False,
            )

            # 读取 system prompt
            sys_prompt = await load_prompt(path=prompt_dir, name="base_sys")
            user_prompt = await load_prompt(path=prompt_dir, name="suggest_sys")
            json_schema = QuestionsSuggest.model_json_schema()

            user_prompt = user_prompt.format(
                data_info=table_mschema,
                history=str(history),
                current_time=current_time,
            )
            messages = [{"role": "user", "content": user_prompt}]

            suggest_agent = AAgent(
                name="SA_agent",
                description="问题推荐",
                system=sys_prompt,
                model_config=config_data["llm"],
            )

            resp = await suggest_agent.run_nonstream(
                messages=messages,
                extra_body={"guided_json": json_schema},
            )

            raw_ans = resp["ans"]["content"]

            # _, block_sccess = await ej_agent.get_block(raw_ans)
            # if block_sccess:
            json_dict, json_sccess = await ej_agent.get_json(raw_ans)
            if json_sccess:
                return {
                    "code": 200,
                    "data": {"suggest_questions": json_dict},
                    "msg": "成功进行问题推荐",
                }

        return {
            "code": 200,
            "data": {"suggest_questions": None},
            "msg": "无法进行问题推荐",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
