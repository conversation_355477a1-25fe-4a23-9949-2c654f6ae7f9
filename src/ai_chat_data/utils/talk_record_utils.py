import asyncio

from datetime import datetime
from fastapi import HTTPException
from pymongo.errors import PyMongoError

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.object_id import convert_to_object_id
from src.common.lifespan.db_client import db_client
from src.common.utils.error_codes import ERROR_CODES


async def update_talk_record(user_id, data_ids, dir_ids, talk_id):
    """更新对话记录 update_time"""
    talk_object_id = convert_to_object_id(talk_id)

    updata_data = {
        "update_time": datetime.now(),
        "data_ids": data_ids,
        "dir_ids": dir_ids,
    }
    try:
        result = await db_client["mongo_col"].talk_records.update_one(
            {"user_id": user_id, "_id": talk_object_id, "status": 0},
            {"$set": updata_data},
            upsert=False,
        )
    except PyMongoError as e:
        raise HTTPException(status_code=2200, detail=e)
    if not result.matched_count > 0:
        raise HTTPException(status_code=2201, detail=ERROR_CODES[2201])
    if not result.modified_count > 0:
        raise HTTPException(status_code=2202, detail=ERROR_CODES[2202])


async def update_data_used_time(user_id: str, data_id: str, set_time: datetime = None):
    """数据源: 更新使用时间"""
    object_id = convert_to_object_id(data_id)
    now_time = set_time or datetime.now()

    try:
        # 更新 MongoDB
        result = await db_client["mongo_col"].data_info.update_one(
            {"user_id": user_id, "_id": object_id},
            {"$set": {"last_used_time": now_time}},
            upsert=False,
        )
    except PyMongoError as e:
        raise HTTPException(status_code=2200, detail=e)
    if not result.matched_count > 0:
        raise HTTPException(status_code=2201, detail=ERROR_CODES[2201])
    if not result.modified_count > 0:
        raise HTTPException(status_code=2202, detail=ERROR_CODES[2202])
