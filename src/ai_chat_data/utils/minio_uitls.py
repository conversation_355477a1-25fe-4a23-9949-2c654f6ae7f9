import io
import os
import urllib
import pathlib

from datetime import datetime
from fastapi import HTTPException

from src.common.utils.logger import logger
from src.common.utils.config import config_data

from src.common.lifespan.db_client import db_client


async def save_file_to_minio(
    file_name: str,
    file_content: bytes,
    prefix_path: str = None,
    is_suffix_path: bool = False,
):
    """保存文档至minio"""

    # 构造上传路径
    if is_suffix_path:
        suffix_path = datetime.now().strftime("%Y-%m-%d-%H-%M-%S-%f")
        file_name = f"{pathlib.Path(file_name).stem}_{suffix_path}{pathlib.Path(file_name).suffix}"

    upload_name = os.path.join(prefix_path, file_name) if prefix_path else file_name

    try:
        # 获取 MinIO 配置
        minio_config = config_data["minio"]
        
        bucket_name = minio_config["bucket_name"]
        endpoint = minio_config["endpoint"]

        # 上传文件
        with io.BytesIO(file_content) as data:
            db_client["minio_client"].put_object(
                bucket_name=bucket_name,
                object_name=upload_name,
                data=data,
                length=len(file_content),
                content_type="application/octet-stream",
            )

        file_url = f"http://{endpoint}/{bucket_name}/{urllib.parse.quote(upload_name)}"
        return file_url

    except Exception as e:
        raise HTTPException(status_code=2100, detail=e)
