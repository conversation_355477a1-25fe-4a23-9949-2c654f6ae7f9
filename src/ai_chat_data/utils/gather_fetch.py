import asyncio

from pymongo.errors import PyMongoError

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.object_id import convert_to_object_id
from src.common.utils.error_codes import return_error
from src.common.lifespan.db_client import db_client

from src.ai_data_source.services.data_source_service import fetch_dir_sub_id

async def gather_datas_from_dirs(user_id: str, dir_ids: list):
    """多 dir_ids 并发查询"""
    tasks = [
        fetch_dir_sub_id(user_id=user_id, dir_id=dir_id, recursive=True)
        for dir_id in dir_ids
    ]
    results = await asyncio.gather(*tasks)
    return [data_id for sub in results for data_id in sub["data"]["data_ids"]]


async def get_data_list_configs(user_id: str, data_ids: list):
    """多 data_ids 并发查询"""
    object_id_list = [convert_to_object_id(data_id) for data_id in data_ids]

    # 查询 MongoDB
    try:
        data_config_list = (
            await db_client["mongo_col"]
            .data_info.find(
                {"user_id": user_id, "_id": {"$in": object_id_list}, "status": 0}
            )
            .to_list(None)
        )
    except PyMongoError as e:
        return return_error(code=2400, e=e)

    for item in data_config_list:
        item["data_id"] = str(item.pop("_id"))

    return data_config_list
