import sqlparse
import regex

from fastapi import HTTPException
from pymongo.errors import PyMongoError
from pymilvus.exceptions import MilvusException
from src.common.utils.error_codes import ERROR_CODES

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.embedding import text_to_dense_vector
from src.common.utils.object_id import convert_to_object_id

from src.common.lifespan.db_client import db_client

from pymilvus import (
    AnnSearchRequest,
    RRFRanker,
    WeightedRanker,
)


def normalize_sql(sql: str) -> str:
    """
    格式化 SQL 语句：重新缩进、统一关键字为大写、移除多余空格
    """
    formatted_sql = sqlparse.format(sql, reindent=True, keyword_case="upper")
    return formatted_sql.strip()


def compare_sql_to_list(sql: str, sql_list: list) -> bool:
    """
    比较一个 SQL 语句与 SQL 列表中的每个语句是否相同（格式化后）
    """
    try:
        norm_sql = normalize_sql(sql)
        return any(norm_sql == normalize_sql(item) for item in sql_list)
    except:
        # 报错意味着无需运行
        return True


def rrf_scores(dict_list):
    final_scores = {}
    for d in dict_list:
        for item in d:
            if item not in final_scores:
                final_scores[item] = 0
            if score := rrf_index(item, d):
                final_scores[item] += 1 / (60 + score)

    # 按分数降序排序，并转换为列表
    sorted_scores = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)

    return sorted_scores


def rrf_index(key, dict):
    # 使用字典的 keys() 获取键的列表，查找 key 的索引
    try:
        return list(dict.keys()).index(key) + 1
    except ValueError:
        return None


async def filter_tables(user_id, user_ask, data_id, filter_config=None):
    filter_config = filter_config or {}
    searched_tables = await table_search(
        user_id=user_id,
        data_id=data_id,
        query=user_ask,
        k=filter_config.get("table_emb_num", 5),
    )
    searched_columns = await column_search(
        user_id=user_id,
        data_id=data_id,
        query=user_ask,
        k=filter_config.get("column_emb_num", 30),
    )
    searched_bizs = await biz_search(
        user_id=user_id,
        data_id=data_id,
        query=user_ask,
        k=filter_config.get("biz_emb_num", 1),
    )

    # 将所有的表放入字典
    st_dict = {}
    for item in searched_tables:
        st_dict[item["entity"]["table_name"]] = item["distance"]

    sc_dict = {}
    for item in searched_columns:
        sc_dict[item["entity"]["table_name"]] = item["distance"]

    # sb_dict = {}
    # for item in searched_bizs:
    #     for biz_tab in item["entity"]["table_names"]:
    #         sb_dict[biz_tab] = item["distance"]

    # 计算 rrf 分数
    final_num = filter_config.get("final_num", 3)
    # final_score = rrf_scores([st_dict, sc_dict, sb_dict])
    final_score = rrf_scores([st_dict, sc_dict])  # biz 中的表全部添加
    table_list = [item[0] for item in final_score[:final_num]]

    # 添加 biz 中的表
    table_list = list(
        set(table_list)
        | {table for item in searched_bizs for table in item["entity"]["table_names"]}
    )

    logger.info(
        f"{user_id:<15} | table_list | {table_list}"
    )
    return table_list


async def table_search(
    user_id: str,
    data_id: str,
    query: str,
    dense_vec: list = None,
    k: int = 10,
    mode="hybrid",
):
    milvus_client = db_client["milvus_client"]
    table_info_col = config_data["milvus"]["col_list"]["table_info"]

    filter_expr = f'user_id == "{user_id}" AND data_id == "{data_id}"'

    output_fields = [
        "table_name",
    ]

    if mode in ["dense", "hybrid"]:
        if not dense_vec:
            embedding = await text_to_dense_vector([query])
            if isinstance(embedding, dict) and "dense" in embedding:
                dense_vec = embedding["dense"][0]
            else:
                dense_vec = embedding[0]

    if mode == "sparse":
        results = milvus_client.search(
            collection_name=table_info_col,
            data=[query],
            anns_field="sparse_vector",
            limit=k,
            filter=filter_expr,
            output_fields=output_fields,
        )
    elif mode == "dense":
        results = milvus_client.search(
            collection_name=table_info_col,
            data=[dense_vec],
            anns_field="dense_vector",
            limit=k,
            filter=filter_expr,
            output_fields=output_fields,
        )
    elif mode == "hybrid":
        full_text_search_params = {"metric_type": "BM25"}
        full_text_search_req = AnnSearchRequest(
            [query],
            "sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )

        dense_search_params = {"metric_type": "IP"}
        dense_req = AnnSearchRequest(
            [dense_vec],
            "dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        results = await milvus_client.hybrid_search(
            table_info_col,
            [full_text_search_req, dense_req],
            ranker=WeightedRanker(0.4, 0.6),
            limit=k,
            output_fields=output_fields,
        )
    else:
        raise ValueError("Invalid mode")

    return results[0]


async def column_search(
    user_id: str,
    data_id: str,
    query: str,
    dense_vec: list = None,
    k: int = 10,
    mode="hybrid",
):
    milvus_client = db_client["milvus_client"]
    column_info_col = config_data["milvus"]["col_list"]["column_info"]

    filter_expr = f'user_id == "{user_id}" AND data_id == "{data_id}"'

    output_fields = [
        "table_name",
    ]

    if mode in ["dense", "hybrid"]:
        if not dense_vec:
            embedding = await text_to_dense_vector([query])
            if isinstance(embedding, dict) and "dense" in embedding:
                dense_vec = embedding["dense"][0]
            else:
                dense_vec = embedding[0]

    if mode == "sparse":
        results = milvus_client.search(
            collection_name=column_info_col,
            data=[query],
            anns_field="sparse_vector",
            limit=k,
            filter=filter_expr,
            output_fields=output_fields,
        )
    elif mode == "dense":
        results = milvus_client.search(
            collection_name=column_info_col,
            data=[dense_vec],
            anns_field="dense_vector",
            limit=k,
            filter=filter_expr,
            output_fields=output_fields,
        )
    elif mode == "hybrid":
        full_text_search_params = {"metric_type": "BM25"}
        full_text_search_req = AnnSearchRequest(
            [query],
            "sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )

        dense_search_params = {"metric_type": "IP"}
        dense_req = AnnSearchRequest(
            [dense_vec],
            "dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        results = await milvus_client.hybrid_search(
            column_info_col,
            [full_text_search_req, dense_req],
            ranker=WeightedRanker(0.3, 0.4),
            limit=k,
            output_fields=output_fields,
        )
    else:
        raise ValueError("Invalid mode")

    return results[0]


async def biz_search(
    user_id: str,
    data_id: str,
    query: str,
    dense_vec: list = None,
    k: int = 10,
    mode="hybrid",
):
    milvus_client = db_client["milvus_client"]
    biz_relation_col = config_data["milvus"]["col_list"]["biz_relation"]

    filter_expr = f'user_id == "{user_id}" AND data_id == "{data_id}"'

    output_fields = [
        "biz_id",
    ]

    if mode in ["dense", "hybrid"]:
        if not dense_vec:
            embedding = await text_to_dense_vector([query])
            if isinstance(embedding, dict) and "dense" in embedding:
                dense_vec = embedding["dense"][0]
            else:
                dense_vec = embedding[0]

    if mode == "sparse":
        full_text_search_params = {"metric_type": "BM25"}
        ask_search_req = AnnSearchRequest(
            [query],
            "ask_sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )
        desc_search_req = AnnSearchRequest(
            [query],
            "desc_sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )

        results = await milvus_client.hybrid_search(
            biz_relation_col,
            [ask_search_req, desc_search_req],
            ranker=RRFRanker(),
            limit=k,
            output_fields=output_fields,
        )

    elif mode == "dense":
        dense_search_params = {"metric_type": "IP"}
        ask_dense_req = AnnSearchRequest(
            [dense_vec],
            "ask_dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        desc_dense_req = AnnSearchRequest(
            [dense_vec],
            "desc_dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        results = await milvus_client.hybrid_search(
            biz_relation_col,
            [ask_dense_req, desc_dense_req],
            ranker=RRFRanker(),
            limit=k,
            output_fields=output_fields,
        )
    elif mode == "hybrid":
        full_text_search_params = {"metric_type": "BM25"}
        ask_search_req = AnnSearchRequest(
            [query],
            "ask_sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )
        desc_search_req = AnnSearchRequest(
            [query],
            "desc_sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )

        dense_search_params = {"metric_type": "IP"}
        ask_dense_req = AnnSearchRequest(
            [dense_vec],
            "ask_dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        desc_dense_req = AnnSearchRequest(
            [dense_vec],
            "desc_dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        results = await milvus_client.hybrid_search(
            biz_relation_col,
            [ask_search_req, desc_search_req, ask_dense_req, desc_dense_req],
            ranker=RRFRanker(),
            limit=k,
            output_fields=output_fields,
        )
    else:
        raise RecursionError("Invalid mode")

    # 从 mongo 中取出table name
    result = results[0]

    for item in result:
        # 查询 MongoDB
        object_id = convert_to_object_id(item["biz_id"])
        try:
            biz_info = await db_client["mongo_col"].biz_relation.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError:
            raise HTTPException(status_code=2400, detail=ERROR_CODES[2400])
        if not biz_info:
            raise HTTPException(status_code=2401, detail=ERROR_CODES[2401])

        item["entity"]["table_names"] = biz_info["table_names"]

    return result


async def sql_few_shot_search(
    user_id: str,
    data_id: str,
    query: str,
    example_type: str = "sql",
    dense_vec: list = None,
    k: int = 3,
    mode="hybrid",
):
    milvus_client = db_client["milvus_client"]
    few_shot_col = config_data["milvus"]["col_list"]["few_shot"]

    filter_expr = f'user_id == "{user_id}"  AND example_type == "{example_type}" AND ARRAY_CONTAINS(data_ids, "{data_id}")'

    output_fields = [
        "few_shot_id",
    ]

    if mode in ["dense", "hybrid"]:
        if not dense_vec:
            embedding = await text_to_dense_vector([query])
            if isinstance(embedding, dict) and "dense" in embedding:
                dense_vec = embedding["dense"][0]
            else:
                dense_vec = embedding[0]
    if mode == "sparse":
        full_text_search_params = {"metric_type": "BM25"}
        ask_search_req = AnnSearchRequest(
            [query],
            "ask_sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )
        example_search_req = AnnSearchRequest(
            [query],
            "example_sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )

        results = await milvus_client.hybrid_search(
            few_shot_col,
            [ask_search_req, example_search_req],
            ranker=WeightedRanker(0.6, 0.4),
            limit=k,
            output_fields=output_fields,
        )
    elif mode == "dense":
        dense_search_params = {"metric_type": "IP"}
        ask_dense_req = AnnSearchRequest(
            [dense_vec],
            "ask_dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        example_dense_req = AnnSearchRequest(
            [dense_vec],
            "example_dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        results = await milvus_client.hybrid_search(
            few_shot_col,
            [ask_dense_req, example_dense_req],
            ranker=WeightedRanker(0.6, 0.4),
            limit=k,
            output_fields=output_fields,
        )
    elif mode == "hybrid":
        full_text_search_params = {"metric_type": "BM25"}
        ask_search_req = AnnSearchRequest(
            [query],
            "ask_sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )
        example_search_req = AnnSearchRequest(
            [query],
            "example_sparse_vector",
            full_text_search_params,
            limit=k,
            expr=filter_expr,
        )

        dense_search_params = {"metric_type": "IP"}
        ask_dense_req = AnnSearchRequest(
            [dense_vec],
            "ask_dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        example_dense_req = AnnSearchRequest(
            [dense_vec],
            "example_dense_vector",
            dense_search_params,
            limit=k,
            expr=filter_expr,
        )

        results = await milvus_client.hybrid_search(
            few_shot_col,
            [ask_search_req, example_search_req, ask_dense_req, example_dense_req],
            ranker=WeightedRanker(0.2, 0.2, 0.3, 0.3),
            limit=k,
            output_fields=output_fields,
        )
    else:
        raise ValueError("Invalid mode")

    result = results[0]
    object_ids = [convert_to_object_id(item["few_shot_id"]) for item in result]

    try:
        few_shot_info = (
            await db_client["mongo_col"]
            .few_shot.find(
                {"user_id": user_id, "_id": {"$in": object_ids}, "status": 0}
            )
            .to_list(None)
        )
    except PyMongoError:
        raise HTTPException(status_code=2400, detail=ERROR_CODES[2400])

    return few_shot_info


async def get_jargon_infos(user_id: str, data_id: str, user_ask: str):
    """过滤相关的知识库条目"""
    jargon_info_res = await fetch_jargon_by_data_id(user_id, data_id)
    if jargon_info_res["code"] != 200:
        raise HTTPException(
            status_code=jargon_info_res["code"], detail=jargon_info_res["msg"]
        )

    jargon_infos = jargon_info_res["data"]["jargon_infos"]

    re_user_ask = user_ask
    filtered_jargon_infos = []

    if jargon_infos:
        for jargon_info in jargon_infos:

            for word in list(
                set(jargon_info["similar_words"] + [jargon_info["jargon_name"]])
            ):
                if word in user_ask:
                    if jargon_info["is_rewrite"]:
                        re_user_ask = re_user_ask.replace(
                            word, jargon_info["jargon_description"]
                        )
                    else:
                        filtered_jargon_infos.append(
                            {
                                "业务名称": jargon_info["jargon_name"],
                                "业务解释": jargon_info["jargon_description"],
                                "同义词": jargon_info["similar_words"],
                            }
                        )

    return filtered_jargon_infos, re_user_ask


async def fetch_jargon_by_data_id(user_id: str, data_id: str):
    """查询 user_id 下所有包含 data_id 的条目"""

    try:
        # 查询 MongoDB
        jargon_infos = (
            await db_client["mongo_col"]
            .jargon_info.find(
                {
                    "user_id": user_id,
                    "data_ids": data_id,
                    "status": 0,
                }
            )
            .to_list(None)
        )

        for item in jargon_infos:
            # 将 _id 转换为字符串
            item["jargon_id"] = str(item.pop("_id"))
            del item["create_time"]
            del item["update_time"]

        return {
            "code": 200,
            "data": {"jargon_infos": jargon_infos},
            "msg": "成功查询知识库条目",
        }

    except Exception as e:
        return {
            "code": 500,
            "data": None,
            "msg": str(e),
        }


def match_metadata(temp_text: str, flag: tuple):
    metadata = before_text = after_text = ""

    if temp_text:
        if len(flag) != 2:
            raise ValueError(f"{flag} 必须为一个长度为 2 的元组")

        match_start = regex.search(flag[0], temp_text)
        if match_start:
            # 提取 flag 开始标记 之前的文本
            before_text = temp_text[: match_start.start()]

            after_before_text = temp_text[match_start.end() :]

            # 查找 flag 结束标记 的位置
            match_end = regex.search(flag[1], after_before_text)
            if match_end:
                # 提取 flag[0] 和 flag[1] 之间的内容
                metadata = temp_text[
                    match_start.end() : match_start.end() + match_end.start()
                ]
                after_text = temp_text[match_start.end() + match_end.end() :]
            else:
                # 如果没有 </tool_call>，则取 <tool_call> 后的所有内容
                metadata = temp_text[match_start.end() :]
                after_text = ""
        else:
            # 如果没有匹配到任何标记
            before_text = temp_text
            after_text = ""

    return {
        "metadata": metadata,
        "before_text": before_text,
        "after_text": after_text,
    }
