from typing import *
from enum import Enum
from pydantic import BaseModel, Field


from src.ai_data_source.schemas.data_source_schema import (
    Page,
    KeyWord,
    DataDirIDs,
    BaseResponse,
    BaseMetaInfo,
)


class UserAsk(BaseModel):
    user_ask: str = Field(
        ...,
        title="用户提问",
        description="用户的问题内容",
        examples=[
            "我想要看看所有在过去两年销量超过5的产品",
            "请列出哪些客户最活跃，并给出他们的详细信息",
            "统计一下近两年每个季度每个类别的总销售额",
            "列出所有已发货订单的详细信息",
            "显示在2023-10月之前加入的客户名单",
            "我要看今年每个月的销售总额",
            "找出购买次数最多的前 10 名客户",
            "2023年每个月数码产品类的订单有多少",
            "2023年，箱包配饰类订单数最多的月份是哪个月",
            "请列出哪些产品最受欢迎",
        ],
    )


class QAID(BaseModel):
    qa_id: str | None = Field(
        None,
        title="问答记录ID",
        description="问答记录的唯一标识符",
    )


class QAList(BaseModel):
    qa_list: List[dict] | None = Field([], title="问答列表", description="记录的问答对")


class TalkID(BaseModel):
    talk_id: str | None = Field(
        None,
        title="对话 ID",
        description="对话的唯一标识符",
    )


class TalkName(BaseModel):
    talk_name: str | None = Field(
        "新对话", title="对话名称", description="对话的名称", examples=["测试名称"]
    )


class TalkTypeEnum(str, Enum):
    CHAT_DATA = "chat_data"
    CHAT_DASHBOARD = "chat_dashboard"
    CHAT_REPORT = "chat_report"


class TalkType(BaseModel):
    talk_type: TalkTypeEnum = Field(
        ...,
        title="对话类型",
        description="对话的类型",
        examples=["chat_data", "chat_dashboard", "chat_report"],
    )


class TalkTypes(BaseModel):
    talk_types: List[TalkTypeEnum] = Field(
        ["chat_data", "chat_dashboard"],
        title="对话类型列表",
        description="可选的对话类型列表",
        examples=[["chat_data", "chat_dashboard"]],
    )


class ExtraBody(BaseModel):
    extra_body: dict | None = Field(
        {},
        title="额外参数",
        description="需要CRUD的额外参数",
        examples=[{"report_id": "67e2703743e482eb4b327538"}],
    )


class NeedGroup(BaseModel):
    group: bool = Field(True, title="分组标志", description="是否进行分组，默认为 True")


class TalkMateInfo(BaseMetaInfo, TalkID, TalkName, TalkType, DataDirIDs):
    pass


class TalkGroupInfos(BaseModel):
    today: List[TalkMateInfo] = Field(
        [], title="今天的记录", description="今天的对话记录"
    )
    yesterday: List[TalkMateInfo] = Field(
        [], title="昨天的记录", description="昨天的对话记录"
    )
    last_7_days: List[TalkMateInfo] = Field(
        [], title="最近 7 天的记录", description="最近 7 天的对话记录"
    )
    last_30_days: List[TalkMateInfo] = Field(
        [], title="最近 30 天的记录", description="最近 30 天的对话记录"
    )
    other: Dict[str, TalkMateInfo] = Field(
        {}, title="其他记录", description="其他时间段的对话记录"
    )


class TalkRecordInfos(BaseModel):
    talk_records: TalkGroupInfos = Field(
        ..., title="分组记录", description="分组后的对话记录"
    )


################################################################################


class CreateTalk(TalkType, ExtraBody):
    pass


class DeleteTalk(TalkID):
    pass


class UpdateTalkName(TalkID, TalkName):
    pass


class FetchTalkRecords(TalkTypes, KeyWord, Page, NeedGroup, ExtraBody):
    pass


class FetchQARecords(TalkID):
    pass


class QuestionsSuggester(TalkID, DataDirIDs, QAList):
    pass


################################################################################


class CreateTalkResponse(BaseResponse):
    data: TalkID | None = Field(..., title="数据", description="具体内容")


class DeleteTalkResponse(BaseResponse):
    data: TalkID | None = Field(..., title="数据", description="具体内容")


class UpdateTalkNameResponse(BaseResponse):
    data: TalkID | None = Field(..., title="数据", description="具体内容")


class FetchTalkRecordsResponse(BaseResponse):
    data: TalkRecordInfos | None = Field(..., title="数据", description="具体内容")


class UpdateTalkNameResponse(BaseResponse):
    data: TalkID | None = Field(..., title="数据", description="具体内容")


class FetchQARecordsResponse(BaseResponse):
    data: QAList | None = Field(..., title="数据", description="具体内容")
