import re

from src.common.utils.object_id import convert_to_object_id
from src.common.lifespan.db_client import db_client

from pymongo.errors import PyMongoError
from src.common.utils.error_codes import return_error


async def check_data_info_exists(user_id: str, data_id: str) -> bool:
    """查询文档是否存在"""
    object_id = convert_to_object_id(data_id)

    result = await db_client["mongo_col"].data_info.find_one(
        {"user_id": user_id, "_id": object_id, "status": 0}
    )
    if result:
        return True
    else:
        return False


async def get_data_names_by_ids(data_ids: list):
    """根据data_id获取data name，并返回 new_data_ids"""
    object_data_ids = [convert_to_object_id(data_id) for data_id in data_ids]

    try:
        data_list = (
            await db_client["mongo_col"]
            .data_info.find(
                {"_id": {"$in": object_data_ids}, "status": 0},
                {"data_config.data_name": 1},
            )
            .to_list(None)
        )

    except PyMongoError as e:
        return return_error(code=2400, e=e)

    for item in data_list:
        item["data_id"] = str(item.pop("_id"))
        item["data_name"] = str(item.get("data_config", {}).pop("data_name", ""))

    # 存在的data_id集合
    existing_data_ids = {item["data_id"] for item in data_list}

    # 找到不存在的 data_id
    delete_data_ids = [
        data_id for data_id in data_ids if data_id not in existing_data_ids
    ]

    # 返回时，如果有要删除的ID，构建新的data_id列表
    new_data_ids = list(existing_data_ids) if delete_data_ids else None

    return data_list, new_data_ids
