import asyncio

from fastapi import HTTPException
from pymilvus.exceptions import MilvusException

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.embedding import text_to_dense_vector
from src.common.utils.error_codes import return_error

from src.common.lifespan.db_client import db_client


def split_data_range(data_range: str):
    start, _, end = data_range.strip().partition("~")
    return start or "", end or ""


async def create_table_data(table_texts, table_infos):
    milvus_client = db_client["milvus_client"]
    table_info_col = config_data["milvus"]["col_list"]["table_info"]

    table_num = 0

    if table_texts:
        batch_size = config_data.get("semaphore", 30)

        for i in range(0, len(table_texts), batch_size):
            batch_table_texts = table_texts[i : i + batch_size]
            batch_table_infos = table_infos[i : i + batch_size]

            table_dense_vector = await text_to_dense_vector(batch_table_texts, 1024)

            # 添加向量
            for j, table_info in enumerate(batch_table_infos):
                table_info["dense_vector"] = table_dense_vector[j]

            # 插入数据到Milvus
            table_res = await milvus_client.insert(table_info_col, batch_table_infos)
            table_num += table_res.get("insert_count", 0)

    return table_num


async def create_column_data(column_texts, column_infos):
    milvus_client = db_client["milvus_client"]
    column_info_col = config_data["milvus"]["col_list"]["column_info"]

    column_num = 0

    if column_texts:
        batch_size = config_data.get("semaphore", 30)

        for i in range(0, len(column_texts), batch_size):
            batch_column_texts = column_texts[i : i + batch_size]
            batch_column_infos = column_infos[i : i + batch_size]

            # 生成列的稠密向量
            column_dense_vector = await text_to_dense_vector(batch_column_texts, 1024)

            # 添加向量
            for j, column_info in enumerate(batch_column_infos):
                column_info["dense_vector"] = column_dense_vector[j]

            # 插入数据到Milvus
            column_res = await milvus_client.insert(column_info_col, batch_column_infos)
            column_num += column_res.get("insert_count", 0)

    return column_num


async def create_milvus_data_info(
    user_id: str, data_id: str, mschema, table_names=None
):
    """数据语义层: 创建"""

    table_infos = []
    table_texts = []
    column_texts = []
    column_infos = []

    # 创建表信息
    mschema_tables = mschema.dump()["tables"]
    for table_name, table_context in mschema_tables.items():
        if table_names is not None and table_name in table_names:
            table_text = mschema.get_mschema_table_info(table_name)
            table_id = f"{user_id}-{data_id}-{table_name}"
            table_infos.append(
                {
                    "table_id": table_id,
                    "user_id": user_id,
                    "data_id": data_id,
                    "table_name": table_name,
                    "text": table_text,
                }
            )
            table_texts.append(table_text)

            # 创建字段信息
            fields = table_context["fields"]
            for field_name, field_context in fields.items():
                column_text = mschema.get_mschema_field_info(field_name, field_context)
                column_id = f"{user_id}-{data_id}-{table_name}-{field_name}"
                column_infos.append(
                    {
                        "column_id": column_id,
                        "user_id": user_id,
                        "data_id": data_id,
                        "table_name": table_name,
                        "column_name": field_name,
                        "text": column_text,
                    }
                )
                column_texts.append(column_text)

    # 统一处理所有数据
    table_num, column_num = await asyncio.gather(
        create_table_data(table_texts, table_infos),
        create_column_data(column_texts, column_infos),
    )

    return table_num, column_num


async def delete_table_data(user_id, data_id, table_names=None):
    milvus_client = db_client["milvus_client"]
    table_info_col = config_data["milvus"]["col_list"]["table_info"]

    table_num = 0
    filter_expr = "user_id == {user_id} AND data_id == {data_id}"
    filter_params = {"user_id": user_id, "data_id": data_id}

    if table_names is not None:
        filter_expr += " AND table_name IN {table_names}"
        filter_params["table_names"] = table_names

    table_delete = await milvus_client.delete(
        collection_name=table_info_col,
        filter=filter_expr,
        filter_params=filter_params,
    )
    table_num = table_delete["delete_count"]

    return table_num


async def delete_column_data(user_id, data_id, table_names=None):
    milvus_client = db_client["milvus_client"]
    column_info_col = config_data["milvus"]["col_list"]["column_info"]

    column_num = 0
    filter_expr = "user_id == {user_id} AND data_id == {data_id}"
    filter_params = {"user_id": user_id, "data_id": data_id}

    if table_names is not None:
        filter_expr += " AND table_name IN {table_names}"
        filter_params["table_names"] = table_names

    column_delete = await milvus_client.delete(
        collection_name=column_info_col,
        filter=filter_expr,
        filter_params=filter_params,
    )
    column_num = column_delete["delete_count"]

    return column_num


async def delete_milvus_data_info(user_id: str, data_id: str):
    """数据语义层: 删除"""

    # 统一处理所有数据
    table_num, column_num = await asyncio.gather(
        delete_table_data(user_id, data_id),
        delete_column_data(user_id, data_id),
    )

    return table_num, column_num


async def fetch_milvus_tables(user_id, data_id):
    milvus_client = db_client["milvus_client"]
    table_info_col = config_data["milvus"]["col_list"]["table_info"]

    table_res = []
    filter_expr = "user_id == {user_id} AND data_id == {data_id}"
    filter_params = {"user_id": user_id, "data_id": data_id}
    table_res = await milvus_client.query(
        collection_name=table_info_col,
        filter=filter_expr,
        filter_params=filter_params,
    )

    return table_res


async def upsert_milvus_data_info(user_id: str, data_id: str, mschema):
    """数据语义层: 同步表"""
    try:
        mschema_tables = mschema.dump()["tables"]

        # 查询数据表信息
        table_res = await fetch_milvus_tables(user_id, data_id)

        mschema_table_names = list(mschema_tables.keys())
        milvus_table_names = [item["table_name"] for item in table_res]

        # 删除的表 和 缺少的表
        tables_to_delete = list(set(milvus_table_names) - set(mschema_table_names))
        tables_to_add = list(set(mschema_table_names) - set(milvus_table_names))

        logger.info(
            f"{user_id:<15} | tables to delete: {tables_to_delete}"
        )
        logger.info(
            f"{user_id:<15} | tables to add: {tables_to_add}"
        )

        if len(tables_to_delete) > 0:
            delete_task = asyncio.gather(
                delete_table_data(user_id, data_id, tables_to_delete),
                delete_column_data(user_id, data_id, tables_to_delete),
            )

        if len(tables_to_add) > 0:
            create_task = asyncio.create_task(
                create_milvus_data_info(user_id, data_id, mschema, tables_to_add)
            )

        table_num = 0
        column_num = 0
        if len(tables_to_delete) > 0:
            delete_res = await delete_task
            delete_table_num = delete_res[0]
            delete_column_num = delete_res[1]

            table_num += delete_table_num
            column_num += delete_column_num

            logger.info(
                f"{user_id:<15} | delete milvus table num: {delete_table_num}"
            )
            logger.info(
                f"{user_id:<15} | delete milvus column num: {delete_column_num}"
            )

        if len(tables_to_add) > 0:
            create_table_num, create_column_num = await create_task
            table_num += create_table_num
            column_num += create_column_num

            logger.info(
                f"{user_id:<15} | create milvus table num: {create_table_num}"
            )
            logger.info(
                f"{user_id:<15} | create milvus column num: {create_column_num}"
            )

        return {
            "code": 200,
            "data": {"table_num": table_num, "column_num": column_num},
            "msg": "成功同步数据表语义",
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
