import os
import sys
import json_repair

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.lifespan.db_client import db_client
from src.common.utils.error_codes import return_error

from datetime import datetime
from fastapi import HTTPException
from pymongo import ReturnDocument
from pymongo.errors import PyMongoError
from pymilvus.exceptions import MilvusException
from src.common.utils.object_id import convert_to_object_id
from src.common.utils.embedding import text_to_dense_vector
from src.ai_data_source.schemas.few_shot_schema import ExampleEnum

async def create_few_shot(
    user_id: str,
    data_ids: list,
    user_ask: str,
    example: str,
    example_type: str = "sql",
    qa_id: str = None,
    talk_id: str = None,
):
    """问答范例管理: 手动创建"""
    try:
        if example_type == ExampleEnum.TEMPLATE:
            # 清理 example
            
            if isinstance(example, str):
                example = json_repair.loads(example)
                
            for line in example:
                for chart in line:
                    chart.pop("key", None)
                    chart.pop("result", None)
                
            example = str(example)
                
                
        # 组装数据以便插入MongoDB
        now_time = datetime.now()
        few_shot_info = {
            "user_id": user_id,
            "data_ids": data_ids,
            "example_type": example_type,
            "example": example,
            "user_ask": user_ask,
            "status": 0,
            "create_time": now_time,
            "update_time": now_time,
        }

        # 将 few_shot_info 保存到 MongoDB
        try:
            result = await db_client["mongo_col"].few_shot.insert_one(few_shot_info)
            few_shot_id = str(result.inserted_id)
        except PyMongoError as e:
            return return_error(code=2100, e=e)
        if not few_shot_id:
            return return_error(code=2100)

        logger.info(
            f"{user_id:<15} | create Mongo data: {few_shot_id}"
        )

        # 保存到 milvus
        milvus_client = db_client["milvus_client"]
        few_shot_col = config_data["milvus"]["col_list"]["few_shot"]

        # 无需避免重复创建
        few_shot = [
            {
                "few_shot_id": few_shot_id,
                "user_id": user_id,
                "data_ids": data_ids,
                "example": example,
                "example_type": example_type,
                "user_ask": user_ask,
            }
        ]

        # 生成稠密向量
        dense_vector = await text_to_dense_vector([user_ask, example], 1024)
        few_shot[0]["ask_dense_vector"] = dense_vector[0]
        few_shot[0]["example_dense_vector"] = dense_vector[1]

        # 插入数据到 Milvus
        try:
            few_shot_res = await milvus_client.insert(few_shot_col, few_shot)
            insert_count = few_shot_res.get("insert_count", 0)
        except MilvusException as e:
            return return_error(code=2100, e=e)
        if not insert_count > 0:
            return return_error(code=2100)

        logger.info(
            f"{user_id:<15} | update Milvus data: {few_shot_id}"
        )

        # 点赞功能
        if qa_id:
            qa_object_id = convert_to_object_id(qa_id)

            updata_data = {
                "update_time": datetime.now(),
                "few_shot_id": few_shot_id,
            }

            try:
                result = await db_client["mongo_col"].qa_records.update_one(
                    {"user_id": user_id, "_id": qa_object_id, "status": 0},
                    {"$set": updata_data},
                    upsert=False,
                )
            except PyMongoError as e:
                return return_error(code=2100, e=e)
            logger.info(
                f"{user_id:<15} | update Mongo data: {qa_id}"
            )
        
        # 仪表盘功能
        if talk_id:
            talk_object_id = convert_to_object_id(talk_id)

            updata_data = {
                "update_time": datetime.now(),
                "few_shot_id": few_shot_id,
            }

            try:
                result = await db_client["mongo_col"].talk_records.update_one(
                    {"user_id": user_id, "_id": talk_object_id, "status": 0},
                    {"$set": updata_data},
                    upsert=False,
                )
            except PyMongoError as e:
                return return_error(code=2100, e=e)
            logger.info(
                f"{user_id:<15} | update Mongo data: {talk_id}"
            )

        return {
            "code": 200,
            "data": {
                "few_shot_id": few_shot_id,
            },
            "msg": "成功创建问答范例",
        }
    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def delete_few_shot(
    user_id: str,
    few_shot_ids: list,
    qa_id: str = None,
    talk_id: str = None,
):
    """问答范例管理: 删除"""
    try:
        object_ids = [convert_to_object_id(few_shot_id) for few_shot_id in few_shot_ids]
        now_time = datetime.now()

        # 批量删除操作
        try:
            result = await db_client["mongo_col"].few_shot.update_many(
                {"user_id": user_id, "_id": {"$in": object_ids}},
                {
                    "$set": {
                        "status": 1,
                        "update_time": now_time,
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2300, e=e)
        if not result.matched_count > 0:
            return return_error(code=2301)
        if not result.modified_count > 0:
            return return_error(code=2300)

        logger.info(
            f"{user_id:<15} | delete Mongo data: {few_shot_ids}"
        )

        # 批量删除 milvus
        milvus_client = db_client["milvus_client"]
        few_shot_col = config_data["milvus"]["col_list"]["few_shot"]

        filter_expr = "user_id == {user_id} AND few_shot_id IN {few_shot_ids}"
        filter_params = {"user_id": user_id, "few_shot_ids": few_shot_ids}

        try:
            few_shot_delete = await milvus_client.delete(
                collection_name=few_shot_col,
                filter=filter_expr,
                filter_params=filter_params,
            )
            delete_count = few_shot_delete["delete_count"]
        except MilvusException as e:
            return return_error(code=2300, e=e)
        if not delete_count > 0:
            return return_error(code=2301)

        if qa_id:
            # 认为只输入了一个 few_shot_ids
            if len(few_shot_ids) != 1:
                return return_error(code=2303)

            qa_object_id = convert_to_object_id(qa_id)
            updata_data = {
                "update_time": datetime.now(),
                "few_shot_id": None,
            }

            try:
                result = await db_client["mongo_col"].qa_records.update_one(
                    {"user_id": user_id, "_id": qa_object_id, "status": 0},
                    {"$set": updata_data},
                    upsert=False,
                )
            except PyMongoError as e:
                return return_error(code=2300, e=e)
            
        if talk_id:
            # 认为只输入了一个 few_shot_ids
            if len(few_shot_ids) != 1:
                return return_error(code=2303)

            talk_object_id = convert_to_object_id(talk_id)
            updata_data = {
                "update_time": datetime.now(),
                "few_shot_id": None,
            }

            try:
                result = await db_client["mongo_col"].talk_records.update_one(
                    {"user_id": user_id, "_id": talk_object_id, "status": 0},
                    {"$set": updata_data},
                    upsert=False,
                )
            except PyMongoError as e:
                return return_error(code=2300, e=e)

        return {
            "code": 200,
            "data": {
                "few_shot_ids": few_shot_ids,
            },
            "msg": "成功删除问答范例",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def upsert_few_shot(
    user_id: str,
    few_shot_id: str,
    data_ids: list | None,
    user_ask: str | None,
    example: str | None,
):
    """问答范例管理: 更新"""
    try:
        object_id = convert_to_object_id(few_shot_id)

        few_shot_info = {
            "user_ask": user_ask,
            "example": example,
            "data_ids": data_ids,
            "update_time": datetime.now(),
        }
        few_shot_info = {
            key: value for key, value in few_shot_info.items() if value is not None
        }

        # 更新 Mongo
        try:
            few_shot_info = await db_client["mongo_col"].few_shot.find_one_and_update(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": few_shot_info},
                return_document=ReturnDocument.AFTER,
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not few_shot_info:
            return return_error(code=2201)

        logger.info(
            f"{user_id:<15} | update Mongo data: {few_shot_id}"
        )

        if user_ask is not None or example is not None or data_ids is not None:
            milvus_client = db_client["milvus_client"]
            few_shot_col = config_data["milvus"]["col_list"]["few_shot"]

            new_user_ask = few_shot_info.get("user_ask", "")
            new_example = few_shot_info.get("example", "")
            new_example_type = few_shot_info.get("example_type", "")
            new_data_ids = few_shot_info.get("data_ids", [])

            few_shot = [
                {
                    "few_shot_id": few_shot_id,
                    "user_id": user_id,
                    "user_ask": new_user_ask,
                    "example": new_example,
                    "example_type": new_example_type,
                    "data_ids": new_data_ids,
                }
            ]

            # 生成稠密向量
            dense_vector = await text_to_dense_vector([new_user_ask, new_example], 1024)
            few_shot[0]["ask_dense_vector"] = dense_vector[0]
            few_shot[0]["example_dense_vector"] = dense_vector[1]

            # 更新数据到 Milvus
            try:
                few_shot_res = await milvus_client.upsert(few_shot_col, few_shot)
                upsert_count = few_shot_res.get("upsert_count", 0)
            except MilvusException as e:
                return return_error(code=2200, e=e)
            if not upsert_count > 0:
                return return_error(code=2201)

            logger.info(
                f"{user_id:<15} | update Milvus data: {few_shot_id}"
            )

        return {
            "code": 200,
            "data": {
                "few_shot_id": few_shot_id,
            },
            "msg": "成功更新问答范例",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.ai_data_source.utils.data_source_utils import get_data_names_by_ids


async def fetch_few_shot(
    user_id: str,
    page_size: int,
    page_number: int,
    key_word: str,
    enable_page: bool,
):
    """问答范例管理: 查询"""
    try:
        condition = {
            "user_id": user_id,
            "status": 0,
        }

        if key_word:
            condition["$or"] = [
                {"user_ask": {"$regex": key_word, "$options": "i"}},
                {"example": {"$regex": key_word, "$options": "i"}},
            ]

        try:
            total_number = await db_client["mongo_col"].few_shot.count_documents(
                condition
            )

            # 构建查询
            query = (
                db_client["mongo_col"]
                .few_shot.find(condition)
                .sort({"update_time": -1})
            )

            # 分页
            if enable_page:
                skip_count = (page_number - 1) * page_size
                query = query.skip(skip_count).limit(page_size)

            # 获取数据
            few_shot_infos = await query.to_list(length=None)
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        for item in few_shot_infos:
            item["few_shot_id"] = str(item.pop("_id"))
            item["create_time"] = item.get("create_time", datetime.now()).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            item["update_time"] = item.get("update_time", datetime.now()).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            # 处理 data_ids

            data_list, new_data_ids = await get_data_names_by_ids(item.pop("data_ids"))
            item["data_list"] = data_list

            if new_data_ids is not None:
                await upsert_few_shot(
                    user_id=user_id,
                    few_shot_id=item["few_shot_id"],
                    data_ids=new_data_ids,
                    user_ask=None,
                    example=None,
                )

        return {
            "code": 200,
            "data": {
                "few_shot_infos": few_shot_infos,
                "total_number": total_number,
            },
            "msg": "成功获取问答范例列表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")

