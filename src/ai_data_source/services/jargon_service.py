import os
import sys

import pandas as pd

from io import BytesIO
from datetime import datetime
from fastapi import HTTPException, UploadFile


from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.lifespan.db_client import db_client
from src.common.utils.error_codes import return_error

from pymongo.errors import PyMongoError
from src.common.utils.object_id import convert_to_object_id
from src.common.utils.file_schema import FileTypeChecker
from src.ai_data_source.utils.data_source_utils import get_data_names_by_ids


async def create_jargon_data(
    user_id: str,
    jargon_name: str,
    jargon_description: str,
    similar_words: list,
    is_rewrite: bool,
    data_ids: list,
):
    """行业惯用语: 新增惯用语
    status 值: 0:活动, 1:已删除
    """
    try:
        # 查询生成范围内是否已经存在相同的行业惯用语
        try:
            existing_jargon = await db_client["mongo_col"].jargon_info.find_one(
                {
                    "user_id": user_id,
                    "jargon_name": jargon_name,
                    "data_ids": {"$in": data_ids},
                    "status": 0,
                }
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if existing_jargon:
            return return_error(code=2101)

        now_time = datetime.now()
        jargon_doc = {
            "user_id": user_id,
            "jargon_name": jargon_name,
            "jargon_description": jargon_description,
            "similar_words": similar_words,
            "is_rewrite": is_rewrite,
            "data_ids": data_ids,
            "status": 0,
            "create_time": now_time,
            "update_time": now_time,
        }

        # 插入数据
        try:
            result = await db_client["mongo_col"].jargon_info.insert_one(jargon_doc)
            jargon_id = str(result.inserted_id)
        except PyMongoError as e:
            return return_error(code=2100, e=e)
        if not jargon_id:
            return return_error(code=2100)

        return {
            "code": 200,
            "data": {"jargon_id": jargon_id},
            "msg": "成功新增行业惯用语",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def delete_jargon_data(
    user_id: str,
    jargon_ids: list,
):
    """行业惯用语: 删除惯用语"""
    try:
        object_ids = [convert_to_object_id(jargon_id) for jargon_id in jargon_ids]
        now_time = datetime.now()

        # 批量删除操作
        try:
            result = await db_client["mongo_col"].jargon_info.update_many(
                {"user_id": user_id, "_id": {"$in": object_ids}},
                {
                    "$set": {
                        "status": 1,
                        "update_time": now_time,
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2300, e=e)
        if not result.matched_count > 0:
            return return_error(code=2301)
        if not result.modified_count > 0:
            return return_error(code=2300)

        return {
            "code": 200,
            "data": {"jargon_ids": jargon_ids},
            "msg": f"成功删除行业惯用语",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def update_jargon_data(
    user_id: str,
    jargon_id: str,
    jargon_name: str | None,
    jargon_description: str | None,
    similar_words: list | None,
    is_rewrite: bool | None,
    data_ids: list | None,
):
    """行业惯用语: 修改惯用语"""
    try:
        jargon_object_id = convert_to_object_id(jargon_id)
        try:
            if data_ids is None:
                existing_jargon_data_ids = await db_client["mongo_col"].jargon_info.find_one(
                    {
                        "user_id": user_id,
                        "_id": jargon_object_id,
                        "status": 0,
                    },
                    {"data_ids": 1}
                )
                data_ids = existing_jargon_data_ids["data_ids"]
        
            existing_jargon = await db_client["mongo_col"].jargon_info.find_one(
                {
                    "user_id": user_id,
                    "_id": {"$ne": jargon_object_id},
                    "jargon_name": jargon_name,
                    "data_ids": {"$in": data_ids},
                    "status": 0,
                }
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if existing_jargon:
            return return_error(code=2101)

        jargon_info = {
            "jargon_name": jargon_name,
            "jargon_description": jargon_description,
            "similar_words": similar_words,
            "data_ids": data_ids,
            "is_rewrite": is_rewrite,
            "update_time": datetime.now(),
        }

        jargon_info = {
            key: value for key, value in jargon_info.items() if value is not None
        }

        # 更新 Mongo
        try:
            result = await db_client["mongo_col"].jargon_info.update_one(
                {"user_id": user_id, "_id": jargon_object_id, "status": 0},
                {"$set": jargon_info},
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not result.matched_count > 0:
            return return_error(code=2201)
        if not result.modified_count > 0:
            return return_error(code=2202)

        return {
            "code": 200,
            "data": {"jargon_id": jargon_id},
            "msg": "成功修改行业惯用语",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def update_jargon_scope(
    user_id: str,
    jargon_ids: list,
    data_ids: list,
):
    """行业惯用语: 批量修改生效范围"""
    try:
        jargon_object_ids = [
            convert_to_object_id(jargon_id) for jargon_id in jargon_ids
        ]

        try:
            existing_jargon_name = (
                await db_client["mongo_col"]
                .jargon_info.find(
                    {
                        "user_id": user_id,
                        "_id": {"$in": jargon_object_ids},
                        "status": 0,
                    },
                    {"jargon_name": 1},
                )
                .to_list(None)
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        existing_jargon_name = [item["jargon_name"] for item in existing_jargon_name]

        # 判断待修改的惯用语之间是否存在重复性
        if len(existing_jargon_name) != len(set(existing_jargon_name)):
            return return_error(code=2101, msg="待修改的惯用语中存在重复的惯用语名称")

        # 判断是否与数据库中名称存在重复
        try:
            existing_jargon_info = (
                await db_client["mongo_col"]
                .jargon_info.find(
                    {
                        "_id": {"$nin": jargon_object_ids},
                        "user_id": user_id,
                        "jargon_name": {"$in": existing_jargon_name},
                        "data_ids": {"$in": data_ids},
                        "status": 0,
                    },
                )
                .to_list(None)
            )
        except PyMongoError as e:
            return return_error(code=2400)
        if existing_jargon_info:
            return return_error(code=2101, msg="待修改的范围中存在重复的惯用语名称")

        # 准备要更新的数据字段
        update_data = {
            "data_ids": data_ids,
            "update_time": datetime.now(),
        }

        try:
            # 执行批量更新操作
            result = await db_client["mongo_col"].jargon_info.update_many(
                {"_id": {"$in": jargon_object_ids}, "user_id": user_id},
                {"$set": update_data},
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not result.matched_count > 0:
            return return_error(code=2201)
        if not result.modified_count > 0:
            return return_error(code=2202)

        return {
            "code": 200,
            "data": {"jargon_ids": jargon_ids},
            "msg": "成功更新惯用语生效范围",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_jargon_info(
    user_id: str,
    page_number: int,
    page_size: int,
    key_word: str,
    enable_page: bool,
    data_ids: list | None,
):
    """惯用语信息查询及关键词模糊检索"""
    try:
        condition = {
            "user_id": user_id,
            "status": 0,
        }
        if data_ids:
            condition["data_ids"] = {"$in": data_ids}
        if key_word:
            condition["$or"] = [
                {"jargon_name": {"$regex": key_word, "$options": "i"}},
                {"jargon_description": {"$regex": key_word, "$options": "i"}},
                {
                    "similar_words": {
                        "$elemMatch": {"$regex": key_word, "$options": "i"}
                    }
                },
            ]

        try:
            # 计算总条数
            total_number = await db_client["mongo_col"].jargon_info.count_documents(
                condition
            )

            query = (
                db_client["mongo_col"]
                .jargon_info.find(condition)
                .sort({"update_time": -1})
            )

            if enable_page:
                skip_count = (page_number - 1) * page_size
                query = query.skip(skip_count).limit(page_size)

            jargon_infos = await query.to_list(length=None)

        except PyMongoError as e:
            return return_error(code=2400, e=e)

        for item in jargon_infos:
            item["jargon_id"] = str(item.pop("_id"))
            item["create_time"] = item.get("create_time", datetime.now()).strftime('%Y-%m-%d %H:%M:%S')
            item["update_time"] = item.get("update_time", datetime.now()).strftime('%Y-%m-%d %H:%M:%S')

            data_list, new_data_ids = await get_data_names_by_ids(item.pop("data_ids"))
            item["data_list"] = data_list
            if new_data_ids is not None:
                await update_jargon_data(
                    user_id=user_id,
                    jargon_id=item["jargon_id"],
                    jargon_name=None,
                    jargon_description=None,
                    similar_words=None,
                    is_rewrite=None,
                    data_ids=new_data_ids,
                )

        return {
            "code": 200,
            "data": {"jargon_infos": jargon_infos, "total_number": total_number},
            "msg": "成功查询到惯用语信息",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from typing import List


async def upload_jargon_excel(user_id: str, files: List[UploadFile], data_ids: list):
    """解析惯用语Excel文件，新增或更新惯用语数据到MongoDB数据库"""

    def parse_similar_words(similar_words_str: str) -> List[str]:
        """将同义词字符串解析为列表"""
        if not isinstance(similar_words_str, str):
            return []
        return [word.strip() for word in similar_words_str.split(";") if word.strip()]

    try:
        for file in files:
            excel_name = file.filename

            # 检查文件格式是否支持
            excel_checker = FileTypeChecker(required_categories="excel")
            file_suffix = excel_checker.get_suffix(excel_name)
            is_support = excel_checker.check(excel_name)

            if not is_support:
                support_suffix = excel_checker.supported_suffix()
                return return_error(
                    code=3000,
                    msg=f"不支持的文件格式 {file_suffix}，目前仅支持上传{support_suffix}",
                )

            file_content = await file.read()
            excel_file = BytesIO(file_content)

            try:
                # 跳过第一行注释，第二行为列名
                df = pd.read_excel(excel_file, header=1)
            except Exception as e:
                return return_error(code=3001, e=e)

            update_jargon_ids = []
            insert_jargon_ids = []

            for _, row in df.iterrows():
                # 如果当前行全部为空值，跳过此行继续处理下一行
                if row.isnull().all():
                    continue

                jargon_name = row.get("业务定义")
                jargon_description = row.get("数据解释")
                similar_words = parse_similar_words(row.get("同义词", ""))
                is_rewrite = True if row.get("是否强制改写") == "是" else False

                # 查询生成范围内是否已经存在相同的行业惯用语
                try:
                    existing_jargon = await db_client["mongo_col"].jargon_info.find_one(
                        {
                            "user_id": user_id,
                            "jargon_name": jargon_name,
                            "data_ids": {"$in": data_ids},
                            "status": 0,
                        }
                    )
                except PyMongoError as e:
                    return return_error(code=2400, e=e)
                if existing_jargon:
                    insert_res = await create_jargon_data(
                        user_id=user_id,
                        jargon_name=jargon_name,
                        jargon_description=jargon_description,
                        similar_words=similar_words,
                        is_rewrite=is_rewrite,
                        data_ids=data_ids,
                    )
                    if insert_res["code"] == 200:
                        insert_jargon_ids.append(insert_res["data"]["jargon_id"])
                    else:
                        return insert_res
                else:
                    existing_jargon_id = str(existing_jargon.get("_id"))
                    update_res = await update_jargon_data(
                        user_id=user_id,
                        jargon_id=existing_jargon_id,
                        jargon_name=jargon_name,
                        jargon_description=jargon_description,
                        similar_words=similar_words,
                        is_rewrite=is_rewrite,
                        data_ids=data_ids,
                    )
                    if update_res["code"] == 200:
                        update_jargon_ids.append(insert_res["data"]["jargon_id"])
                    else:
                        return update_res

        return {
            "code": 200,
            "data": {
                "insert_jargon_ids": insert_jargon_ids,
                "update_jargon_ids": update_jargon_ids,
            },
            "msg": "批量添加惯用语成功",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
