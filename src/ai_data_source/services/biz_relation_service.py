import os
import sys

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.lifespan.db_client import db_client
from src.common.utils.error_codes import return_error

from datetime import datetime
from fastapi import HTTPException
from pymongo import ReturnDocument
from pymongo.errors import PyMongoError
from pymilvus.exceptions import MilvusException
from src.common.utils.object_id import convert_to_object_id
from src.common.utils.embedding import text_to_dense_vector


async def create_biz_relation(
    user_id: str,
    data_id: str,
    biz_name: str,
    biz_ask: str,
    biz_desc: str,
    table_names: list,
):
    """业务关联表管理: 创建"""
    try:
        # 检查是否存在相同名的业务数据表
        existing_biz = await db_client["mongo_col"].biz_relation.find_one(
            {"user_id": user_id, "data_id": data_id, "biz_name": biz_name, "status": 0}
        )
        if existing_biz:
            return return_error(code=2101)

        # 组装数据以便插入MongoDB
        now_time = datetime.now()
        biz_info = {
            "user_id": user_id,
            "data_id": data_id,
            "biz_name": biz_name,
            "biz_ask": biz_ask,
            "biz_desc": biz_desc,
            "table_names": table_names,
            "status": 0,
            "create_time": now_time,
            "update_time": now_time,
        }

        # 将 biz_info 保存到 Mongo
        try:
            result = await db_client["mongo_col"].biz_relation.insert_one(biz_info)
            biz_id = str(result.inserted_id)
        except PyMongoError as e:
            return return_error(code=2100, e=e)
        if not biz_id:
            return return_error(code=2100)

        logger.info(
            f"{user_id:<15} | create Mongo data: {biz_id}"
        )

        # 保存到 milvus
        milvus_client = db_client["milvus_client"]
        biz_relation_col = config_data["milvus"]["col_list"]["biz_relation"]

        desc_text = f"{biz_name}: {biz_desc}"
        biz_milvus = [
            {
                "biz_id": biz_id,
                "user_id": user_id,
                "data_id": data_id,
                "ask_text": biz_ask,
                "desc_text": desc_text,
            }
        ]

        # 生成稠密向量
        dense_vector = await text_to_dense_vector([biz_ask, desc_text], 1024)
        biz_milvus[0]["ask_dense_vector"] = dense_vector[0]
        biz_milvus[0]["desc_dense_vector"] = dense_vector[1]

        # 插入数据到 Milvus
        try:
            biz_res = await milvus_client.insert(biz_relation_col, biz_milvus)
            insert_count = biz_res.get("insert_count", 0)
        except MilvusException as e:
            return return_error(code=2100, e=e)
        if not insert_count > 0:
            return return_error(code=2100)

        logger.info(
            f"{user_id:<15} | create Milvus data: {biz_id}"
        )

        return {
            "code": 200,
            "data": {
                "biz_id": biz_id,
            },
            "msg": "成功创建业务数据表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def delete_biz_relation(
    user_id: str,
    biz_ids: list,
):
    """业务关联表管理: 删除"""
    try:
        object_ids = [convert_to_object_id(biz_id) for biz_id in biz_ids]
        now_time = datetime.now()

        # 批量删除操作
        try:
            result = await db_client["mongo_col"].biz_relation.update_many(
                {"user_id": user_id, "_id": {"$in": object_ids}},
                {
                    "$set": {
                        "status": 1,
                        "update_time": now_time,
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2300, e=e)
        if not result.matched_count > 0:
            return return_error(code=2301)
        if not result.modified_count > 0:
            return return_error(code=2300)

        logger.info(
            f"{user_id:<15} | delete Mongo data: {biz_ids}"
        )

        # 批量删除 milvus
        milvus_client = db_client["milvus_client"]
        biz_relation_col = config_data["milvus"]["col_list"]["biz_relation"]

        filter_expr = "user_id == {user_id} AND biz_id IN {biz_ids}"
        filter_params = {"user_id": user_id, "biz_ids": biz_ids}

        try:
            biz_delete = await milvus_client.delete(
                collection_name=biz_relation_col,
                filter=filter_expr,
                filter_params=filter_params,
            )
            delete_count = biz_delete["delete_count"]
        except MilvusException as e:
            return return_error(code=2300, e=e)
        if not delete_count > 0:
            return return_error(code=2301, e=e)

        logger.info(
            f"{user_id:<15} | delete Milvus data: {biz_ids}"
        )

        return {
            "code": 200,
            "data": {
                "biz_ids": biz_ids,
            },
            "msg": "成功删除业务数据表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def update_biz_relation(
    user_id: str,
    biz_id: str,
    data_id: str,
    biz_name: str | None,
    biz_ask: str | None,
    biz_desc: str | None,
    table_names: list | None,
):
    """业务关联表管理: 更新"""
    try:
        if biz_name:
            # 检查是否存在相同名的业务数据表
            existing_biz = await db_client["mongo_col"].biz_relation.find_one(
                {
                    "user_id": user_id,
                    "data_id": data_id,
                    "biz_name": biz_name,
                    "status": 0,
                }
            )
            if existing_biz:
                return return_error(code=2101)

        object_id = convert_to_object_id(biz_id)

        biz_info = {
            "biz_name": biz_name,
            "biz_ask": biz_ask,
            "biz_desc": biz_desc,
            "table_names": table_names,
            "update_time": datetime.now(),
        }
        biz_info = {key: value for key, value in biz_info.items() if value is not None}

        # 更新 Mongo
        try:
            biz_info = await db_client["mongo_col"].biz_relation.find_one_and_update(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": biz_info},
                return_document=ReturnDocument.AFTER,
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not biz_info:
            return return_error(code=2201)

        logger.info(
            f"{user_id:<15} | update Mongo data: {biz_id}"
        )

        if biz_ask is not None or biz_name is not None or biz_desc is not None:
            # 更新 Milvus
            milvus_client = db_client["milvus_client"]
            biz_relation_col = config_data["milvus"]["col_list"]["biz_relation"]
                
            ask_text = biz_info.get("biz_ask", "")
            desc_text = f'{biz_info.get("biz_name", "")}: {biz_info.get("biz_desc", "")}'

            biz_milvus = [
                {
                    "biz_id": biz_id,
                    "user_id": user_id,
                    "data_id": data_id,
                    "ask_text": ask_text,
                    "desc_text": desc_text,
                }
            ]

            # 生成稠密向量
            dense_vector = await text_to_dense_vector([ask_text, desc_text], 1024)
            biz_milvus[0]["ask_dense_vector"] = dense_vector[0]
            biz_milvus[0]["desc_dense_vector"] = dense_vector[1]

            # 插入数据到 Milvus
            try:
                biz_res = await milvus_client.upsert(biz_relation_col, biz_milvus)
                upsert_count = biz_res.get("upsert_count", 0)
            except MilvusException as e:
                return return_error(code=2200, e=e)
            if not upsert_count > 0:
                return return_error(code=2201)

            logger.info(
                f"{user_id:<15} | update Milvus data: {biz_id}"
            )

        return {
            "code": 200,
            "data": {
                "biz_id": biz_id,
            },
            "msg": "成功更新业务数据表",
        }
    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_biz_relation(
    user_id: str,
    page_size: int,
    page_number: int,
    key_word: str,
    enable_page: bool,
):
    """业务关联表管理: 查询"""
    try:
        condition = {
            "user_id": user_id,
            "status": 0,
        }
        if key_word:
            condition["$or"] = [
                {"biz_name": {"$regex": key_word, "$options": "i"}},
                {"biz_ask": {"$regex": key_word, "$options": "i"}},
                {"biz_desc": {"$regex": key_word, "$options": "i"}},
            ]

        try:
            total_number = await db_client["mongo_col"].biz_relation.count_documents(
                condition
            )

            # 构建查询
            query = (
                db_client["mongo_col"]
                .biz_relation.find(condition)
                .sort({"update_time": -1})
            )

            # 分页
            if enable_page:
                skip_count = (page_number - 1) * page_size
                query = query.skip(skip_count).limit(page_size)

            # 获取数据
            biz_infos = await query.to_list(length=None)
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        for item in biz_infos:
            item["biz_id"] = str(item.pop("_id"))
            item["create_time"] = item.get("create_time", datetime.now()).strftime('%Y-%m-%d %H:%M:%S')
            item["update_time"] = item.get("update_time", datetime.now()).strftime('%Y-%m-%d %H:%M:%S')

        return {
            "code": 200,
            "data": {
                "biz_infos": biz_infos,
                "total_number": total_number,
            },
            "msg": "成功查询到业务数据表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
