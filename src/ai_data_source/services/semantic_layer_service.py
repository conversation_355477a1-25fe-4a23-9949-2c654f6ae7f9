import os
import sys
import regex
import asyncio
from datetime import datetime

from fastapi import <PERSON>TTP<PERSON>x<PERSON>
from src.common.utils.logger import logger, log_runtime
from src.common.utils.config import config_data
from src.common.utils.error_codes import return_error
from src.common.lifespan.db_client import db_client

from pymongo import ReturnDocument
from pymongo.errors import PyMongoError
from pymilvus.exceptions import MilvusException
from src.common.utils.object_id import convert_to_object_id

from src.sql_engine.utils.db_mschema import MSchema
from src.sql_engine.utils.db_util import BackStatusEnum


async def fetch_table_info(
    user_id: str,
    data_id: str,
    table_name: str,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not data_info:
            return return_error(code=2401)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        mschema = data_info.get("mschema", {})
        mschema_tables = mschema.get("tables", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)
        mschema_table_info = mschema_tables[table_name]

        # foreign_keys
        fk_str_list = mschema_class.mschema_foreign_keys(
            selected_tables=[table_name], check_selected=False
        )
        table_foreign_keys = []
        for fk_str in fk_str_list:
            table_foreign_keys.append(fk_str)

        # primary_keys
        table_primary_keys = []
        mschema_field = mschema_table_info.get("fields", {})
        for field_name, field_info in mschema_field.items():
            if field_info.get("primary_key", False):
                table_primary_keys.append(field_name)

        table_info = {
            "data_name": mschema.get("db_name", ""),
            "table_name": table_name,
            "rename": mschema_table_info.get("rename", ""),
            "remark": mschema_table_info.get("remark", ""),
            "comment": mschema_table_info.get("comment", ""),
            "primary_keys": table_primary_keys,
            "foreign_keys": table_foreign_keys,
            "unique_keys": mschema_table_info.get("unique_keys", []),
            "indexs": mschema_table_info.get("indexs", []),
            "desc_gen_status": mschema_table_info.get(
                "desc_gen_status", BackStatusEnum.PENDING
            ),
            "example_sync_status": mschema_table_info.get(
                "example_sync_status", BackStatusEnum.PENDING
            ),
            "statistics_sync_status": mschema_table_info.get(
                "statistics_sync_status", BackStatusEnum.PENDING
            ),
        }

        return {
            "code": 200,
            "data": {"table_info": table_info},
            "msg": "成功查询数据表信息",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_table_list(
    user_id: str,
    data_id: str,
    page_size: int,
    page_number: int,
    enable_page: bool,
    key_word: str = "",
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not data_info:
            return return_error(code=2401)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        mschema = data_info.get("mschema", {})
        mschema_tables = mschema.get("tables", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        table_infos = []
        for table_name, mschema_table_info in mschema_tables.items():

            # foreign_keys
            fk_str_list = mschema_class.mschema_foreign_keys(
                selected_tables=[table_name], check_selected=False
            )
            table_foreign_keys = []
            for fk_str in fk_str_list:
                table_foreign_keys.append(fk_str)

            # primary_keys
            table_primary_keys = []
            mschema_field = mschema_table_info.get("fields", {})
            for field_name, field_info in mschema_field.items():
                if field_info.get("primary_key", False):
                    table_primary_keys.append(field_name)

            table_data = {
                "data_name": mschema.get("db_name", ""),
                "table_name": table_name,
                "rename": mschema_table_info.get("rename", ""),
                "remark": mschema_table_info.get("remark", ""),
                "comment": mschema_table_info.get("comment", ""),
                "primary_keys": table_primary_keys,
                "foreign_keys": table_foreign_keys,
                "unique_keys": mschema_table_info.get("unique_keys", []),
                "indexs": mschema_table_info.get("indexs", []),
                "desc_gen_status": mschema_table_info.get(
                    "desc_gen_status", BackStatusEnum.PENDING
                ),
                "example_sync_status": mschema_table_info.get(
                    "example_sync_status", BackStatusEnum.PENDING
                ),
                "statistics_sync_status": mschema_table_info.get(
                    "statistics_sync_status", BackStatusEnum.PENDING
                ),
            }

            # 如果提供了关键字，则进行模糊搜索
            if key_word:
                pattern = regex.compile(key_word, regex.IGNORECASE)
                # 只在特定字段上进行搜索
                target_fields = ["table_name", "rename", "remark", "comment"]
                if any(
                    pattern.search(table_data[field])
                    for field in target_fields
                    if field in table_data
                ):
                    table_infos.append(table_data)
            else:
                table_infos.append(table_data)

        total_number = len(table_infos)

        if enable_page:
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            table_infos = table_infos[start_index:end_index]

        return {
            "code": 200,
            "data": {"table_infos": table_infos, "total_number": total_number},
            "msg": "成功查询数据表信息",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_column_list(
    user_id: str,
    data_id: str,
    table_name: str,
    page_size: int,
    page_number: int,
    enable_page: bool,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not data_info:
            return return_error(code=2401)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        mschema = data_info.get("mschema", {})
        mschema_tables = mschema.get("tables", {})
        mschema_table_info = mschema_tables.get(table_name, {})
        mschema_fields = mschema_table_info.get("fields", {})

        column_infos = []
        for field_name, field_info in mschema_fields.items():
            column_infos.append(
                {
                    "column_name": field_name,
                    "comment": field_info.get("comment", ""),
                    "rename": field_info.get("rename", ""),
                    "remark": field_info.get("remark", ""),
                    "autoincrement": field_info.get("autoincrement", False),
                    "default": field_info.get("default", None),
                    "nullable": field_info.get("nullable", False),
                    "unique": field_info.get("unique", False),
                    "primary_key": field_info.get("primary_key", False),
                    "type": field_info.get("type", ""),
                    "category": field_info.get("category", ""),
                    "data_range": field_info.get("data_range", []),
                    "date_min_gran": field_info.get("date_min_gran", ""),
                    "dim_or_meas": field_info.get("dim_or_meas", ""),
                    "examples": field_info.get("examples", []),
                    "statistics": field_info.get("statistics", {}),
                    "desc_gen_status": field_info.get(
                        "desc_gen_status", BackStatusEnum.PENDING
                    ),
                    "example_sync_status": field_info.get(
                        "example_sync_status", BackStatusEnum.PENDING
                    ),
                    "statistics_sync_status": field_info.get(
                        "statistics_sync_status", BackStatusEnum.PENDING
                    ),
                }
            )

        total_number = len(column_infos)

        if enable_page:
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            column_infos = column_infos[start_index:end_index]

        return {
            "code": 200,
            "data": {"column_infos": column_infos, "total_number": total_number},
            "msg": "成功查询数据表信息",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.common.utils.embedding import text_to_dense_vector


async def update_tabel_semantic(
    user_id: str,
    data_id: str,
    table_name: str,
    rename: str | None,
    remark: str | None,
):
    try:
        object_id = convert_to_object_id(data_id)

        table_info = {
            f"mschema.tables.{table_name}.rename": rename,
            f"mschema.tables.{table_name}.remark": remark,
            "update_time": datetime.now(),
        }
        table_info = {
            key: value for key, value in table_info.items() if value is not None
        }

        # 更新 MongoDB
        try:
            data_info = await db_client["mongo_col"].data_info.find_one_and_update(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": table_info},
                return_document=ReturnDocument.AFTER,
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not data_info:
            return return_error(code=2201)

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )

        # 更新 Milvus
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        table_text = mschema_class.get_mschema_table_info(table_name=table_name)
        dense_vector = await text_to_dense_vector([table_text], 1024)

        table_info_col = config_data["milvus"]["col_list"]["table_info"]
        milvus_client = db_client["milvus_client"]
        table_id = f"{user_id}-{data_id}-{table_name}"
        milvus_table_info = {
            "table_id": table_id,
            "user_id": user_id,
            "data_id": data_id,
            "table_name": table_name,
            "text": table_text,
            "dense_vector": dense_vector[0],
        }

        try:
            upsert_res = await milvus_client.upsert(
                collection_name=table_info_col,
                data=[milvus_table_info],
            )
        except MilvusException as e:
            return return_error(code=2200, e=e)
        if not upsert_res.get("upsert_count", 0):
            return return_error(code=2200)

        logger.info(
            f"{user_id:<15} | upsert Milvus data: {table_id}"
        )

        return {
            "code": 200,
            "data": {"data_id": data_id},
            "msg": "成功更新数据表语义信息",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.sql_engine.utils.type_engine import TypeEngine


async def update_column_semantic(
    user_id: str,
    data_id: str,
    table_name: str,
    column_name: str,
    rename: str | None,
    remark: str | None,
    examples: list | None,
    data_range: list | None,
    category: str | None,
    date_min_gran: str | None,
):
    try:
        object_id = convert_to_object_id(data_id)

        if data_range and len(data_range) != 2:
            data_range = None

        if category:
            type_engine = TypeEngine()
            if category == type_engine.field_category_measure_label:
                dim_or_meas = type_engine.measure_label
            else:
                dim_or_meas = type_engine.dimension_label
        else:
            dim_or_meas = None

        column_info = {
            f"mschema.tables.{table_name}.fields.{column_name}.rename": rename,
            f"mschema.tables.{table_name}.fields.{column_name}.remark": remark,
            f"mschema.tables.{table_name}.fields.{column_name}.examples": examples,
            f"mschema.tables.{table_name}.fields.{column_name}.data_range": data_range,
            f"mschema.tables.{table_name}.fields.{column_name}.category": category,
            f"mschema.tables.{table_name}.fields.{column_name}.date_min_gran": date_min_gran,
            f"mschema.tables.{table_name}.fields.{column_name}.dim_or_meas": dim_or_meas,
            "update_time": datetime.now(),
        }
        column_info = {
            key: value for key, value in column_info.items() if value is not None
        }

        # 更新 MongoDB
        try:
            data_info = await db_client["mongo_col"].data_info.find_one_and_update(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": column_info},
                return_document=ReturnDocument.AFTER,
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not data_info:
            return return_error(code=2201)

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )

        # 更新 Milvus
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        column_text = mschema_class.get_mschema_field_info(
            field_name=column_name,
            field_info=mschema_class.tables[table_name]["fields"][column_name],
        )
        dense_vector = await text_to_dense_vector([column_text], 1024)

        column_info_col = config_data["milvus"]["col_list"]["column_info"]
        milvus_client = db_client["milvus_client"]
        column_id = f"{user_id}-{data_id}-{table_name}-{column_name}"
        milvus_column_info = {
            "column_id": column_id,
            "user_id": user_id,
            "data_id": data_id,
            "table_name": table_name,
            "column_name": column_name,
            "text": column_text,
            "dense_vector": dense_vector[0],
        }

        try:
            upsert_res = await milvus_client.upsert(
                collection_name=column_info_col,
                data=[milvus_column_info],
            )
        except MilvusException as e:
            return return_error(code=2200, e=e)
        if not upsert_res.get("upsert_count", 0):
            return return_error(code=2200)

        logger.info(
            f"{user_id:<15} | upsert Milvus data: {data_id}"
        )

        return {
            "code": 200,
            "data": {"data_id": data_id},
            "msg": "成功更新数据表语义信息",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.sql_engine.utils.db_config import DBConfig
from src.sql_engine.utils.db_init import init_db_conn


async def fetch_sample_data(
    user_id: str,
    data_id: str,
    table_name: str,
    sample_size: int = 20,  # 默认返回20条记录
):
    try:
        data_conn = None
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not data_info:
            return return_error(code=2401)

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        # 连接 data_conn
        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )

        data_conn = init_db_conn(
            db_config, include_tables=include_tables, mschema=mschema_class
        )
        column_names, sample_data = await data_conn.get_top_rows(
            table_name, top=sample_size
        )

        return {
            "code": 200,
            "data": {
                "columns": column_names,
                "sample_data": sample_data,
            },
            "msg": "成功获取数据表示例数据",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
    finally:
        if data_conn:
            data_conn.close()


@log_runtime
async def gen_single_field_desc(
    user_id: str,
    data_id: str,
    table_name: str,
    column_name: str,
    ref_num: int = None,
    comment_mode: str = None,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config,
            include_tables=include_tables,
            mschema=mschema_class,
            comment_mode=comment_mode,
        )

        # 更新状态
        await data_conn.set_column_status(
            table_name, [column_name], "desc_gen_status", BackStatusEnum.WORKING
        )

        if comment_mode == "separate":
            data_conn.mschema.set_column_property(table_name, column_name, "rename", "")
            data_conn.mschema.set_column_property(table_name, column_name, "remark", "")

        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))

        # 生成描述
        await data_conn.single_table_category(
            table_name=table_name, column_names=[column_name]
        )
        await data_conn.table_desc_generation(
            table_name=table_name,
            gen_table_desc=False,
            column_names=[column_name],
            ref_num=ref_num,
        )

        gen_rename = data_conn.mschema.tables[table_name]["fields"][column_name][
            "rename"
        ]
        gen_remark = data_conn.mschema.tables[table_name]["fields"][column_name][
            "remark"
        ]

        # 更新 MongoDB
        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))
        if not result.matched_count > 0:
            logger.error(return_error(code=2201))
        if not result.modified_count > 0:
            logger.error(return_error(code=2202))

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )

        return {
            "code": 200,
            "data": {
                "rename": gen_rename,
                "remark": gen_remark,
            },
            "msg": "成功生成字段语义",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


@log_runtime
async def gen_some_field_desc(
    user_id: str,
    data_id: str,
    table_name: str,
    column_names: list,
    ref_num: int = None,
    comment_mode: str = None,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config,
            include_tables=include_tables,
            mschema=mschema_class,
            comment_mode=comment_mode,
        )

        # 更新状态
        await data_conn.set_column_status(
            table_name, column_names, "desc_gen_status", BackStatusEnum.WORKING
        )

        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))

        # 生成描述
        await data_conn.single_table_category(
            table_name=table_name, column_names=column_names
        )
        await data_conn.table_desc_generation(
            table_name=table_name,
            gen_table_desc=False,
            column_names=column_names,
            ref_num=ref_num,
        )

        # 更新 MongoDB
        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))
        if not result.matched_count > 0:
            logger.error(return_error(code=2201))
        if not result.modified_count > 0:
            logger.error(return_error(code=2202))

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


@log_runtime
async def gen_table_field_desc(
    user_id: str,
    data_id: str,
    table_names: list = None,
    ref_num: int = None,
    comment_mode: str = None,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config,
            include_tables=include_tables,
            mschema=mschema_class,
            comment_mode=comment_mode,
        )

        # 是否整个数据库都在更新
        update_desc_gen_status = False
        if table_names is None:
            table_names = data_conn.get_table_names()
            update_desc_gen_status = True

        # 更新状态
        await data_conn.set_table_status(
            table_names, "desc_gen_status", BackStatusEnum.WORKING
        )
        for table_name in table_names:
            await data_conn.set_column_status(
                table_name, None, "desc_gen_status", BackStatusEnum.WORKING
            )

        try:
            update_fields = {
                "mschema": data_conn.mschema.dump(),
                "update_time": datetime.now(),
            }
            if update_desc_gen_status:
                update_fields["desc_gen_status"] = BackStatusEnum.WORKING

            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": update_fields},
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))

        # 生成描述
        await data_conn.fields_category(table_names=table_names)
        await data_conn.desc_generation(table_names=table_names, ref_num=ref_num)

        # 更新 MongoDB
        try:
            update_fields = {
                "mschema": data_conn.mschema.dump(),
                "update_time": datetime.now(),
            }
            if update_desc_gen_status:
                update_fields["desc_gen_status"] = BackStatusEnum.SUCCEEDED

            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": update_fields},
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))
        if not result.matched_count > 0:
            logger.error(return_error(code=2201))
        if not result.modified_count > 0:
            logger.error(return_error(code=2202))

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")

        # 更新 MongoDB
        try:
            object_id = convert_to_object_id(data_id)
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "desc_gen_status": BackStatusEnum.FAILED,
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))

        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


@log_runtime
async def gen_table_desc(
    user_id: str,
    data_id: str,
    table_name: str,
    is_background_tasks: bool,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))
        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config, include_tables=include_tables, mschema=mschema_class
        )

        # 更新状态
        if is_background_tasks:
            try:
                await data_conn.set_table_status(
                    [table_name], "desc_gen_status", BackStatusEnum.WORKING
                )

                result = await db_client["mongo_col"].data_info.update_one(
                    {"user_id": user_id, "_id": object_id, "status": 0},
                    {
                        "$set": {
                            "mschema": data_conn.mschema.dump(),
                            "update_time": datetime.now(),
                        }
                    },
                    upsert=False,
                )
            except PyMongoError as e:
                logger.error(return_error(code=2200, e=e))

        gen_rename, gen_remark = await data_conn.only_table_desc_generation(
            table_name=table_name,
        )
        if not gen_rename and not gen_remark:
            raise HTTPException(status_code=500, detail="生成失败")

        if not is_background_tasks:
            return {
                "code": 200,
                "data": {
                    "rename": gen_rename,
                    "remark": gen_remark,
                },
                "msg": "成功生成数据表语义",
            }
        else:
            # 更新 MongoDB
            try:
                data_conn.mschema.set_table_property(table_name, "rename", gen_rename)
                data_conn.mschema.set_table_property(table_name, "remark", gen_remark)
                result = await db_client["mongo_col"].data_info.update_one(
                    {"user_id": user_id, "_id": object_id, "status": 0},
                    {
                        "$set": {
                            "mschema": data_conn.mschema.dump(),
                            "update_time": datetime.now(),
                        }
                    },
                    upsert=False,
                )
            except PyMongoError as e:
                logger.error(return_error(code=2200, e=e))
            if not result.matched_count > 0:
                logger.error(return_error(code=2201))
            if not result.modified_count > 0:
                logger.error(return_error(code=2202))

            logger.info(
                f"{user_id:<15} | update MongoDB data: {data_id}"
            )
    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


@log_runtime
async def gen_data_desc(
    user_id: str,
    data_id: str,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))
        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config, include_tables=include_tables, mschema=mschema_class
        )

        data_rename, data_desc, db_info = await data_conn.gen_data_desc(
            return_format=False
        )

        return {
            "code": 200,
            "data": {
                "rename": data_rename,
                "remark": data_desc + "\n" + db_info,
            },
            "msg": "成功生成数据源语义",
        }

    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


@log_runtime
async def get_field_examples(
    user_id: str,
    data_id: str,
    table_name: str,
    column_names: list = None,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config, include_tables=include_tables, mschema=mschema_class
        )

        # 更新状态
        await data_conn.set_column_status(
            table_name, column_names, "example_sync_status", BackStatusEnum.WORKING
        )

        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))

        fields = await data_conn.get_columns(table_name)
        if column_names is not None:
            fields = {field_name: fields[field_name] for field_name in column_names}

        tasks = []
        for field in fields:
            field_name = field["name"]
            tasks.append(data_conn.init_field_examples(table_name, field_name))
        await asyncio.gather(*tasks)

        # 更新 MongoDB
        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))
        if not result.matched_count > 0:
            logger.error(return_error(code=2201))
        if not result.modified_count > 0:
            logger.error(return_error(code=2202))

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )
    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


@log_runtime
async def get_field_statistics(
    user_id: str,
    data_id: str,
    table_name: str,
    column_names: list = None,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config, include_tables=include_tables, mschema=mschema_class
        )

        # 更新状态            
        await data_conn.set_column_status(
            table_name, column_names, "statistics_sync_status", BackStatusEnum.WORKING
        )
        
        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))

        fields = await data_conn.get_columns(table_name)
        if column_names is not None:
            fields = {field_name: fields[field_name] for field_name in column_names}

        tasks = []
        for field in fields:
            field_type = f"{field['type']!s}"
            field_name = field["name"]
            tasks.append(
                data_conn.init_field_statistics(table_name, field_name, field_type)
            )
        await asyncio.gather(*tasks)

        # 更新 MongoDB
        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))
        if not result.matched_count > 0:
            logger.error(return_error(code=2201))
        if not result.modified_count > 0:
            logger.error(return_error(code=2202))

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


@log_runtime
async def get_table_examples(
    user_id: str,
    data_id: str,
    table_names: list,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config, include_tables=include_tables, mschema=mschema_class
        )
        if table_names is None:
            table_names = data_conn.get_table_names()

        # 更新状态
        await data_conn.set_table_status(
            table_names, "example_sync_status", BackStatusEnum.WORKING
        )
        for table_name in table_names:
            await data_conn.set_column_status(
                table_name, None, "example_sync_status", BackStatusEnum.WORKING
            )

        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))

        async def process_table(table_name, semaphore):
            async with semaphore:
                fields = await data_conn.get_columns(table_name)
                tasks = []
                for field in fields:
                    field_name = field["name"]
                    tasks.append(data_conn.init_field_examples(table_name, field_name))
                await asyncio.gather(*tasks)

                await data_conn.set_table_status(
                    [table_name], "example_sync_status", BackStatusEnum.SUCCEEDED
                )
                await data_conn.set_column_status(
                    table_name, None, "example_sync_status", BackStatusEnum.SUCCEEDED
                )

        semaphore = asyncio.Semaphore(config_data.get("semaphore", 30))
        tasks = [process_table(table, semaphore) for table in table_names]
        await asyncio.gather(*tasks)

        logger.info(
            f"{user_id:<15} | init_field_examples over"
        )

        # 更新 MongoDB
        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))
        if not result.matched_count > 0:
            logger.error(return_error(code=2201))
        if not result.modified_count > 0:
            logger.error(return_error(code=2202))

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


@log_runtime
async def get_table_statistics(
    user_id: str,
    data_id: str,
    table_names: list,
):
    try:
        # 查询 MongoDB
        object_id = convert_to_object_id(data_id)
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            logger.error(return_error(code=2400, e=e))
        if not data_info:
            logger.error(return_error(code=2401))

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", None)
        mschema = data_info.get("mschema", {})
        mschema_class = MSchema()
        mschema_class.load_from_dict(mschema)

        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config, include_tables=include_tables, mschema=mschema_class
        )
        if table_names is None:
            table_names = data_conn.get_table_names()

        # 更新状态
        await data_conn.set_table_status(
            table_names, "statistics_sync_status", BackStatusEnum.WORKING
        )
        for table_name in table_names:
            await data_conn.set_column_status(
                table_name, None, "statistics_sync_status", BackStatusEnum.WORKING
            )

        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))

        async def process_table(table_name, semaphore):
            async with semaphore:
                fields = await data_conn.get_columns(table_name)
                tasks = []
                for field in fields:
                    field_type = f"{field['type']!s}"
                    field_name = field["name"]
                    tasks.append(
                        data_conn.init_field_statistics(
                            table_name, field_name, field_type
                        )
                    )
                await asyncio.gather(*tasks)
                await data_conn.set_table_status(
                    [table_name], "statistics_sync_status", BackStatusEnum.SUCCEEDED
                )
                await data_conn.set_column_status(
                    table_name, None, "statistics_sync_status", BackStatusEnum.SUCCEEDED
                )

        semaphore = asyncio.Semaphore(config_data.get("semaphore", 30))
        tasks = [process_table(table, semaphore) for table in table_names]
        await asyncio.gather(*tasks)

        logger.info(
            f"{user_id:<15} | init_field_statistics over"
        )

        # 更新 MongoDB
        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "mschema": data_conn.mschema.dump(),
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))
        if not result.matched_count > 0:
            logger.error(return_error(code=2201))
        if not result.modified_count > 0:
            logger.error(return_error(code=2202))

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
