import os
import sys
import regex
from sqlalchemy import inspect as sqlalchemy_inspect

from fastapi import HTTPException
from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.error_codes import return_error
from src.common.lifespan.db_client import db_client

# mongo
from datetime import datetime
from pymongo.errors import PyMongoError
from src.common.utils.object_id import convert_to_object_id, resolve_parent_folder_id

# minio
from fastapi import UploadFile
from src.ai_chat_data.utils.minio_uitls import save_file_to_minio
from src.common.utils.file_schema import FileTypeChecker

# 数据源配置增删改查
####################################################################################################

from src.sql_engine.utils.db_config import DBConfig
from src.sql_engine.utils.db_init import init_db_conn

# from src.sql_engine.database_env import DataBaseEnv


async def test_data_connect(
    user_id: str,
    data_config: dict,
):
    """数据源: 连接测试"""
    try:
        data_conn = None
        data_config = data_config.model_dump()
        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(db_config, conn_init=False)

        logger.info(
            f"{user_id:<15} | get_conn success: {data_config['data_name']}"
        )

        return {
            "code": 200,
            "data": None,
            "msg": "成功连接数据源",
        }
    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
    finally:
        if data_conn:
            data_conn.dispose()


async def view_data_info(
    user_id: str,
    data_config: dict,
    page_size: int,
    page_number: int,
    enable_page: bool,
    key_word: str = "",
):
    """数据源: 检查数据表"""
    try:
        data_conn = None
        data_config = data_config.model_dump()
        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )

        data_conn = init_db_conn(db_config,conn_init=False)
        # 无需初始化 async_init

        logger.info(
            f"{user_id:<15} | get conn success: {data_config['data_name']}"
        )

         # 分页查询 原生注释
        inspector = sqlalchemy_inspect(data_conn)
        table_names = inspector.get_table_names()
                    
        if key_word:
            pattern = regex.compile(key_word, regex.IGNORECASE)  # 忽略大小写
            table_names = [
                name
                for name in table_names
                if pattern.search(name) or pattern.search(table_comments.get(name, ""))
            ]
        total_number = len(table_names)

        if enable_page:
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            table_names = table_names[start_index:end_index]
        
        table_comments = {}
        for table_name in table_names:
            table_comment = inspector.get_table_comment(table_name)
            table_comments[table_name] = table_comment.get('text') or ""
            
        logger.info(
            f"{user_id:<15} | page table names: {table_names}"
        )


        table_infos = [
            {
                "table_name": name,
                "table_comment": table_comments.get(name, ""),  # 处理注释不存在的情况
            }
            for name in table_names
        ]

        return {
            "code": 200,
            "data": {"table_infos": table_infos, "total_number": total_number},
            "msg": "成功连接数据源",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生未知错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
    finally:
        if data_conn:
            data_conn.dispose()


from src.ai_data_source.utils.milvus_semantic import create_milvus_data_info


async def create_data_config(
    user_id: str,
    data_config: dict,
    include_tables: list,
    rename: str,
    remark: str,
    parent_dir_id: str,
):
    """数据源配置: 新建数据源
    status 值: 0:活动, 1:已删除, 2:已存档
    """

    try:
        data_conn = None
        await resolve_parent_folder_id(
            user_id, parent_dir_id, db_client["mongo_col"].data_dir
        )

        data_config = data_config.model_dump()

        # 连接 data_conn
        db_config = DBConfig(
            db_type=data_config["data_type"],
            db_path=data_config["data_path"],
            db_name=data_config["data_name"],
            db_host=data_config["data_host"],
            port=data_config["data_port"],
            user_name=data_config["data_user"],
            db_pwd=data_config["data_pwd"],
        )
        data_conn = init_db_conn(db_config, include_tables=include_tables)
        await data_conn.async_init(get_examples=False, get_statistics=False)
        # await data_conn.async_init()

        logger.info(
            f"{user_id:<15} | get conn success: {data_config['data_name']}"
        )

        if not include_tables:
            include_tables = data_conn.get_table_names()

        data_conn.mschema.db_info = remark

        # 组装数据以便插入MongoDB
        now_time = datetime.now()
        data_info = {
            "user_id": user_id,
            "data_config": data_config,
            "rename": rename,
            "remark": remark,
            "include_tables": include_tables,
            "mschema": data_conn.mschema.dump(),
            "status": 0,
            "parent_dir_id": parent_dir_id,
            "create_time": now_time,
            "update_time": now_time,
            "last_used_time": now_time,
        }

        # 将 db_info 保存到 MongoDB
        try:
            result = await db_client["mongo_col"].data_info.insert_one(data_info)
            data_id = str(result.inserted_id)
        except PyMongoError as e:
            return return_error(code=2100, e=e)
        if not data_id:
            return return_error(code=2100)

        logger.info(
            f"{user_id:<15} | create MongoDB data: {data_id}"
        )

        # 保存到 milvus
        table_num, column_num = await create_milvus_data_info(
            user_id=user_id,
            data_id=data_id,
            mschema=data_conn.mschema,
            table_names=include_tables,
        )

        logger.info(
            f"{user_id:<15} | create Milvus table num: {table_num}"
        )
        logger.info(
            f"{user_id:<15} | create Milvus column num: {column_num}"
        )

        return {
            "code": 200,
            "data": {"data_id": data_id},
            "msg": "成功创建数据源",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
    finally:
        if data_conn:
            data_conn.close()


async def upload_excel_file(user_id: str, file: UploadFile):
    """数据源配置: 上传 excel 文件"""
    try:
        file_name = file.filename

        # 检查文件格式是否支持
        file_checker = FileTypeChecker(required_categories="excel")
        file_suffix = file_checker.get_suffix(file_name)
        is_support = file_checker.check(file_name)

        if not is_support:
            support_suffix = file_checker.supported_suffix()
            return return_error(
                code=3000,
                msg=f"不支持的文件格式 {file_suffix}，目前仅支持上传{support_suffix}",
            )

        # 设置保存目录
        prefix_path = f"excel/{user_id}"
        file_content = await file.read()
        file_url = await save_file_to_minio(file_name, file_content, prefix_path, True)

        logger.info(
            f"{user_id:<15} | create Minio data: {file_url}"
        )

        return {
            "code": 200,
            "data": {"file_url": file_url},
            "msg": "成功上传数据源",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.ai_data_source.utils.milvus_semantic import delete_milvus_data_info


async def delete_data_source(user_id: str, data_id: str):
    """数据源配置: 删除"""
    try:
        object_id = convert_to_object_id(data_id)
        now_time = datetime.now()

        # 删除操作
        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "status": 1,
                        "update_time": now_time,
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2300, e=e)
        if not result.matched_count > 0:
            return return_error(code=2301)
        if not result.modified_count > 0:
            return return_error(code=2300)

        logger.info(
            f"{user_id:<15} | deleted MongoDB data: {data_id}"
        )

        # milvus 删除
        table_num, column_num = await delete_milvus_data_info(
            user_id=user_id,
            data_id=data_id,
        )

        logger.info(
            f"{user_id:<15} | delete Milvus table num: {table_num}"
        )
        logger.info(
            f"{user_id:<15} | delete Milvus column num: {column_num}"
        )

        return {
            "code": 200,
            "data": {"data_id": data_id},
            "msg": "成功删除数据源",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.sql_engine.utils.db_mschema import MSchema
from src.ai_data_source.utils.milvus_semantic import upsert_milvus_data_info
from src.sql_engine.utils.db_util import BackStatusEnum


async def update_data_source(
    user_id: str,
    data_id: str,
    data_config: dict | None,
    include_tables: list | None,
    rename: str | None,
    remark: str | None,
    parent_dir_id: str | None,
):
    """数据源配置: 修改"""

    try:
        data_conn = None
        mschema = None
        if data_config:
            data_config = data_config.model_dump()
        if include_tables is not None:
            # 查询 MongoDB
            object_id = convert_to_object_id(data_id)
            try:
                data_info = await db_client["mongo_col"].data_info.find_one(
                    {"user_id": user_id, "_id": object_id, "status": 0}
                )
            except PyMongoError as e:
                return return_error(code=2400, e=e)
            if not data_info:
                return return_error(code=2401)

            logger.info(
                f"{user_id:<15} | fetch MongoDB data: {data_id}"
            )

            data_config = data_config or data_info.get("data_config", {})
            mschema = data_info.get("mschema", {})
            mschema_class = MSchema()
            mschema_class.load_from_dict(mschema)

            db_config = DBConfig(
                db_type=data_config["data_type"],
                db_path=data_config["data_path"],
                db_name=data_config["data_name"],
                db_host=data_config["data_host"],
                port=data_config["data_port"],
                user_name=data_config["data_user"],
                db_pwd=data_config["data_pwd"],
            )
            data_conn = init_db_conn(
                db_config, include_tables=include_tables, mschema=mschema_class
            )
            await data_conn.async_init(get_examples=False, get_statistics=False)
            # await data_conn.async_init()

            logger.info(
                f"{user_id:<15} | conn init: {data_config['data_name']}"
            )

            mschema = data_conn.mschema
            if not include_tables:
                include_tables = data_conn.get_table_names()
            if remark is not None:
                data_conn.mschema.db_info = remark

        await resolve_parent_folder_id(
            user_id, parent_dir_id, db_client["mongo_col"].data_dir
        )

        data_info = {
            "data_config": data_config,
            "rename": rename,
            "remark": remark,
            "include_tables": include_tables,
            "parent_dir_id": parent_dir_id,
            "update_time": datetime.now(),
        }

        if mschema is not None:
            data_info["mschema"] = mschema.dump()

        data_info = {
            key: value for key, value in data_info.items() if value is not None
        }

        # 更新 MongoDB
        object_id = convert_to_object_id(data_id)

        try:
            result = await db_client["mongo_col"].data_info.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": data_info},
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not result.matched_count > 0:
            return return_error(code=2201)
        if not result.modified_count > 0:
            return return_error(code=2202)

        logger.info(
            f"{user_id:<15} | update MongoDB data: {data_id}"
        )

        # 同步 Milvus
        if data_conn and mschema:
            # 同步 milvus
            upsert_res = await upsert_milvus_data_info(
                user_id=user_id, data_id=data_id, mschema=mschema
            )

            table_num = upsert_res["data"]["table_num"]
            column_num = upsert_res["data"]["column_num"]

            logger.info(
                f"{user_id:<15} | upsert Milvus table num: {table_num}"
            )
            logger.info(
                f"{user_id:<15} | upsert Milvus column num: {column_num}"
            )

        return {
            "code": 200,
            "data": {"data_id": data_id},
            "msg": "成功修改数据源配置",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
    finally:
        if data_conn:
            data_conn.close()


async def fetch_data_source_dy_id(user_id: str, data_id: str, mschema: bool = False):
    """数据源配置: 查询 by ID"""

    try:
        object_id = convert_to_object_id(data_id)

        # 查询 MongoDB
        try:
            data_info = await db_client["mongo_col"].data_info.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not data_info:
            return return_error(code=2401)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {data_id}"
        )

        if data_info:
            data_info["data_id"] = str(data_info.pop("_id"))
            data_info["last_used_time"] = data_info.get(
                "last_used_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["create_time"] = data_info.get(
                "create_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["update_time"] = data_info.get(
                "update_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["include_tables"] = data_info.get("include_tables", [])
            if not mschema:
                data_info.pop("mschema", None)
            data_info["desc_gen_status"] = data_info.get(
                "desc_gen_status", BackStatusEnum.PENDING
            )

        else:
            return return_error(code=2401)

        return {
            "code": 200,
            "data": {"data_info": data_info},
            "msg": "成功查询数据源",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_data_sources_by_time(
    user_id: str,
    page_size: int,
    page_number: int,
    enable_page: bool,
):
    """数据源配置: 查询 by Time"""
    try:

        sort_priority = [("last_used_time", -1), ("update_time", -1)]
        condition = {
            "user_id": user_id,
            "status": 0,
        }

        try:
            total_number = await db_client["mongo_col"].data_info.count_documents(
                condition
            )

            # 构建查询
            query = db_client["mongo_col"].data_info.find(condition).sort(sort_priority)

            if enable_page:
                skip_count = (page_number - 1) * page_size
                query = query.skip(skip_count).limit(page_size)

            data_infos = await query.to_list(length=None)

        except PyMongoError as e:
            return return_error(code=2400, e=e)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data num: {len(data_infos)}"
        )

        # 将 _id 转换为字符串
        for data_info in data_infos:
            data_info["data_id"] = str(data_info.pop("_id"))
            data_info["last_used_time"] = data_info.get(
                "last_used_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["create_time"] = data_info.get(
                "create_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["update_time"] = data_info.get(
                "update_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["include_tables"] = data_info.get("include_tables", [])
            data_info.pop("mschema", None)
            data_info["desc_gen_status"] = data_info.get(
                "desc_gen_status", BackStatusEnum.PENDING
            )

        return {
            "code": 200,
            "data": {"data_infos": data_infos, "total_number": total_number},
            "msg": "成功查询数据源",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_data_sources_by_name_and_type(
    user_id: str,
    data_name: str,
    data_type: str,
    page_size: int,
    page_number: int,
    enable_page: bool,
):
    """数据源配置: 查询 by Name and type"""
    try:
        # 构建查询条件
        condition = {
            "user_id": user_id,
            "status": 0,
        }

        if data_name:
            condition["$or"] = [
                {"data_config.data_name": {"$regex": data_name, "$options": "i"}},
                {"rename": {"$regex": data_name, "$options": "i"}},
            ]

        if data_type:
            condition["data_config.data_type"] = data_type

        # 查询 MongoDB
        try:
            total_number = await db_client["mongo_col"].data_info.count_documents(
                condition
            )

            # 构建查询
            query = (
                db_client["mongo_col"]
                .data_info.find(condition)
                .sort({"update_time": -1})
            )

            if enable_page:
                skip_count = (page_number - 1) * page_size
                query = query.skip(skip_count).limit(page_size)

            data_infos = await query.to_list(length=None)

        except PyMongoError as e:
            return return_error(code=2400, e=e)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data num: {len(data_infos)}"
        )

        # 将 _id 转换为字符串
        for data_info in data_infos:
            data_info["data_id"] = str(data_info.pop("_id"))
            data_info["last_used_time"] = data_info.get(
                "last_used_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["create_time"] = data_info.get(
                "create_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["update_time"] = data_info.get(
                "update_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            data_info["include_tables"] = data_info.get("include_tables", [])
            data_info.pop("mschema", None)
            data_info["desc_gen_status"] = data_info.get(
                "desc_gen_status", BackStatusEnum.PENDING
            )

        return {
            "code": 200,
            "data": {"data_infos": data_infos, "total_number": total_number},
            "msg": "成功查询数据源",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


# 文件夹配置增删改查
####################################################################################################


async def create_data_dir(user_id: str, dir_name: str, parent_dir_id: str):
    """文件夹配置: 创建"""

    try:
        # 验证文件夹 ID
        await resolve_parent_folder_id(
            user_id, parent_dir_id, db_client["mongo_col"].data_dir
        )

        now_time = datetime.now()

        dir_info = {
            "user_id": user_id,
            "dir_name": dir_name,
            "status": 0,
            "parent_dir_id": parent_dir_id,
            "create_time": now_time,
            "update_time": now_time,
        }
        try:
            result = await db_client["mongo_col"].data_dir.insert_one(dir_info)
            dir_id = str(result.inserted_id)
        except PyMongoError as e:
            return return_error(code=2100, e=e)
        if not dir_id:
            return return_error(code=2100)

        logger.info(
            f"{user_id:<15} | create MongoDB data: {dir_id}"
        )

        return {
            "code": 200,
            "data": {"dir_id": dir_id},
            "msg": "文件夹已成功创建",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def delete_data_dir(user_id: str, dir_id: str):
    """文件夹配置: 删除"""

    try:
        object_id = convert_to_object_id(dir_id)

        # 先将之下的所有的data与dir设置为删除
        id_res = await fetch_dir_sub_id(user_id, dir_id, True)
        sub_dir_ids = id_res["data"]["dir_ids"]
        sub_data_ids = id_res["data"]["data_ids"]

        if sub_dir_ids:
            for sub_dir_id in sub_dir_ids:
                await delete_data_dir(user_id, sub_dir_id)
        if sub_data_ids:
            for sub_data_id in sub_data_ids:
                await delete_data_source(user_id, sub_data_id)

        # 然后删除文件夹
        try:
            result = await db_client["mongo_col"].data_dir.update_one(
                {"user_id": user_id, "_id": object_id},
                {
                    "$set": {
                        "status": 1,
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2300, e=e)
        if not result.matched_count > 0:
            return return_error(code=2301)
        if not result.modified_count > 0:
            return return_error(code=2300)

        logger.info(
            f"{user_id:<15} | delete MongoDB data: {dir_id}"
        )

        return {
            "code": 200,
            "data": {"dir_id": dir_id},
            "msg": "文件夹已成功删除",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_dir_sub_id(user_id: str, dir_id: str, recursive: bool):
    """递归查询文件夹及其所有子 dir 和 data"""
    try:
        await resolve_parent_folder_id(user_id, dir_id, db_client["mongo_col"].data_dir)

        # 查询文件夹下所有子文件夹的 ID
        try:
            sub_dirs = (
                await db_client["mongo_col"]
                .data_dir.find(
                    {"user_id": user_id, "parent_dir_id": dir_id, "status": 0}
                )
                .to_list(None)
            )
            sub_dir_ids = [str(dir["_id"]) for dir in sub_dirs]
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        # 查询该文件夹下所有子数据源的 ID
        try:

            data_infos = (
                await db_client["mongo_col"]
                .data_info.find(
                    {"user_id": user_id, "parent_dir_id": dir_id, "status": 0}
                )
                .to_list(None)
            )
            data_ids = [str(data_info["_id"]) for data_info in data_infos]
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        logger.info(
            f"{user_id:<15} | fetch dir directory: {dir_id}"
        )

        # 如果递归查询为 True，递归查询子文件夹和数据源
        if recursive:
            for sub_dir_id in sub_dir_ids:
                # 递归查找该子文件夹的子文件夹和数据源
                sub_dir_result = await fetch_dir_sub_id(user_id, sub_dir_id, True)
                sub_dir_ids.extend(sub_dir_result["data"]["dir_ids"])
                data_ids.extend(sub_dir_result["data"]["data_ids"])

        # 返回字典
        return {
            "code": 200,
            "data": {"dir_ids": sub_dir_ids, "data_ids": data_ids},
            "msg": "文件夹内容 ID 已成功查询",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_data_dir(user_id: str, dir_id: str):
    """文件夹配置: 查询"""

    try:
        await resolve_parent_folder_id(user_id, dir_id, db_client["mongo_col"].data_dir)

        # 查询 MongoDB
        try:
            dir_list = (
                await db_client["mongo_col"]
                .data_dir.find(
                    {"user_id": user_id, "parent_dir_id": dir_id, "status": 0}
                )
                .to_list(None)
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        try:
            data_list = (
                await db_client["mongo_col"]
                .data_info.find(
                    {"user_id": user_id, "parent_dir_id": dir_id, "status": 0}
                )
                .to_list(None)
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        logger.info(
            f"{user_id:<15} | fetch dir directory: {dir_id}"
        )

        # 转换 _id 为字符串, 并将结果合并
        merged_results = []

        # 处理 dir_list
        for item in dir_list:
            item["dir_id"] = str(item.pop("_id"))
            item["create_time"] = item.get("create_time", datetime.now()).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            item["update_time"] = item.get("update_time", datetime.now()).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            merged_results.append(item)

        # 处理 data_list
        for item in data_list:
            item["data_id"] = str(item.pop("_id"))
            item["create_time"] = item.get("create_time", datetime.now()).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            item["update_time"] = item.get("update_time", datetime.now()).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            item["last_used_time"] = item.get(
                "last_used_time", datetime.now()
            ).strftime("%Y-%m-%d %H:%M:%S")
            item["include_tables"] = item.get("include_tables", [])
            item.pop("mschema", None)
            merged_results.append(item)

        return {
            "code": 200,
            "data": {"dir_info": merged_results},
            "msg": "文件夹配置已成功查询",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def update_data_dir(
    user_id: str,
    dir_id: str,
    dir_name: str | None,
    parent_dir_id: str | None,
):
    """文件夹配置: 修改"""

    try:
        dir_info = {
            "dir_name": dir_name,
            "parent_dir_id": parent_dir_id,
            "update_time": datetime.now(),
        }
        dir_info = {key: value for key, value in dir_info.items() if value is not None}
        dir_info["update_time"] = datetime.now()

        dir_object_id = convert_to_object_id(dir_id)
        if parent_dir_id:
            await resolve_parent_folder_id(
                user_id, parent_dir_id, db_client["mongo_col"].data_dir
            )

        # 更新 MongoDB
        try:
            result = await db_client["mongo_col"].data_dir.update_one(
                {"user_id": user_id, "_id": dir_object_id, "status": 0},
                {"$set": dir_info},
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2200, e=e)
        if not result.matched_count > 0:
            return return_error(code=2201)
        if not result.modified_count > 0:
            return return_error(code=2202)

        return {
            "code": 200,
            "data": {"dir_id": dir_id},
            "msg": "文件夹配置已成功修改",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")
