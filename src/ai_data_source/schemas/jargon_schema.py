from typing import *
from enum import Enum
from pydantic import BaseModel, Field

from src.ai_data_source.schemas.data_source_schema import (
    KeyWord,
    Page,
    DataID,
    DataIDs,
    DataName,
    TotalNumber,
    BaseResponse,
    BaseMetaInfo,
)


class JargonID(BaseModel):
    jargon_id: str = Field(
        ...,
        title="惯用语ID",
        description="惯用语的唯一标识符",
    )


class JargonIDs(BaseModel):
    jargon_ids: List[str] = Field(
        ...,
        title="惯用语ID列表",
        description="惯用语的唯一标识符列表",
    )


class JargonInfo(BaseModel):
    jargon_name: str | None = Field(
        ...,
        title="业务定义名称",
        description="用于定义企业内的某一通用概念，例如销售进展、财年，最多100个字符",
    )
    jargon_description: str | None = Field(
        "",
        title="名称解释",
        description="对该业务定义名称的具体说明，关联数据指标，以便模型对不同指标进行识别和关联理解，最多300个字符。",
    )
    is_rewrite: bool | None = Field(
        False,
        title="是否开启强制改写",
        description="开启后，用户提问中匹配到「业务定义名称」及「同义词」时，将会被改写为「名称解释」中的内容，",
    )
    similar_words: List[str] | None = Field(
        [],
        title="名称的同义词",
        description="用于定义该业务名称的在企业内的不同叫法，以便模型识别不同的问法",
    )


class DataIDName(DataID, DataName):
    pass


class DataIDNameList(BaseModel):
    data_list: List[DataIDName] = Field(
        ...,
        title="数据库的ID与名称列表",
        description="包含的数据库的ID与名称列表",
    )


class JargonMetaInfo(JargonID, JargonInfo, DataIDNameList, BaseMetaInfo):
    pass


class JargonMetaInfos(TotalNumber):
    jargon_infos: List[JargonMetaInfo] = Field(
        ..., title="惯用语列表", description="包含分页后的惯用语信息"
    )


class UploadJargonID(BaseModel):
    insert_jargon_ids: List[str] = Field(
        ...,
        title="新建的惯用语ID列表",
        description="新建的惯用语的唯一标识符列表",
    )
    update_jargon_ids: List[str] = Field(
        ...,
        title="更新的惯用语ID列表",
        description="更新的惯用语的唯一标识符列表",
    )


################################################################################


class CreateJargonInfo(JargonInfo, DataIDs):
    pass


class DeleteJargonInfo(JargonIDs):
    pass


class UpdateJargonInfo(JargonID, JargonInfo, DataIDs):
    pass


class UpdateJargonScope(JargonIDs, DataIDs):
    pass


class FetchJargonInfo(KeyWord, Page, DataIDs):
    pass


################################################################################


class CreateJargonInfoResponse(BaseResponse):
    data: JargonID | None = Field(..., title="数据", description="具体内容")


class DeleteJargonInfoResponse(BaseResponse):
    data: JargonIDs | None = Field(..., title="数据", description="具体内容")


class UpdateJargonInfoResponse(BaseResponse):
    data: JargonID | None = Field(..., title="数据", description="具体内容")


class UpdateJargonScopeResponse(BaseResponse):
    data: JargonIDs | None = Field(..., title="数据", description="具体内容")


class FetchJargonInfoResponse(BaseResponse):
    data: JargonMetaInfos | None = Field(..., title="数据", description="具体内容")


class UploadJargonExcelResponse(BaseResponse):
    data: UploadJargonID | None = Field(..., title="数据", description="具体内容")
