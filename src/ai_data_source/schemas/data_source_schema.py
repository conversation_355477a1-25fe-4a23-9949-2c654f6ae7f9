from typing import *
from pydantic import BaseModel, Field
from src.sql_engine.utils.db_util import BackStatusEnum


class KeyWord(BaseModel):
    key_word: str = Field("", title="关键词", description="支持模糊搜索")


class Page(BaseModel):
    page_size: int = Field(25, title="每页数量", description="默认为 25", examples=[25])
    page_number: int = Field(1, title="分页页码", description="默认为 1", examples=[1])
    enable_page: bool = Field(
        True,
        title="是否开启分页",
        description="关闭时, page_size 与 page_number 无效",
        examples=[True, False],
    )


class GenDescStatus(BaseModel):
    desc_gen_status: BackStatusEnum = Field(
        BackStatusEnum.PENDING, title="生成状态", description="语义注释的生成状态"
    )


class ExampleSyncStatus(BaseModel):
    example_sync_status: BackStatusEnum = Field(
        BackStatusEnum.PENDING, title="同步状态", description="示例数据的同步状态"
    )


class StatisticsSyncStatus(BaseModel):
    statistics_sync_status: BackStatusEnum = Field(
        BackStatusEnum.PENDING, title="同步状态", description="统计信息的同步状态"
    )


class DataType(BaseModel):
    data_type: str | None = Field(
        None, title="数据源类型", description="例如 mysql, excel 等", examples=["mysql"]
    )


class DataName(BaseModel):
    data_name: str | None = Field(
        None,
        title="数据源名称",
        description="原始数据库的名称",
        examples=["xiyan_data"],
    )


class DataPath(BaseModel):
    data_path: str | None = Field(
        None, title="数据源路径", description="只对 excel 文件类型有效"
    )


class DataHost(BaseModel):
    data_host: str | None = Field(
        None,
        title="主机地址",
        description="数据源的主机地址",
        examples=["************"],
    )


class DataPort(BaseModel):
    data_port: str | None = Field(
        None, title="端口号", description="数据源的端口号", examples=["30202"]
    )


class DataUser(BaseModel):
    data_user: str | None = Field(
        None, title="用户名", description="数据库的用户名", examples=["root"]
    )


class DataPwd(BaseModel):
    data_pwd: str | None = Field(
        None, title="密码", description="数据库的密码", examples=["<EMAIL>"]
    )


class DataConfig(DataType, DataName, DataPath, DataHost, DataPort, DataUser, DataPwd):
    pass


class IncludeTables(BaseModel):
    include_tables: List[str] | None = Field(
        None,
        title="数据表名列表",
        description="注意：传入空数组时表示全部, 更新不传值时表示不做修改",
        examples=[[], ["orders", "customers"]],
    )


class ReMarkName(BaseModel):
    rename: str | None = Field(
        None, title="中文名", description="便于显示的中文名称, 更新不传值时表示不做修改"
    )
    remark: str | None = Field(
        None, title="语义解释", description="用于额外补充用途, 更新不传值时表示不做修改"
    )


class ParentDir(BaseModel):
    parent_dir_id: str | None = Field(
        None,
        title="父目录 ID",
        description="父目录的唯一标识符, 空字符串代表根目录, 更新不传值时表示不做修改",
        examples=[""],
    )


class DataConnConfig(BaseModel):
    data_config: DataConfig | None = Field(
        None, title="数据源配置", description="详细配置参数, 更新不传值时表示不做修改"
    )


class DataID(BaseModel):
    data_id: str = Field(
        ...,
        title="数据源 ID",
        description="数据源的唯一标识符",
        examples=["67e2703743e482eb4b327538"],
    )


class DataIDs(BaseModel):
    data_ids: List[str] | None = Field(
        [],
        title="数据源 ID 列表",
        description="数据源的唯一标识符的列表",
    )


class DirID(BaseModel):
    dir_id: str = Field(
        ...,
        title="目录 ID",
        description="目录的唯一标识符, 空字符串代表根目录",
        examples=[""],
    )


class DirIDs(BaseModel):
    dir_ids: list[str] = Field(
        [], title="目录 ID 列表", description="目录的唯一标识符的列表"
    )


class DirName(BaseModel):
    dir_name: str | None = Field(
        ...,
        title="目录名称",
        description="目录的名称, 更新不传值时表示不做修改",
        examples=["xiyan"],
    )


class Recursive(BaseModel):
    recursive: bool = Field(
        False,
        title="递归标志",
        description="是否递归查询子目录",
        examples=[True, False],
    )


class TotalNumber(BaseModel):
    total_number: int = Field(..., title="数据总数", description="数据库中数据的数量")


class BaseResponse(BaseModel):
    code: int = Field(..., title="响应代码", description="响应的状态代码")
    msg: str = Field(..., title="响应消息", description="响应的消息内容")


class UserID(BaseModel):
    user_id: str = Field(..., title="用户 ID", description="用户的唯一标识符")


class Status(BaseModel):
    status: int = Field(
        ..., title="状态", description="数据的状态, 0 表示正常，1 表示删除"
    )


class CUTime(BaseModel):
    create_time: str = Field(..., title="创建时间", description="数据的创建时间")
    update_time: str = Field(..., title="更新时间", description="数据的更新时间")


class BaseMetaInfo(UserID, Status, CUTime):
    pass


class TableName(BaseModel):
    table_name: str = Field(
        ...,
        title="原始表名",
        description="数据表的原始名称（数据库中的名称）",
        examples=["orders"],
    )


class TableNames(BaseModel):
    table_names: list | None = Field(
        None,
        title="表名列表",
        description="数据表的列表，不传值代表选择全部数据表",
        examples=[["orders"]],
    )


class ColumnName(BaseModel):
    column_name: str = Field(
        ...,
        title="原始列名",
        description="字段的原始名称（数据库中的名称）",
        examples=["order_id"],
    )


class ColumnNames(BaseModel):
    column_names: list | None = Field(
        None,
        title="列名列表",
        description="数据库中字段的列表",
        examples=[["order_id"]],
    )


class TableComment(BaseModel):
    # TODO 修改
    table_comment: str = Field(..., title="表注释", description="数据表的注释")


class Comment(BaseModel):
    comment: str = Field(..., title="原始注释", description="数据库中的原始注释")


class TableInfo(TableName, TableComment):
    # TODO 修改
    pass


class TableInfos(TotalNumber):
    table_infos: List[TableInfo] = Field(
        ..., title="数据表信息", description="所有的数据表列表信息"
    )


class FileUrl(BaseModel):
    file_url: str = Field(..., title="文件 url", description="文件在 minio 中的 url")


class LastUsedTime(BaseModel):
    last_used_time: str = Field(
        ..., title="使用时间", description="数据源的最近使用时间"
    )


class DataMetaInfo(
    BaseMetaInfo, DataID, LastUsedTime, DataConnConfig, IncludeTables, ReMarkName, GenDescStatus
):
    pass


class DataInfoData(BaseModel):
    data_info: DataMetaInfo = Field(
        ..., title="数据源信息", description="数据源的全部信息"
    )


class DataInfosData(TotalNumber):
    data_infos: List[DataMetaInfo] = Field(
        ..., title="数据源信息列表", description="数据源全部信息的列表"
    )


class DirMetaInfo(BaseMetaInfo, DirID, DirName, ParentDir):
    pass


class DirContents(BaseModel):
    # TODO 调整名称
    dir_info: List[Union[DirMetaInfo, DataMetaInfo]] = Field(
        ..., title="目录信息", description="目录的详细信息，包括目录信息和数据源信息"
    )


class DataDirIDs(DataIDs, DirIDs):
    pass


################################################################################


class TestDataConnect(DataConnConfig):
    pass


class ViewDataAllTable(DataConnConfig, Page, KeyWord):
    pass


class CreateDataSource(DataConnConfig, IncludeTables, ReMarkName, ParentDir):
    pass


class DeleteDataSource(DataID):
    pass


class UpdateDataSource(DataID, ReMarkName, DataConnConfig, IncludeTables, ParentDir):
    pass


class FetchDataSourceByID(DataID):
    pass


class FetchDataSourceByTime(Page):
    pass


class FetchDataSourceByNameType(DataName, DataType, Page):
    pass


class CreateDataDir(DirName, ParentDir):
    pass


class DeleteDataDir(DirID):
    pass


class UpdateDataDir(DirID, DirName, ParentDir):
    pass


class FetchDataDir(DirID):
    pass


class FetchDirSubID(DirID, Recursive):
    pass


################################################################################


class TestDataConnectResponse(BaseResponse):
    data: dict | None = Field(..., title="数据", description="具体内容")


class ViewDataAllTableResponse(BaseResponse):
    data: TableInfos | None = Field(..., title="数据", description="具体内容")


class UploadExcelFileResponse(BaseResponse):
    data: FileUrl | None = Field(..., title="数据", description="具体内容")


class CreateDataSourceResponse(BaseResponse):
    data: DataID | None = Field(..., title="数据", description="具体内容")


class DeleteDataSourceResponse(BaseResponse):
    data: DataID | None = Field(..., title="数据", description="具体内容")


class UpdateDataSourceResponse(BaseResponse):
    data: DataID | None = Field(..., title="数据", description="具体内容")


class FetchDataSourceByIDResponse(BaseResponse):
    data: DataInfoData | None = Field(..., title="数据", description="具体内容")


class FetchDataSourceByTimeResponse(BaseResponse):
    data: DataInfosData | None = Field(..., title="数据", description="具体内容")


class FetchDataSourceByNameTypeResponse(BaseResponse):
    data: DataInfosData | None = Field(..., title="数据", description="具体内容")


class CreateDataDirResponse(BaseResponse):
    data: DirID | None = Field(..., title="数据", description="具体内容")


class DeleteDataDirResponse(BaseResponse):
    data: DirID | None = Field(..., title="数据", description="具体内容")


class UpdateDataDirResponse(BaseResponse):
    data: DirID | None = Field(..., title="数据", description="具体内容")


class FetchDataDirResponse(BaseResponse):
    data: DirContents | None = Field(..., title="数据", description="具体内容")


class FetchDirSubIDResponse(BaseResponse):
    data: DataDirIDs | None = Field(..., title="数据", description="具体内容")
