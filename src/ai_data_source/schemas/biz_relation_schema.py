from typing import *
from pydantic import BaseModel, Field

from src.ai_data_source.schemas.data_source_schema import (
    Page,
    KeyWord,
    DataID,
    TableNames,
    TotalNumber,
    BaseMetaInfo,
    BaseResponse,
)


class BizID(BaseModel):
    biz_id: str = Field(
        ...,
        title="milvus ID",
        description="数据在 milvus 中的ID",
    )


class BizIDs(BaseModel):
    biz_ids: List[str] = Field(
        ...,
        title="milvus IDs",
        description="数据在 milvus 中的ID 列表",
    )


class BizName(BaseModel):
    biz_name: str | None = Field(
        ...,
        title="业务名称",
        description="更新不传值时表示不做修改",
        examples=["客户与订单"],
    )


class BizAskDesc(BaseModel):
    biz_ask: str | None = Field(
        "",
        title="业务问题",
        description="更新不传值时表示不做修改",
        examples=["每个客户的有多少订单？"],
    )
    biz_desc: str | None = Field(
        "",
        title="业务描述",
        description="更新不传值时表示不做修改",
        examples=["包含客户信息与订单信息"],
    )


class BizInfo(DataID, TableNames, BizName, BizAskDesc):
    pass


class BizMetaInfo(BizID, BizInfo, BaseMetaInfo):
    pass


class BizMetaInfos(TotalNumber):
    biz_infos: List[BizMetaInfo] = Field(
        ...,
        title="业务关联表列表",
        description="包含分页后的业务关联表信息",
    )


################################################################################


class CreateBizRelation(BizInfo):
    pass


class DeleteBizRelation(BizIDs):
    pass


class UpdateBizRelation(BizID, BizInfo):
    pass


class FetchBizRelation(Page, KeyWord):
    pass


################################################################################


class CreateBizRelationResponse(BaseResponse):
    data: BizID | None = Field(..., title="数据", description="具体内容")


class DeleteBizRelationResponse(BaseResponse):
    data: BizIDs | None = Field(..., title="数据", description="具体内容")


class UpdateBizRelationResponse(BaseResponse):
    data: BizID | None = Field(..., title="数据", description="具体内容")


class FetchBizRelationResponse(BaseResponse):
    data: BizMetaInfos | None = Field(..., title="数据", description="具体内容")
