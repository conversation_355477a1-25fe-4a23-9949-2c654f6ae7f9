from typing import *
from enum import Enum
from pydantic import BaseModel, Field

from src.ai_chat_data.schemas.chat_data_schema import UserAsk
from src.ai_data_source.schemas.jargon_schema import DataIDNameList
from src.ai_chat_data.schemas.chat_llm_schema import QAID, TalkID
from src.ai_data_source.schemas.data_source_schema import (
    Page,
    KeyWord,
    DataIDs,
    TotalNumber,
    BaseResponse,
    BaseMetaInfo,
)


class FewShotID(BaseModel):
    few_shot_id: str = Field(
        ...,
        title="问答范例 ID",
        description="问答范例的唯一标识符",
        examples=["67cfd15bc0b3c64a66ccbbdb"],
    )


class FewShotIDs(BaseModel):
    few_shot_ids: List[str] = Field(
        ...,
        title="问答范例 ID 列表",
        description="问答范例的唯一标识符的列表",
        examples=[["67cfd15bc0b3c64a66ccbbdb"]],
    )


class ExampleEnum(str, Enum):
    SQL = "sql"
    TEMPLATE = "template"


class ExampleType(BaseModel):
    example_type: ExampleEnum = Field(
        ...,
        title="例子类型",
        description="例子的类型",
        examples=["sql"],
    )


class Example(BaseModel):
    example: str = Field(
        ...,
        title="例子",
        description="同于few shot 的例子",
        examples=["SELECT * FROM sales WHERE year = 2024"],
    )


class FewShotInfo(UserAsk, Example):
    pass


class FewShotMetaInfo(
    FewShotID, ExampleType, FewShotInfo, DataIDNameList, BaseMetaInfo
):
    pass


class FewShotMetaInfos(TotalNumber):
    few_shot_infos: List[FewShotMetaInfo] = Field(
        ..., title="问答范例列表", description="包含分页后的问答范例信息"
    )


################################################################################


class CreateFewShot(ExampleType, FewShotInfo, DataIDs, QAID, TalkID):
    pass


class DeleteFewShot(FewShotIDs, QAID, TalkID):
    pass


class UpsertFewShot(FewShotID, FewShotInfo, DataIDs):
    pass


class FetchFewShot(Page, KeyWord):
    pass


################################################################################


class CreateFewShotResponse(BaseResponse):
    data: FewShotID | None = Field(..., title="数据", description="具体内容")


class DeleteFewShotResponse(BaseResponse):
    data: FewShotIDs | None = Field(..., title="数据", description="具体内容")


class UpsertFewShotResponse(BaseResponse):
    data: FewShotID | None = Field(..., title="数据", description="具体内容")


class FetchFewShotResponse(BaseResponse):
    data: FewShotMetaInfos | None = Field(..., title="数据", description="具体内容")
