from typing import *
from enum import Enum
from pydantic import BaseModel, Field

from src.ai_data_source.schemas.data_source_schema import (
    DataID,
    DataName,
    Page,
    KeyWord,
    BaseResponse,
    BaseMetaInfo,
    TotalNumber,
    TableName,
    TableNames,
    ColumnName,
    ColumnNames,
    ReMarkName,
    Comment,
    GenDescStatus,
    ExampleSyncStatus,
    StatisticsSyncStatus,
)


class IsBackTask(BaseModel):
    is_background_tasks: bool = Field(
        False,
        title="后台任务",
        description="是否为后台任务",
        examples=[True, False],
    )


class CategoryEnum(str, Enum):
    CODE = "Code"
    ENUM = "Enum"
    DATETIME = "DateTime"
    TEXT = "Text"
    MEASURE = "Measure"
    NONE = ""


class DimMeasureEnum(str, Enum):
    DIMENSION = "Dimension"
    MEASURE = "Measure"
    NONE = ""


class TimeMinGransEnum(str, Enum):
    YEAR = "YEAR"
    MONTH = "MONTH"
    DAY = "DAY"
    QUARTER = "QUARTER"
    WEEK = "WEEK"
    HOUR = "HOUR"
    MINUTE = "MINUTE"
    SECOND = "SECOND"
    MILLISECOND = "MILLISECOND"
    MICROSECOND = "MICROSECOND"
    OTHER = "OTHER"
    NONE = ""


class ColumnInfoChange(BaseModel):
    category: CategoryEnum | None = Field(
        None, title="类型", description="该列的数据分类（例如: CODE, TEXT等）"
    )
    data_range: List | None = Field(
        None, title="数据范围", description="该列数据的取值范围"
    )
    date_min_gran: TimeMinGransEnum | None = Field(
        None, title="最小粒度", description="该列的时间数据的最小粒度"
    )
    examples: List | None = Field(
        None, title="示例值", description="该列可能的示例数据值"
    )


class ColumnInfoDisplay(BaseModel):
    statistics: Dict | None = Field(None, title="统计值", description="该列的统计数据")
    dim_or_meas: DimMeasureEnum | None = Field(
        None, title="度量或度量", description="该列的数据是否为度量或度量"
    )


class CommentMode(str, Enum):
    SEPARATE = "separate"
    SEPARATE_MERGE = "separate_merge"


class GenConfig(BaseModel):
    ref_num: int = Field(
        3, title="参考数量", description="生成语义时参考其他列的数量", examples=[3]
    )
    comment_mode: CommentMode = Field(
        "separate_merge",
        title="生成模式",
        description="separate: 清除已有的描述信息，完全由模型生成; separate_merge: 没有描述的生成描述信息；已经有描述信息的，不再生成",
        examples=["separate_merge"],
    )


class SampleSize(BaseModel):
    sample_size: int = Field(
        20, title="示例数据大小", description="要获取的示例数据条数"
    )


class TableInfo(
    DataName,
    TableName,
    Comment,
    ReMarkName,
    GenDescStatus,
    ExampleSyncStatus,
    StatisticsSyncStatus,
):
    primary_keys: List = Field(..., title="主键", description="该表的主键字段列表")
    foreign_keys: List = Field(..., title="外键", description="该表中的外键字段列表")
    unique_keys: List = Field(..., title="唯一约束", description="该表的唯一约束列表")
    indexs: List = Field(..., title="索引", description="该表中的外键字段列表")


class TableMetaInfo(BaseModel):
    table_info: TableInfo = Field(
        ..., title="数据表信息", description="数据表的详细信息"
    )


class TableMetaInfos(TotalNumber):
    table_infos: List[TableInfo] = Field(
        ..., title="数据表信息", description="所有的数据表列表信息"
    )


class ColumnInfo(
    ColumnName,
    Comment,
    ReMarkName,
    ColumnInfoChange,
    ColumnInfoDisplay,
    GenDescStatus,
    ExampleSyncStatus,
    StatisticsSyncStatus,
):
    autoincrement: bool = Field(..., title="是否自增", description="该列是否自增")
    default: str | None = Field(..., title="默认值", description="该列的默认值")
    nullable: bool = Field(..., title="是否为空", description="该列是否允许为空")
    unique: bool = Field(..., title="是否唯一", description="该列的值是否唯一")
    primary_key: bool = Field(..., title="是否为主键", description="该列是否为主键字段")
    type: str = Field(
        ..., title="类型", description="该列的数据类型（例如：VARCHAR, INT等）"
    )


class ColumnMetaInfo(TotalNumber):
    column_info: ColumnInfo = Field(
        ..., title="数据列信息", description="单个字段的信息"
    )


class ColumnMetaInfos(TotalNumber):
    column_infos: List[ColumnInfo] = Field(
        ..., title="字段信息的列表", description="多个字段信息的列表"
    )


class SampleData(BaseModel):
    # TODO: 修改继承 ColumnNames
    columns: List[str] = Field(..., title="列名", description="返回的列名")
    sample_data: List = Field(..., title="示例数据", description="返回的示例数据")


################################################################################


class FetchTableInfo(DataID, TableName):
    pass


class FetchTableList(DataID, Page, KeyWord):
    pass


class FetchColumnList(DataID, TableName, Page):
    pass


class UpdateTabelSemantic(DataID, TableName, ReMarkName):
    pass


class UpdateColumnSemantic(DataID, TableName, ColumnName, ReMarkName, ColumnInfoChange):
    pass


class FetchSampleData(DataID, TableName, SampleSize):
    pass


class GenSingleFieldDesc(DataID, TableName, ColumnName, GenConfig):
    pass


class GenSomeFieldDesc(DataID, TableName, ColumnNames, GenConfig):
    pass


class GenTableFieldDesc(DataID, TableNames, GenConfig):
    pass


class GenTableDesc(DataID, TableName, IsBackTask):
    pass


class GenDataDesc(DataID):
    pass


class GetFieldExamples(DataID, TableName, ColumnNames):
    pass


class GetFieldStatistics(DataID, TableName, ColumnNames):
    pass


class GetTableExamples(DataID, TableNames):
    pass


class GetTableStatistics(DataID, TableNames):
    pass


################################################################################


class FetchTableInfoResponse(BaseResponse):
    data: TableMetaInfo | None = Field(..., title="数据", description="具体内容")


class FetchTableListResponse(BaseResponse):
    data: TableMetaInfos | None = Field(..., title="数据", description="具体内容")


class FetchColumnListResponse(BaseResponse):
    data: ColumnMetaInfos | None = Field(..., title="数据", description="具体内容")


class UpdateTabelSemanticResponse(BaseResponse):
    data: DataID | None = Field(..., title="数据", description="具体内容")


class UpdateColumnSemanticResponse(BaseResponse):
    data: DataID | None = Field(..., title="数据", description="具体内容")


class FetchSampleDataResponse(BaseResponse):
    data: SampleData | None = Field(..., title="数据", description="具体内容")


class GenSingleFieldDescResponse(BaseResponse):
    data: ReMarkName | None = Field(..., title="数据", description="具体内容")


class GenDataDescResponse(BaseResponse):
    data: ReMarkName | None = Field(..., title="数据", description="具体内容")


class GenTableDescResponse(BaseResponse):
    data: ReMarkName | None = Field(..., title="数据", description="具体内容")


class EmptyResponse(BaseResponse):
    data: None = Field(..., title="数据", description="具体内容")
