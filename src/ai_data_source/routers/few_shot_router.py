from fastapi import APIRouter, Security

from src.common.utils.logger import logger
from src.common.auth.authorize import get_user_id

router = APIRouter()

from src.ai_data_source.schemas.few_shot_schema import CreateFewShot
from src.ai_data_source.schemas.few_shot_schema import CreateFewShotResponse
from src.ai_data_source.services.few_shot_service import create_few_shot


@router.post(
    "/create_few_shot",
    response_model=CreateFewShotResponse,
    summary="问答范例库: 创建范例",
)
async def _create_few_shot(
    input_data: CreateFewShot, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await create_few_shot(
        user_id=user_id,
        data_ids=input_data.data_ids,
        user_ask=input_data.user_ask,
        example=input_data.example,
        example_type=input_data.example_type,
        qa_id=input_data.qa_id,
        talk_id=input_data.talk_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.few_shot_schema import DeleteFewShot
from src.ai_data_source.schemas.few_shot_schema import DeleteFewShotResponse
from src.ai_data_source.services.few_shot_service import delete_few_shot


@router.post(
    "/delete_few_shot",
    response_model=DeleteFewShotResponse,
    summary="问答范例库: 删除范例",
)
async def _delete_few_shot(
    input_data: DeleteFewShot, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await delete_few_shot(
        user_id=user_id,
        few_shot_ids=input_data.few_shot_ids,
        qa_id=input_data.qa_id,
        talk_id=input_data.talk_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.few_shot_schema import UpsertFewShot
from src.ai_data_source.schemas.few_shot_schema import UpsertFewShotResponse
from src.ai_data_source.services.few_shot_service import upsert_few_shot


@router.post(
    "/upsert_few_shot",
    response_model=UpsertFewShotResponse,
    summary="问答范例库: 更新范例",
)
async def _upsert_few_shot(
    input_data: UpsertFewShot, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await upsert_few_shot(
        user_id=user_id,
        few_shot_id=input_data.few_shot_id,
        data_ids=input_data.data_ids,
        user_ask=input_data.user_ask,
        example=input_data.example,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.few_shot_schema import FetchFewShot
from src.ai_data_source.schemas.few_shot_schema import FetchFewShotResponse
from src.ai_data_source.services.few_shot_service import fetch_few_shot


@router.post(
    "/fetch_few_shot",
    response_model=FetchFewShotResponse,
    summary="问答范例库: 查询范例",
)
async def _fetch_few_shot(
    input_data: FetchFewShot, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_few_shot(
        user_id=user_id,
        page_size=input_data.page_size,
        page_number=input_data.page_number,
        enable_page=input_data.enable_page,
        key_word=input_data.key_word,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data
