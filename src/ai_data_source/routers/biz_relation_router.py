from fastapi import APIRouter, Security

from src.common.utils.logger import logger
from src.common.auth.authorize import get_user_id

router = APIRouter()

from src.ai_data_source.schemas.biz_relation_schema import CreateBizRelation
from src.ai_data_source.schemas.biz_relation_schema import CreateBizRelationResponse
from src.ai_data_source.services.biz_relation_service import create_biz_relation


@router.post(
    "/create_biz_relation",
    response_model=CreateBizRelationResponse,
    summary="业务关联表: 创建关联表",
)
async def _create_biz_relation(
    input_data: CreateBizRelation, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await create_biz_relation(
        user_id=user_id,
        data_id=input_data.data_id,
        biz_name=input_data.biz_name,
        biz_ask=input_data.biz_ask,
        biz_desc=input_data.biz_desc,
        table_names=input_data.table_names,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.biz_relation_schema import DeleteBizRelation
from src.ai_data_source.schemas.biz_relation_schema import DeleteBizRelationResponse
from src.ai_data_source.services.biz_relation_service import delete_biz_relation


@router.post(
    "/delete_biz_relation",
    response_model=DeleteBizRelationResponse,
    summary="业务关联表: 删除关联表",
)
async def _delete_biz_relation(
    input_data: DeleteBizRelation, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await delete_biz_relation(
        user_id=user_id,
        biz_ids=input_data.biz_ids,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.biz_relation_schema import UpdateBizRelation
from src.ai_data_source.schemas.biz_relation_schema import UpdateBizRelationResponse
from src.ai_data_source.services.biz_relation_service import update_biz_relation


@router.post(
    "/update_biz_relation",
    response_model=UpdateBizRelationResponse,
    summary="业务关联表: 更新关联表",
)
async def _update_biz_relation(
    input_data: UpdateBizRelation, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_biz_relation(
        user_id=user_id,
        biz_id=input_data.biz_id,
        data_id=input_data.data_id,
        biz_name=input_data.biz_name,
        biz_ask=input_data.biz_ask,
        biz_desc=input_data.biz_desc,
        table_names=input_data.table_names,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.biz_relation_schema import FetchBizRelation
from src.ai_data_source.schemas.biz_relation_schema import FetchBizRelationResponse
from src.ai_data_source.services.biz_relation_service import fetch_biz_relation


@router.post(
    "/fetch_biz_relation",
    response_model=FetchBizRelationResponse,
    summary="业务关联表: 查询关联表",
)
async def _fetch_biz_relation(
    input_data: FetchBizRelation, user_id: str = Security(get_user_id)
):
    """业务关联表管理: 查询"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_biz_relation(
        user_id=user_id,
        page_size=input_data.page_size,
        page_number=input_data.page_number,
        key_word=input_data.key_word,
        enable_page=input_data.enable_page,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data
