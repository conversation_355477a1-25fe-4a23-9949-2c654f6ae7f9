from fastapi import APIRouter, Security, UploadFile, File, Form

from src.common.utils.logger import logger
from src.common.auth.authorize import get_user_id
from src.common.utils.config import FILES_DIR
from typing import *

router = APIRouter()


from src.ai_data_source.schemas.jargon_schema import CreateJargonInfo
from src.ai_data_source.schemas.jargon_schema import CreateJargonInfoResponse
from src.ai_data_source.services.jargon_service import create_jargon_data


@router.post(
    "/create_jargon_data",
    response_model=CreateJargonInfoResponse,
    summary="行业惯用语: 新增惯用语",
)
async def _create_jargon_data(
    input_data: CreateJargonInfo, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await create_jargon_data(
        user_id=user_id,
        jargon_name=input_data.jargon_name,
        jargon_description=input_data.jargon_description,
        similar_words=input_data.similar_words,
        is_rewrite=input_data.is_rewrite,
        data_ids=input_data.data_ids,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_data_source.schemas.jargon_schema import DeleteJargonInfo
from src.ai_data_source.schemas.jargon_schema import DeleteJargonInfoResponse
from src.ai_data_source.services.jargon_service import delete_jargon_data


@router.post(
    "/delete_jargon_data",
    response_model=DeleteJargonInfoResponse,
    summary="行业惯用语: 删除惯用语",
)
async def _delete_jargon_data(
    input_data: DeleteJargonInfo, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await delete_jargon_data(
        user_id=user_id,
        jargon_ids=input_data.jargon_ids,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_data_source.schemas.jargon_schema import UpdateJargonInfo
from src.ai_data_source.schemas.jargon_schema import UpdateJargonInfoResponse
from src.ai_data_source.services.jargon_service import update_jargon_data


@router.post(
    "/update_jargon_data",
    response_model=UpdateJargonInfoResponse,
    summary="行业惯用语: 修改惯用语",
)
async def _update_jargon_data(
    input_data: UpdateJargonInfo, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_jargon_data(
        user_id=user_id,
        jargon_id=input_data.jargon_id,
        jargon_name=input_data.jargon_name,
        jargon_description=input_data.jargon_description,
        similar_words=input_data.similar_words,
        is_rewrite=input_data.is_rewrite,
        data_ids=input_data.data_ids,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_data_source.schemas.jargon_schema import UpdateJargonScope
from src.ai_data_source.schemas.jargon_schema import UpdateJargonScopeResponse
from src.ai_data_source.services.jargon_service import update_jargon_scope


@router.post(
    "/update_jargon_scope",
    response_model=UpdateJargonScopeResponse,
    summary="行业惯用语: 批量修改范围",
)
async def _update_jargon_scope(
    input_data: UpdateJargonScope, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_jargon_scope(
        user_id=user_id,
        jargon_ids=input_data.jargon_ids,
        data_ids=input_data.data_ids,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_data_source.schemas.jargon_schema import FetchJargonInfo
from src.ai_data_source.schemas.jargon_schema import FetchJargonInfoResponse
from src.ai_data_source.services.jargon_service import fetch_jargon_info


@router.post(
    "/fetch_jargon_info",
    response_model=FetchJargonInfoResponse,
    summary="行业惯用语: 查询惯用语",
)
async def _fetch_jargon_info(
    input_data: FetchJargonInfo, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_jargon_info(
        user_id=user_id,
        page_number=input_data.page_number,
        page_size=input_data.page_size,
        enable_page=input_data.enable_page,
        key_word=input_data.key_word,
        data_ids=input_data.data_ids,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_data_source.schemas.jargon_schema import UploadJargonExcelResponse
from src.ai_data_source.services.jargon_service import upload_jargon_excel


@router.post(
    "/upload_jargon_excel",
    response_model=UploadJargonExcelResponse,
    summary="行业惯用语: 上传文件",
)
async def _upload_jargon_excel(
    data_ids: List[str] = Form(
        ...,
        title="惯用语生效范围的文件id列表",
        description="此惯用语生效的所有数据文件的id",
        examples=["678352e46e5216e2ba0947e4", "6786053d6e5216e2ba094816"],
    ),
    files: List[UploadFile] = File(...),
    user_id: str = Security(get_user_id),
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | data_ids: {data_ids}"
    )

    response_data = await upload_jargon_excel(
        user_id=user_id,
        files=files,
        data_ids=data_ids,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from fastapi.responses import FileResponse


@router.post(
    "/download_jargon_example_file",
    response_class=FileResponse,
    summary="行业惯用语: 下载模板文件",
)
async def _download_example_file(user_id: str = Security(get_user_id)):
    """ """

    # 构建文件路径
    file_name = "行业惯用语表格模板.xlsx"
    file_path = FILES_DIR / file_name

    # 判断文件是否存在
    if not file_path.exists() or not file_path.is_file():
        logger.error(f"File {file_name} not found for user {user_id}")
        return {"error": "File not found"}

    # 返回文件
    logger.info(
        f"{user_id:<15} | download | {file_name}"
    )
    return FileResponse(
        file_path, media_type="application/octet-stream", filename=file_name
    )
