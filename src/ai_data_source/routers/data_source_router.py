import os
import sys

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.auth.authorize import get_user_id

from typing import *
from fastapi import APIRouter, Security

router = APIRouter()

from src.ai_data_source.schemas.data_source_schema import TestDataConnect
from src.ai_data_source.schemas.data_source_schema import TestDataConnectResponse
from src.ai_data_source.services.data_source_service import test_data_connect


@router.post(
    "/test_data_connect",
    response_model=TestDataConnectResponse,
    summary="数据源管理: 连接测试",
)
async def _test_data_connect(
    input_data: TestDataConnect, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await test_data_connect(
        user_id=user_id,
        data_config=input_data.data_config,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import ViewDataAllTable
from src.ai_data_source.schemas.data_source_schema import ViewDataAllTableResponse
from src.ai_data_source.services.data_source_service import view_data_info


@router.post(
    "/view_data_info",
    response_model=ViewDataAllTableResponse,
    summary="数据源管理: 检视数据表",
)
async def _view_data_info(
    input_data: ViewDataAllTable, user_id: str = Security(get_user_id)
):
    """ """
    # TODO: 修改函数名称

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await view_data_info(
        user_id=user_id,
        data_config=input_data.data_config,
        page_size=input_data.page_size,
        page_number=input_data.page_number,
        enable_page=input_data.enable_page,
        key_word=input_data.key_word,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import UploadExcelFileResponse
from src.ai_data_source.services.data_source_service import upload_excel_file
from fastapi import UploadFile, File


@router.post(
    "/upload_excel_file",
    response_model=UploadExcelFileResponse,
    summary="数据源管理: 上传 excel 文件",
)
async def _upload_excel_file(
    file: UploadFile = File(...),
    user_id: str = Security(get_user_id),
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {file.filename}"
    )

    response_data = await upload_excel_file(
        user_id=user_id,
        file=file,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import CreateDataSource
from src.ai_data_source.schemas.data_source_schema import CreateDataSourceResponse
from src.ai_data_source.services.data_source_service import create_data_config


@router.post(
    "/create_data_config",
    response_model=CreateDataSourceResponse,
    summary="数据源管理: 新建数据源",
)
async def _create_data_config(
    input_data: CreateDataSource, user_id: str = Security(get_user_id)
):
    """ """
    # TODO: 修改函数名称

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await create_data_config(
        user_id=user_id,
        data_config=input_data.data_config,
        include_tables=input_data.include_tables,
        rename=input_data.rename,
        remark=input_data.remark,
        parent_dir_id=input_data.parent_dir_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import DeleteDataSource
from src.ai_data_source.schemas.data_source_schema import DeleteDataSourceResponse
from src.ai_data_source.services.data_source_service import delete_data_source


@router.post(
    "/delete_data_source",
    response_model=DeleteDataSourceResponse,
    summary="数据源管理: 删除数据源",
)
async def _delete_data_source(
    input_data: DeleteDataSource,
    user_id: str = Security(get_user_id),
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await delete_data_source(
        user_id=user_id,
        data_id=input_data.data_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import UpdateDataSource
from src.ai_data_source.schemas.data_source_schema import UpdateDataSourceResponse
from src.ai_data_source.services.data_source_service import update_data_source


@router.post(
    "/update_data_source",
    response_model=UpdateDataSourceResponse,
    summary="数据源管理: 修改数据源",
)
async def _update_data_source(
    input_data: UpdateDataSource, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_data_source(
        user_id=user_id,
        data_id=input_data.data_id,
        data_config=input_data.data_config,
        include_tables=input_data.include_tables,
        rename=input_data.rename,
        remark=input_data.remark,
        parent_dir_id=input_data.parent_dir_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import FetchDataSourceByID
from src.ai_data_source.schemas.data_source_schema import FetchDataSourceByIDResponse
from src.ai_data_source.services.data_source_service import fetch_data_source_dy_id


@router.post(
    "/fetch_data_source_dy_id",
    response_model=FetchDataSourceByIDResponse,
    summary="数据源管理: 查询 by ID",
)
async def _fetch_data_source_dy_id(
    input_data: FetchDataSourceByID, user_id: str = Security(get_user_id)
):
    """ """
    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_data_source_dy_id(
        user_id=user_id,
        data_id=input_data.data_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import FetchDataSourceByTime
from src.ai_data_source.schemas.data_source_schema import FetchDataSourceByTimeResponse
from src.ai_data_source.services.data_source_service import fetch_data_sources_by_time


@router.post(
    "/fetch_data_sources_by_time",
    response_model=FetchDataSourceByTimeResponse,
    summary="数据源管理: 查询 by 时间",
)
async def _fetch_data_sources_by_time(
    input_data: FetchDataSourceByTime, user_id: str = Security(get_user_id)
):
    """ """
    # TODO 待删除
    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_data_sources_by_time(
        user_id=user_id,
        page_size=input_data.page_size,
        page_number=input_data.page_number,
        enable_page=input_data.enable_page,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import FetchDataSourceByNameType
from src.ai_data_source.schemas.data_source_schema import (
    FetchDataSourceByNameTypeResponse,
)
from src.ai_data_source.services.data_source_service import (
    fetch_data_sources_by_name_and_type,
)


@router.post(
    "/fetch_data_sources_by_name_and_type",
    response_model=FetchDataSourceByNameTypeResponse,
    summary="数据源管理: 查询 by 名称与类型",
)
async def _fetch_data_sources_by_name_and_type(
    input_data: FetchDataSourceByNameType, user_id: str = Security(get_user_id)
):
    """"""
    # TODO 待删除

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_data_sources_by_name_and_type(
        user_id=user_id,
        data_name=input_data.data_name,
        data_type=input_data.data_type,
        page_size=input_data.page_size,
        page_number=input_data.page_number,
        enable_page=input_data.enable_page,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import CreateDataDir
from src.ai_data_source.schemas.data_source_schema import CreateDataDirResponse
from src.ai_data_source.services.data_source_service import create_data_dir


@router.post(
    "/create_data_dir",
    response_model=CreateDataDirResponse,
    summary="数据源管理: 新建文件夹",
)
async def _create_data_dir(
    input_data: CreateDataDir, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await create_data_dir(
        user_id=user_id,
        dir_name=input_data.dir_name,
        parent_dir_id=input_data.parent_dir_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import DeleteDataDir
from src.ai_data_source.schemas.data_source_schema import DeleteDataDirResponse
from src.ai_data_source.services.data_source_service import delete_data_dir


@router.post(
    "/delete_data_dir",
    response_model=DeleteDataDirResponse,
    summary="数据源管理: 删除文件夹",
)
async def _delete_data_dir(
    input_data: DeleteDataDir, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await delete_data_dir(
        user_id=user_id,
        dir_id=input_data.dir_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )
    return response_data


from src.ai_data_source.schemas.data_source_schema import UpdateDataDir
from src.ai_data_source.schemas.data_source_schema import UpdateDataDirResponse
from src.ai_data_source.services.data_source_service import update_data_dir


@router.post(
    "/update_data_dir",
    response_model=UpdateDataDirResponse,
    summary="数据源管理: 修改文件夹",
)
async def _update_data_dir(
    input_data: UpdateDataDir, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_data_dir(
        user_id=user_id,
        dir_id=input_data.dir_id,
        dir_name=input_data.dir_name,
        parent_dir_id=input_data.parent_dir_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import FetchDataDir
from src.ai_data_source.schemas.data_source_schema import FetchDataDirResponse
from src.ai_data_source.services.data_source_service import fetch_data_dir


@router.post(
    "/fetch_data_dir",
    response_model=FetchDataDirResponse,
    summary="数据源管理: 查询文件夹",
)
async def _fetch_data_dir(
    input_data: FetchDataDir, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_data_dir(
        user_id=user_id,
        dir_id=input_data.dir_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.data_source_schema import FetchDirSubID
from src.ai_data_source.schemas.data_source_schema import FetchDirSubIDResponse
from src.ai_data_source.services.data_source_service import fetch_dir_sub_id


@router.post(
    "/fetch_dir_sub_id",
    response_model=FetchDirSubIDResponse,
    summary="数据源管理: 递归查询文件夹",
)
async def _fetch_dir_sub_id(
    input_data: FetchDirSubID, user_id: str = Security(get_user_id)
):
    """递归查询文件夹的所有子 dir 和 data"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_dir_sub_id(
        user_id=user_id,
        dir_id=input_data.dir_id,
        recursive=input_data.recursive,
    )
    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data
