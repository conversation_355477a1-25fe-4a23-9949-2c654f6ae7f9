import os
import sys

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.auth.authorize import get_user_id
from src.common.utils.error_codes import return_error
from src.ai_data_source.utils.data_source_utils import check_data_info_exists

from typing import *
from fastapi import APIRouter, Security, BackgroundTasks

router = APIRouter()

from src.ai_data_source.schemas.semantic_layer_schema import FetchTableInfo
from src.ai_data_source.schemas.semantic_layer_schema import FetchTableInfoResponse
from src.ai_data_source.services.semantic_layer_service import fetch_table_info


@router.post(
    "/fetch_table_info",
    response_model=FetchTableInfoResponse,
    summary="语义层配置: 查询单个表",
)
async def _fetch_table_info(
    input_data: FetchTableInfo, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_table_info(
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import FetchTableList
from src.ai_data_source.schemas.semantic_layer_schema import FetchTableListResponse
from src.ai_data_source.services.semantic_layer_service import fetch_table_list


@router.post(
    "/fetch_table_list",
    response_model=FetchTableListResponse,
    summary="语义层配置: 查询表列表",
)
async def _fetch_table_list(
    input_data: FetchTableList, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_table_list(
        user_id=user_id,
        data_id=input_data.data_id,
        page_size=input_data.page_size,
        page_number=input_data.page_number,
        enable_page=input_data.enable_page,
        key_word=input_data.key_word,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import FetchColumnList
from src.ai_data_source.schemas.semantic_layer_schema import FetchColumnListResponse
from src.ai_data_source.services.semantic_layer_service import fetch_column_list


@router.post(
    "/fetch_column_list",
    response_model=FetchColumnListResponse,
    summary="语义层配置: 查询字段列表",
)
async def _fetch_column_list(
    input_data: FetchColumnList, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_column_list(
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
        page_size=input_data.page_size,
        page_number=input_data.page_number,
        enable_page=input_data.enable_page,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import UpdateTabelSemantic
from src.ai_data_source.schemas.semantic_layer_schema import UpdateTabelSemanticResponse
from src.ai_data_source.services.semantic_layer_service import update_tabel_semantic


@router.post(
    "/update_tabel_semantic",
    response_model=UpdateTabelSemanticResponse,
    summary="语义层配置: 更新表语义",
)
async def _update_tabel_semantic(
    input_data: UpdateTabelSemantic, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_tabel_semantic(
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
        rename=input_data.rename,
        remark=input_data.remark,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import UpdateColumnSemantic
from src.ai_data_source.schemas.semantic_layer_schema import (
    UpdateColumnSemanticResponse,
)
from src.ai_data_source.services.semantic_layer_service import update_column_semantic


@router.post(
    "/update_column_semantic",
    response_model=UpdateColumnSemanticResponse,
    summary="语义层配置: 更新字段语义",
)
async def _update_column_semantic(
    input_data: UpdateColumnSemantic, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_column_semantic(
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
        column_name=input_data.column_name,
        rename=input_data.rename,
        remark=input_data.remark,
        examples=input_data.examples,
        data_range=input_data.data_range,
        category=input_data.category,
        date_min_gran=input_data.date_min_gran,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import FetchSampleData
from src.ai_data_source.schemas.semantic_layer_schema import FetchSampleDataResponse
from src.ai_data_source.services.semantic_layer_service import fetch_sample_data


@router.post(
    "/fetch_sample_data",
    response_model=FetchSampleDataResponse,
    summary="语义层配置: 获取表的示例数据",
)
async def _fetch_sample_data(
    input_data: FetchSampleData, user_id: str = Security(get_user_id)
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_sample_data(
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
        sample_size=input_data.sample_size,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GenSingleFieldDesc
from src.ai_data_source.schemas.semantic_layer_schema import GenSingleFieldDescResponse
from src.ai_data_source.services.semantic_layer_service import gen_single_field_desc


@router.post(
    "/gen_single_field_desc",
    response_model=GenSingleFieldDescResponse,
    summary="语义层配置: 生成数据表单个字段语义",
)
async def _gen_single_field_desc(
    input_data: GenSingleFieldDesc,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await gen_single_field_desc(
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
        column_name=input_data.column_name,
        ref_num=input_data.ref_num,
        comment_mode=input_data.comment_mode,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GenSomeFieldDesc
from src.ai_data_source.schemas.semantic_layer_schema import EmptyResponse
from src.ai_data_source.services.semantic_layer_service import gen_some_field_desc


@router.post(
    "/gen_some_field_desc",
    response_model=EmptyResponse,
    summary="语义层配置: 生成数据表部分字段语义",
)
async def _gen_some_field_desc(
    background_tasks: BackgroundTasks,
    input_data: GenSomeFieldDesc,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    if not await check_data_info_exists(user_id=user_id, data_id=input_data.data_id):
        return return_error(code=2401)

    background_tasks.add_task(
        gen_some_field_desc,
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
        column_names=input_data.column_names,
        ref_num=input_data.ref_num,
        comment_mode=input_data.comment_mode,
    )

    response_data = {
        "code": 200,
        "data": None,
        "msg": "正在生成语义",
    }

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GenTableFieldDesc
from src.ai_data_source.schemas.semantic_layer_schema import EmptyResponse
from src.ai_data_source.services.semantic_layer_service import gen_table_field_desc


@router.post(
    "/gen_table_field_desc",
    response_model=EmptyResponse,
    summary="语义层配置: 生成数据表全部字段语义",
)
async def _gen_table_field_desc(
    background_tasks: BackgroundTasks,
    input_data: GenTableFieldDesc,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    if not await check_data_info_exists(user_id=user_id, data_id=input_data.data_id):
        return return_error(code=2401)

    background_tasks.add_task(
        gen_table_field_desc,
        user_id=user_id,
        data_id=input_data.data_id,
        table_names=input_data.table_names,
        ref_num=input_data.ref_num,
        comment_mode=input_data.comment_mode,
    )

    response_data = {
        "code": 200,
        "data": None,
        "msg": "正在生成语义",
    }

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GenTableDesc
from src.ai_data_source.schemas.semantic_layer_schema import GenTableDescResponse
from src.ai_data_source.services.semantic_layer_service import gen_table_desc


@router.post(
    "/gen_table_desc",
    response_model=GenTableDescResponse,
    summary="语义层配置: 生成数据表语义",
)
async def _gen_table_desc(
    background_tasks: BackgroundTasks,
    input_data: GenTableDesc,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    if not await check_data_info_exists(user_id=user_id, data_id=input_data.data_id):
        return return_error(code=2401)

    if input_data.is_background_tasks:
        background_tasks.add_task(
            gen_table_desc,
            user_id=user_id,
            data_id=input_data.data_id,
            table_name=input_data.table_name,
            is_background_tasks=input_data.is_background_tasks,
        )

        response_data = {
            "code": 200,
            "data": None,
            "msg": "正在生成语义，请稍后查看",
        }
    else:
        response_data = await gen_table_desc(
            user_id=user_id,
            data_id=input_data.data_id,
            table_name=input_data.table_name,
            is_background_tasks=input_data.is_background_tasks,
        )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GenDataDesc
from src.ai_data_source.schemas.semantic_layer_schema import GenDataDescResponse
from src.ai_data_source.services.semantic_layer_service import gen_data_desc


@router.post(
    "/gen_data_desc",
    response_model=GenDataDescResponse,
    summary="语义层配置: 生成数据库语义",
)
async def _gen_data_desc(
    input_data: GenDataDesc,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await gen_data_desc(
        user_id=user_id,
        data_id=input_data.data_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GetFieldExamples
from src.ai_data_source.schemas.semantic_layer_schema import EmptyResponse
from src.ai_data_source.services.semantic_layer_service import get_field_examples


@router.post(
    "/get_field_examples",
    response_model=EmptyResponse,
    summary="语义层配置: 同步字段示例数据",
)
async def _get_field_examples(
    background_tasks: BackgroundTasks,
    input_data: GetFieldExamples,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    if not await check_data_info_exists(user_id=user_id, data_id=input_data.data_id):
        return return_error(code=2401)

    background_tasks.add_task(
        get_field_examples,
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
        column_names=input_data.column_names,
    )

    response_data = {
        "code": 200,
        "data": None,
        "msg": "正在同步示例数据",
    }

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GetFieldStatistics
from src.ai_data_source.schemas.semantic_layer_schema import EmptyResponse
from src.ai_data_source.services.semantic_layer_service import get_field_statistics


@router.post(
    "/get_field_statistics",
    response_model=EmptyResponse,
    summary="语义层配置: 同步字段统计数据",
)
async def _get_field_statistics(
    background_tasks: BackgroundTasks,
    input_data: GetFieldStatistics,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    if not await check_data_info_exists(user_id=user_id, data_id=input_data.data_id):
        return return_error(code=2401)

    background_tasks.add_task(
        get_field_statistics,
        user_id=user_id,
        data_id=input_data.data_id,
        table_name=input_data.table_name,
        column_names=input_data.column_names,
    )

    response_data = {
        "code": 200,
        "data": None,
        "msg": "正在同步统计信息",
    }

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GetTableExamples
from src.ai_data_source.schemas.semantic_layer_schema import EmptyResponse
from src.ai_data_source.services.semantic_layer_service import get_table_examples


@router.post(
    "/get_table_examples",
    response_model=EmptyResponse,
    summary="语义层配置: 同步表示例数据",
)
async def _get_table_examples(
    background_tasks: BackgroundTasks,
    input_data: GetTableExamples,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    if not await check_data_info_exists(user_id=user_id, data_id=input_data.data_id):
        return return_error(code=2401)

    background_tasks.add_task(
        get_table_examples,
        user_id=user_id,
        data_id=input_data.data_id,
        table_names=input_data.table_names,
    )

    response_data = {
        "code": 200,
        "data": None,
        "msg": "正在同步示例数据",
    }

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_data_source.schemas.semantic_layer_schema import GetTableStatistics
from src.ai_data_source.schemas.semantic_layer_schema import EmptyResponse
from src.ai_data_source.services.semantic_layer_service import get_table_statistics


@router.post(
    "/get_table_statistics",
    response_model=EmptyResponse,
    summary="语义层配置: 同步表统计数据",
)
async def _get_table_statistics(
    background_tasks: BackgroundTasks,
    input_data: GetTableStatistics,
    user_id: str = Security(get_user_id),
):
    """"""

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    if not await check_data_info_exists(user_id=user_id, data_id=input_data.data_id):
        return return_error(code=2401)

    background_tasks.add_task(
        get_table_statistics,
        user_id=user_id,
        data_id=input_data.data_id,
        table_names=input_data.table_names,
    )

    response_data = {
        "code": 200,
        "data": None,
        "msg": "正在同步统计信息",
    }

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data
