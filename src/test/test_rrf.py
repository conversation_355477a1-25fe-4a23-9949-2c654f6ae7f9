a = [
    {
        "ads_yptl_op_profit_sum_daily": 0.7146866321563721,
        "ads_yptl_op_st_inno_sum_daily": 0.7527271509170532,
        "ads_yptl_op_st_rd_input_daily": 0.3889716565608978,
        "ads_yptl_op_st_unit_ipr_daily": 0.4005822241306305,
        "ads_yptl_op_st_unit_patent_daily": 0.761462926864624,
    },
    {
        "ads_yptl_as_claim_dispos_detail_daily": 0.25800761580467224,
        "ads_yptl_ivs_three_lib_daily": 0.2590414583683014,
        "ads_yptl_op_income_mth_daily": 0.2579180896282196,
        "ads_yptl_op_income_sum_daily": 0.26489049196243286,
        "ads_yptl_op_prod_unit_tqcm_daily": 0.2607305943965912,
        "ads_yptl_op_profit_sum_daily": 0.2587974965572357,
        "ads_yptl_op_profit_unit_daily": 0.25789037346839905,
        "ads_yptl_op_sec_unit_income_daily": 0.5176171064376831,
        "ads_yptl_op_sec_unit_profit_daily": 0.5227597951889038,
        "ads_yptl_op_st_app_proj_detail_daily": 0.2593355178833008,
        "ads_yptl_op_st_high_unit_daily": 0.2607637345790863,
        "ads_yptl_op_st_inno_sum_daily": 0.25987428426742554,
        "ads_yptl_op_st_plan_proj_detail_daily": 0.257935106754303,
        "ads_yptl_op_st_rd_input_daily": 0.25844237208366394,
        "ads_yptl_op_st_study_proj_detail_daily": 0.257935106754303,
        "ads_yptl_op_st_unit_award_daily": 0.25875401496887207,
        "ads_yptl_op_st_unit_ipr_daily": 0.2729499042034149,
        "ads_yptl_op_st_unit_patent_daily": 0.5413217544555664,
        "ads_yptl_op_third_unit_income_daily": 0.5176171064376831,
        "ads_yptl_op_third_unit_profit_daily": 0.5227597951889038,
    },
]
def calculate_scores(dict_list):
    final_scores = {}
    for d in dict_list:
        for item in d:
            if item not in final_scores:
                final_scores[item] = 0
            if score := rrf_index(item, d):
                final_scores[item] += 1 / (60 + score)
                
    final_scores = sorted(final_scores.items(), key=lambda item: item[1], reverse=True)
    
    return final_scores

def rrf_index(key, dict):
    # 使用字典的 keys() 获取键的列表，查找 key 的索引
    try:
        return list(dict.keys()).index(key)+1
    except ValueError:
        return None


ans = calculate_scores(a)
import pprint
pprint.pprint(ans)