import sys
import os
from loguru import logger
sys.path.append(os.getcwd().split("/src")[0])

import asyncio
from src.sql_engine.m_schema.base_engine import get_connect

llm_data = {
    "data_name": "xiyan_data",
    "data_type": "mysql",
    "data_config": {
        "data_path": "",
        "data_host": "**************",
        "data_port": "3308",
        "data_user": "root",
        "data_pwd": "admin",
    },
    "include_tables": [],
}

# llm_data = {
#     "data_name": "XIYAN",
#     "data_type": "dm",
#     "data_config": {
#         "data_path": "",
#         "data_host": "*************",
#         "data_port": "5236",
#         "data_user": "sysdba",
#         "data_pwd": "SYSDBA",
#     },
#     "include_tables": [],
# }


async def main():
    try:
        
        data_conn = get_connect(
            llm_data["data_type"],
            llm_data["data_name"],
            llm_data["data_config"],
            llm_data["include_tables"],
        )
        
        # table_name = data_conn.get_table_names()
        # table_comment = data_conn.get_table_comment(table_name[0])
        
        # examples = data_conn.fetch_distinct_values("customers", "email", 5)
        # print(examples)
        
        
        # print(table_name)
        # print(table_comment)
        
        # table_columns = data_conn.get_table_columns(table_name[0])
        # print(table_columns)
        
        mschema = data_conn.mschema.dump()

        # print(mschema)
        import pprint

        pprint.pprint(
            mschema
        )
        
            
    except Exception as e:
        logger.exception(e)
    



asyncio.run(main())