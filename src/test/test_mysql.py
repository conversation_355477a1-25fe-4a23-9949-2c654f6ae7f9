import sys
import os
from loguru import logger
from fastapi import HTTPException

sys.path.append(os.getcwd().split("/src")[0])

import asyncio

from src.sql_engine.utils.db_config import DBConfig
from src.sql_engine.utils.db_init import init_db_conn
from src.sql_engine.utils.db_source import HITLSQLDatabase
from src.sql_engine.database_env import DataBaseEnv
from src.common.utils.error_codes import return_error
from src.sql_engine.utils.db_mschema import MSchema

db = DBConfig(
    db_type="mysql",
    db_name="ovitboot-data",
    user_name="root",
    db_pwd="<EMAIL>",
    db_host="************",
    port="30202",
)

import pprint


async def main():
    try:
        # mschema=MSchema()
        # mschema.load_from_file(file_path="/home/<USER>/project/bowen-aidata-backend/src/test/test_mschema_2.json")
        # db_source = init_db_conn(db,mschema=mschema)
        
        db_source = init_db_conn(db, include_tables=["act_app_appdef"])        
        await db_source.async_init()
        
        t = db_source._inspector.get_indexes("act_app_appdef")
        pprint.pprint(t)

        db_source.close()
    except HTTPException as e:
        print(return_error(code=e.status_code))
    except Exception as e:
        logger.exception(e)


asyncio.run(main())

# nohup python src/test/test_mschema.py > test_mschema_$(date +%Y%m%d_%H%M%S).log 2>&1 &