#!/usr/bin/env python3
"""
测试SQL修复逻辑的优化
"""
import sys
import os
import asyncio

sys.path.append(os.getcwd().split("/src")[0])

from src.ai_chat_data.services.chat_data_service import _get_rf_user_prompt, _get_selected_sql


def test_get_rf_user_prompt():
    """测试修复提示词生成函数"""
    print("=== 测试 _get_rf_user_prompt 函数 ===")
    
    user_ask = "查询2023年的销售数据"
    
    # 测试用例1：有错误的SQL
    sql_list_with_error = [
        {
            "sql": "SELECT * FROM sales WHERE year = 2023",
            "error": "Table 'sales' doesn't exist",
            "data_frame": None,
            "result_stats": {}
        }
    ]
    
    prompt1 = _get_rf_user_prompt(sql_list_with_error, user_ask)
    print("测试用例1 - 有错误的SQL:")
    print(prompt1)
    print("\n" + "="*50 + "\n")
    
    # 测试用例2：无结果的SQL
    sql_list_no_result = [
        {
            "sql": "SELECT * FROM sales_data WHERE year = 2023",
            "error": "",
            "data_frame": [],
            "result_stats": {}
        }
    ]
    
    prompt2 = _get_rf_user_prompt(sql_list_no_result, user_ask)
    print("测试用例2 - 无结果的SQL:")
    print(prompt2)
    print("\n" + "="*50 + "\n")
    
    # 测试用例3：结果过少的SQL
    sql_list_few_results = [
        {
            "sql": "SELECT * FROM sales_data WHERE year = 2023 AND region = 'North'",
            "error": "",
            "data_frame": [{"id": 1, "amount": 1000}],
            "result_stats": {
                "id": {"count": 1, "mean": 1.0},
                "amount": {"count": 1, "mean": 1000.0}
            }
        }
    ]
    
    prompt3 = _get_rf_user_prompt(sql_list_few_results, user_ask)
    print("测试用例3 - 结果过少的SQL:")
    print(prompt3)
    print("\n" + "="*50 + "\n")
    
    # 测试用例4：正常的SQL（不需要修复）
    sql_list_normal = [
        {
            "sql": "SELECT * FROM sales_data WHERE year = 2023",
            "error": "",
            "data_frame": [
                {"id": 1, "amount": 1000},
                {"id": 2, "amount": 2000},
                {"id": 3, "amount": 1500},
                {"id": 4, "amount": 3000},
                {"id": 5, "amount": 2500}
            ],
            "result_stats": {
                "id": {"count": 5, "mean": 3.0},
                "amount": {"count": 5, "mean": 2000.0}
            }
        }
    ]
    
    prompt4 = _get_rf_user_prompt(sql_list_normal, user_ask)
    print("测试用例4 - 正常的SQL:")
    print(prompt4)
    print("\n" + "="*50 + "\n")


async def test_get_selected_sql():
    """测试SQL选择逻辑"""
    print("=== 测试 _get_selected_sql 函数 ===")
    
    # 测试用例1：多个SQL，选择最优的
    sql_list = [
        {
            "sql": "SELECT * FROM sales WHERE year = 2023",
            "error": "Table 'sales' doesn't exist",
            "data_frame": None,
            "result_stats": {}
        },
        {
            "sql": "SELECT * FROM sales_data WHERE year = 2023",
            "error": "",
            "data_frame": [],
            "result_stats": {}
        },
        {
            "sql": "SELECT * FROM sales_data WHERE year >= 2023",
            "error": "",
            "data_frame": [
                {"id": 1, "amount": 1000},
                {"id": 2, "amount": 2000}
            ],
            "result_stats": {
                "id": {"count": 2, "mean": 1.5},
                "amount": {"count": 2, "mean": 1500.0}
            }
        }
    ]
    
    # 模拟JSON响应
    raw_ans = '{"index": 1, "chart_type": "Bar"}'
    
    selected = await _get_selected_sql(raw_ans, sql_list)
    print("测试用例1 - 多个SQL选择:")
    print(f"选择的SQL: {selected['sql']}")
    print(f"是否有错误: {bool(selected['error'])}")
    print(f"结果行数: {len(selected.get('data_frame', []))}")
    print(f"图表类型: {selected['chart_type']}")
    print("\n" + "="*50 + "\n")
    
    # 测试用例2：所有SQL都有错误
    sql_list_all_error = [
        {
            "sql": "SELECT * FROM sales WHERE year = 2023",
            "error": "Table 'sales' doesn't exist",
            "data_frame": None,
            "result_stats": {}
        },
        {
            "sql": "SELECT * FROM sales_data WHERE year = 2023",
            "error": "Column 'year' not found",
            "data_frame": None,
            "result_stats": {}
        }
    ]
    
    selected2 = await _get_selected_sql(raw_ans, sql_list_all_error)
    print("测试用例2 - 所有SQL都有错误:")
    print(f"选择的SQL: {selected2['sql']}")
    print(f"是否有错误: {bool(selected2['error'])}")
    print("\n" + "="*50 + "\n")


def main():
    """主测试函数"""
    print("开始测试SQL修复逻辑优化...")
    print("="*60)
    
    # 测试提示词生成
    test_get_rf_user_prompt()
    
    # 测试SQL选择逻辑
    asyncio.run(test_get_selected_sql())
    
    print("测试完成！")


if __name__ == "__main__":
    main()
