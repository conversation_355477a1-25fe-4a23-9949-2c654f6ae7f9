import sys
import os
from trace import Trace
from loguru import logger
from fastapi import HTTPException

sys.path.append(os.getcwd().split("/src")[0])

import asyncio

from src.sql_engine.utils.db_config import DBConfig
from src.sql_engine.utils.db_init import init_db_conn
from src.sql_engine.utils.db_source import HITLSQLDatabase
from src.sql_engine.database_env import DataBaseEnv
from src.common.utils.error_codes import return_error
from src.sql_engine.utils.db_mschema import MSchema

# db = DBConfig(
#     db_type="mysql",
#     db_name="xiyan_data",
#     user_name="root",
#     db_pwd="<EMAIL>",
#     db_host="************",
#     port="30202",
# )

db = DBConfig(
    db_type="argodb",
    db_name="dwd",
    user_name="bdp",
    db_pwd="@51bsi.com",
    db_host="************",
    port="10000",
)

# excel = DBConfig(
#     db_type="excel",
#     db_path="http://************:30292/aidb-files/excel/wenwuq/%E5%8A%A0%E7%8F%AD%E8%AE%B0%E5%BD%95_2025-03-11-15-37-18-827754.xlsx",
#     db_name="加班记录.xlsx",
# )

import pprint


async def main():
    try:
        # mschema=MSchema()
        # mschema.load_from_file(file_path="/home/<USER>/project/bowen-aidata-backend/src/test/test_mschema_2.json")
        # db_source = init_db_conn(db,mschema=mschema)
        
        db_source = init_db_conn(db)        
        await db_source.async_init(get_examples=False, get_statistics=False)
        
        data = await db_source.get_top_rows("customers")
        print(data)
        # await db_source.fields_category()
        # await db_source.desc_generation()
        
        # pprint.pprint(db_source.mschema.dump())
        # print(db_source.mschema.to_mschema())
        
        
        db_source.close()
    except HTTPException as e:
        print(return_error(code=e.status_code))
    except Exception as e:
        logger.exception(e)


asyncio.run(main())

# nohup python src/test/test_mschema.py > test_mschema_$(date +%Y%m%d_%H%M%S).log 2>&1 &