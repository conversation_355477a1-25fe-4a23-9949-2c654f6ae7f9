import sys
import os

sys.path.append(os.getcwd().split("/src")[0])

import asyncio
from src.ai_chat_data.services.chat_data_service import get_connect

llm_data = {
    "data_name": "case104_cjcyjt_operation",
    "data_type": "mysql",
    "data_config": {
        "data_path": "",
        "data_host": "**************",
        "data_port": "3308",
        "data_user": "root",
        "data_pwd": "admin",
    }
}

from sqlalchemy import func, MetaData
from sqlalchemy.orm import sessionmaker
from typing import Optional

from src.common.utils.embdding import text_to_dense_vector
async def main():
    texts = ["这是一个测试文本"]

    try:
        vectors = await text_to_dense_vector(texts, 1024)
        print(vectors)
    except Exception as e:
        print(f"调用接口时出错: {e}")


asyncio.run(main())