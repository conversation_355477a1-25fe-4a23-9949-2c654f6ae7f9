import sys
import os

sys.path.append(os.getcwd().split("/src")[0])

import asyncio


async def fetch_empty(time):
    print(f"Fetching {time}...")
    await asyncio.sleep(time)  # 模拟 I/O 操作
    print(f"Finished fetching {time}")


async def fetch_data(time):
    print(f"Fetching {time}...")
    await asyncio.sleep(time)  # 模拟 I/O 操作
    print(f"Finished fetching {time}")

    return "Data"


async def main():
    # 启动任务并立即打印 "test"
    ans = asyncio.gather(
        fetch_data(3),
        fetch_empty(2),
    )

    await asyncio.sleep(1)
    print("test")

    # 等待所有任务完成
    ans = await ans
    print(ans)


asyncio.run(main())
