import sys
import os

sys.path.append(os.getcwd().split("/src")[0])

import asyncio
from src.aagent.agent.aagent import AAgent
from src.sql_engine.utils.call_llm import call_llm
from llama_index.core.prompts import PromptTemplate
from llama_index.core.prompts.prompt_type import PromptType

async def main():
    TEST_PROMPT = PromptTemplate(
        "为我介绍一下什么是{dbtype}",
        prompt_type=PromptType.CUSTOM,
    )
    
    model_config = {
        "api_key": "IF4jXMG7vKXGsGYPDoumqrxmJ8HE9BOxjElTY8uUH0YJ8JLDKlWMbaAZhmJglBvg",
        "base_url": "http://10.20.1.51:20007/v1",
        "model": "xiyan7b",
    }
    a = AAgent(model_config=model_config)
    # msg = [{"role": "user", "content": "你好"}]
    # ans = await a.run_nonstream(msg)
    ans = await call_llm(TEST_PROMPT, a, dbtype = "sqlite")
    print(ans)

asyncio.run(main())