import sys
import os
import asyncio

sys.path.append(os.getcwd().split("/src")[0])

from src.ai_chat_data.services.chat_data_service import run_sql
from src.sql_engine.m_schema.base_engine import get_connect
from src.ai_data_source.services.data_source_service import fetch_data_source_dy_id

llm_data = {
    "data_name": "Excel数据源问答(1).xlsx",
    "data_type": "excel",
    "data_config": {
        "data_path": "http://************:30292/aidb-files/excel/wenwuq/Excel%E6%95%B0%E6%8D%AE%E6%BA%90%E9%97%AE%E7%AD%94%281%29_2025-03-14-09-12-29-637427.xlsx",
        "data_host": None,
        "data_port": None,
        "data_user": None,
        "data_pwd": "",
    },
}


async def main():
    user_id = "wenwuq"

    data_conn = get_connect(
        llm_data["data_type"], llm_data["data_name"], llm_data["data_config"]
    )

    sql = "SELECT SUM(营业总收入) AS 累计营业总收入 FROM Sheet1 WHERE 年度 >= 2023"
    ans = run_sql(data_conn, sql)
    print(ans)


asyncio.run(main())
