import sys
import os
from loguru import logger

sys.path.append(os.getcwd().split("/src")[0])

import asyncio
from src.common.utils.msg_utils import *

qa_list = [
            {
                "talk_id": "67d3c8a45d21a952bc70fd24",
                "user_id": "wenwuq",
                "message": {
                    "chatcmpl-a9ef1eda61f848ec8f64af832fd8de17": {
                        "name": "USER_agent",
                        "description": "用户提问",
                        "ans": {
                            "role": "user",
                            "content": "分析营业总收入趋势"
                        },
                        "metadata": {}
                    },
                    "chatcmpl-33a8eee12b1c4a12a35f7963309cacf0": {
                        "name": "RA_agent",
                        "description": "问题改写",
                        "ans": {
                            "role": "assistant",
                            "content": "改写为: 分析并展示营业总收入的时间趋势，包括增长点和可能的驱动因素。"
                        },
                        "metadata": {}
                    },
                    "chatcmpl-ffb5f07b04a6475a8255ff00eb5e024e": {
                        "name": "FT_agent",
                        "description": "数据表选取",
                        "ans": {
                            "role": "assistant",
                            "content": "最终选择表为: Sheet1"
                        },
                        "metadata": {
                            "selected_tables": {
                                "code": 200,
                                "data": {
                                    "selected_tables": [
                                        "Sheet1"
                                    ]
                                },
                                "msg": "成功选取数据表"
                            }
                        }
                    },
                    "chatcmpl-dbf13c6f33324ad083db888f4e72a68a": {
                        "name": "AS_agent",
                        "description": "思路分析",
                        "ans": {
                            "role": "assistant",
                            "content": "# 分析并展示营业总收入的时间趋势，包括增长点和可能的驱动因素。\n# 目标表：Sheet1\n# 关键字段：年度, 营业总收入\n\n# 查询思路\n1. 确定查询的目标表为 `Sheet1`。\n2. 确定关键字段为 `年度` 和 `营业总收入`。\n3. 评估是否需要过滤条件：无。\n4. 评估是否需要分组：按年度分组。\n5. 评估是否需要聚合：计算每年的营业总收入总和。\n6. 评估是否需要排序：按年度排序。\n7. 评估是否需要限制返回结果：无。\n8. 评估是否需要计算增长点：计算每年的营业总收入增长率。\n9. 评估是否需要分析增长点的可能驱动因素：无，但可以提供增长点的年度数据以便进一步分析。"
                        },
                        "metadata": {}
                    },
                    "chatcmpl-e355a07389854313982a16482a1a4e60": {
                        "name": "GS_agent",
                        "description": "SQL 生成",
                        "ans": {
                            "role": "assistant",
                            "content": ""
                        },
                        "metadata": {
                            "struct_sql": {
                                "code": 200,
                                "data": {
                                    "struct_sql": "SELECT 年度, SUM(营业总收入) AS 营业总收入总和, LAG(SUM(营业总收入)) OVER (ORDER BY 年度) AS 上一年度营业总收入总和 FROM Sheet1 GROUP BY 年度 ORDER BY 年度;"
                                },
                                "msg": "SQL 生成成功"
                            }
                        }
                    },
                    "chatcmpl-e4a93156a6ba44c093d5e6f072a621f6": {
                        "name": "SS_agent",
                        "description": "图表选取",
                        "ans": {
                            "role": "assistant",
                            "content": ""
                        },
                        "metadata": {
                            "final_sql_result": {
                                "code": 200,
                                "data": {
                                    "final_sql": "SELECT 年度, SUM(营业总收入) AS 营业总收入总和, LAG(SUM(营业总收入)) OVER (ORDER BY 年度) AS 上一年度营业总收入总和 FROM Sheet1 GROUP BY 年度 ORDER BY 年度;",
                                    "final_result": [
                                        {
                                            "年度": 1994,
                                            "营业总收入总和": 3.176,
                                            "上一年度营业总收入总和": None
                                        },
                                        {
                                            "年度": 1995,
                                            "营业总收入总和": 4.066,
                                            "上一年度营业总收入总和": 3.176
                                        },
                                        {
                                            "年度": 1996,
                                            "营业总收入总和": 4.136,
                                            "上一年度营业总收入总和": 4.066
                                        },
                                        {
                                            "年度": 1997,
                                            "营业总收入总和": 4.872,
                                            "上一年度营业总收入总和": 4.136
                                        },
                                        {
                                            "年度": 1998,
                                            "营业总收入总和": 4.977,
                                            "上一年度营业总收入总和": 4.872
                                        },
                                        {
                                            "年度": 1999,
                                            "营业总收入总和": 5.328,
                                            "上一年度营业总收入总和": 4.977
                                        },
                                        {
                                            "年度": 2000,
                                            "营业总收入总和": 6.653,
                                            "上一年度营业总收入总和": 5.328
                                        },
                                        {
                                            "年度": 2001,
                                            "营业总收入总和": 6.249,
                                            "上一年度营业总收入总和": 6.653
                                        },
                                        {
                                            "年度": 2002,
                                            "营业总收入总和": 7.064,
                                            "上一年度营业总收入总和": 6.249
                                        },
                                        {
                                            "年度": 2003,
                                            "营业总收入总和": 9.134,
                                            "上一年度营业总收入总和": 7.064
                                        },
                                        {
                                            "年度": 2004,
                                            "营业总收入总和": 9.1,
                                            "上一年度营业总收入总和": 9.134
                                        },
                                        {
                                            "年度": 2005,
                                            "营业总收入总和": 11.23,
                                            "上一年度营业总收入总和": 9.1
                                        },
                                        {
                                            "年度": 2006,
                                            "营业总收入总和": 17.62,
                                            "上一年度营业总收入总和": 11.23
                                        },
                                        {
                                            "年度": 2007,
                                            "营业总收入总和": 18.96,
                                            "上一年度营业总收入总和": 17.62
                                        },
                                        {
                                            "年度": 2008,
                                            "营业总收入总和": 20.35,
                                            "上一年度营业总收入总和": 18.96
                                        },
                                        {
                                            "年度": 2009,
                                            "营业总收入总和": 22.07,
                                            "上一年度营业总收入总和": 20.35
                                        },
                                        {
                                            "年度": 2010,
                                            "营业总收入总和": 29.09,
                                            "上一年度营业总收入总和": 22.07
                                        },
                                        {
                                            "年度": 2011,
                                            "营业总收入总和": 33.39,
                                            "上一年度营业总收入总和": 29.09
                                        },
                                        {
                                            "年度": 2012,
                                            "营业总收入总和": 29.74,
                                            "上一年度营业总收入总和": 33.39
                                        },
                                        {
                                            "年度": 2013,
                                            "营业总收入总和": 32.4,
                                            "上一年度营业总收入总和": 29.74
                                        },
                                        {
                                            "年度": 2014,
                                            "营业总收入总和": 41.7,
                                            "上一年度营业总收入总和": 32.4
                                        },
                                        {
                                            "年度": 2015,
                                            "营业总收入总和": 50.64,
                                            "上一年度营业总收入总和": 41.7
                                        },
                                        {
                                            "年度": 2016,
                                            "营业总收入总和": 65.89,
                                            "上一年度营业总收入总和": 50.64
                                        },
                                        {
                                            "年度": 2017,
                                            "营业总收入总和": 80.18,
                                            "上一年度营业总收入总和": 65.89
                                        },
                                        {
                                            "年度": 2018,
                                            "营业总收入总和": 53.38,
                                            "上一年度营业总收入总和": 80.18
                                        },
                                        {
                                            "年度": 2019,
                                            "营业总收入总和": 36.68,
                                            "上一年度营业总收入总和": 53.38
                                        }
                                    ],
                                    "chart_type": "Trend"
                                },
                                "msg": "成功选择图表"
                            }
                        }
                    }
                },
                "create_time": "2025-03-14T14:11:49.009000",
                "update_time": "2025-03-14T14:11:55.311000",
                "parent": "",
                "children": [
                    "67d3c8d05d21a952bc70fd26"
                ],
                "qa_id": "67d3c8a55d21a952bc70fd25"
            },
            ]

import pprint
async def main():
    try:
        history = await make_history(
            talk_id="67d3c8a45d21a952bc70fd24",
            qa_list=qa_list,
            check_talk_id=False,
            exclude_agent=["USER_agent","FT_agent", "ND_agent", "FD_agent"],

        )
        pprint.pprint(history)
    except Exception as e:
        logger.exception(e)


asyncio.run(main())
