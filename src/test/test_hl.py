import sys
import os
from loguru import logger
from fastapi import HTTPException

sys.path.append(os.getcwd().split("/src")[0])

import asyncio

from src.sql_engine.utils.db_config import DBConfig
from src.sql_engine.utils.db_init import init_db_conn
from src.sql_engine.utils.db_source import HITLSQLDatabase
from src.sql_engine.database_env import DataBaseEnv
from src.common.utils.error_codes import return_error
from src.sql_engine.utils.db_mschema import MSchema

db = DBConfig(
    db_type="vastdata",
    db_name="xiyan_data",
    user_name="vbadmin",
    db_pwd="X35ea!69",
    db_host="***********",
    port="5432",
)


async def main():
    try:

        db_source = init_db_conn(db)
        await db_source.async_init()

        # mschema = db_source.mschema.dump()
        mschema = db_source.mschema.to_mschema()

        print(mschema)
        # import pprint

        # pprint.pprint(mschema)

    except Exception as e:
        logger.exception(e)


asyncio.run(main())
