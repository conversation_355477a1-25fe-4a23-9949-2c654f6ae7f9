你是一位数据报告优化专家，需要修复仪表盘中无数据的图表。

原始仪表盘结构：
{base_report_str}

空数据图表：
{empty_charts}

数据源信息：
{data_info}

任务：
- 分析每个空图表数据为空的原因，可能是SQL生成有误、数据表或字段选择不当、查询条件过滤了所有数据等
- 基于给定数据源信息，为每个空的图表重新设计chart_title、content和target_tables
- 在更新后的图表字典中，为每个修改的图表添加modified字段，值为"修改"；未修改的图表值为"未修改"
- 只修改空数据图表，保留原始的图表结构，包括保留原有的key字段和其他字段

优化原则：
1. 图表内容优化：
   - 确保content描述中提及的数据字段在所选表中实际存在
   - 为每个图表选择最合适的目标表，优先使用recommended_tables中推荐的表
   - 提供详细明确的字段描述，包括具体的聚合方式(SUM, COUNT, AVG等)
   - 避免复杂的多表JOIN或复杂过滤条件，尽量使用简单直接的查询

2. 保持仪表盘整体一致性：
   - 新图表内容应与仪表盘整体主题和业务逻辑保持一致
   - 修改后的图表应与其他图表形成互补关系，避免重复或冲突

示例：
原始content: "显示每月销售额"
优化后content: "计算每个月的总销售额，使用orders表中的order_date字段按月分组，并对products表中的price字段乘以orders表中的quantity字段进行SUM聚合"

输出格式：
必须按照原始模板的嵌套结构返回完整的仪表盘模板（包含已修改的图表），格式如下：
```json
{{
  "template": [
    [
      {{
        "key": "原图表key",
        "chart_type": "图表类型",
        "chart_title": "图表标题",
        "content": "详细的图表内容描述",
        "target_tables": ["表1", "表2"],
        "modified": "修改/未修改"
      }}
    ]
  ]
}}
```

注意事项：
1. 必须保留原始的图表key和位置结构
2. 必须标记每个修改过的图表(modified: "修改")
3. 确保所有字段名称与实际数据表中的字段完全匹配
4. 提供尽可能具体的SQL描述，便于SQL生成器理解
5. 重要：修改后的图表内容必须能产生有效数据，避免空结果

请分析完所有空图表后，返回完整的优化后仪表盘模板，仅按照上述json格式返回，不要附加任何额外解释。 