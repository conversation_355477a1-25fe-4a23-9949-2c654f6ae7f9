import os, sys

sys.path.append(os.getcwd())

import json
import copy
import asyncio
from pathlib import Path
from fastapi import HTTPException
from src.aagent.agent.aagent import AAgent
from src.aagent.utils.utils import random_uuid
from src.aagent.utils.load_prompt import load_prompt
from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.time import get_current_time_with_weekday
from src.common.utils.error_codes import return_error
from src.ai_chat_data.services.chat_data_service import (
    chat_analysis,
    chat_gen_sql,
    fetch_data_source_dy_id,
)
from src.ai_chat_data.utils.chat_data_utils import filter_tables
from src.ai_dashboard.utils.table_utils import (
    get_table_structure_by_token_limit,
    extract_specific_tables_mschema,
)

from src.ai_dashboard.utils.template_utils import clear_template_results
from src.ai_dashboard.utils.agent_utils import (
    run_agent_stream,
    extract_json_from_response,
)
from src.ai_dashboard.utils.response_utils import (
    update_stage_metadata,
    ensure_metadata_exists,
)
from src.ai_dashboard.utils.dashboard_agent_utils import (
    create_requirement_analysis_agent,
    create_template_filling_agent,
    create_correlation_validation_agent,
    process_template_filling_result,
    process_correlation_validation_result,
    create_report_optimizer_agent,
    create_chart_modifier_agent,
    create_data_confirm_agent,
)
from src.common.utils.msg_utils import create_user_resp
from src.common.utils.logger import logger

current_dir = Path(__file__).resolve().parent.parent
prompt_dir = current_dir / "prompts"
template_dir = current_dir / "template"


async def get_dashboard_template(user_id: str):
    logger.info(f"【仪表盘】get_dashboard_template 方法开始，user_id={user_id}")
    """本期只有默认模板，后期支持用户自定义模版"""
    template_list = os.listdir(template_dir)
    template_list = [
        template for template in template_list if template.endswith(".json")
    ]
    template_list = [
        json.load(open(template_dir / template, "r")) for template in template_list
    ]
    return template_list


async def select_dashboard_database(
    user_id: str,
    data_id: str,
    talk_id: str = None,
    stream: bool = True,
):
    logger.info(f"【仪表盘】select_dashboard_database 方法开始，user_id={user_id}, data_id={data_id}, talk_id={talk_id}")
    """
    选择数据库并生成数据库简要描述和可以生成的仪表盘主题建议

    Args:
        user_id (str): 用户ID
        user_ask (str): 用户问题
        data_id (str): 数据源ID
        talk_id (str, optional): 对话ID
        stream (bool, optional): 是否使用流式返回

    Returns:
        如果stream=True，返回异步迭代器用于流式传输
        如果stream=False，返回完整的结果
    """
    try:
        llm_data = {
            "resp_raw": {},
            "talk_id": talk_id,
        }

        # 获取数据库信息
        data_info_res = await fetch_data_source_dy_id(
            user_id=user_id, data_id=data_id, mschema=True
        )

        if data_info_res["code"] != 200:
            raise HTTPException(
                status_code=data_info_res["code"],
                detail=data_info_res["msg"],
            )

        data_info = data_info_res["data"]["data_info"]

        mschema = data_info.get("mschema", {})

        # 使用统一的表结构获取函数，根据token限制获取适当的表结构
        table_description = get_table_structure_by_token_limit(
            mschema,
            limit_tokens=config_data["chat_dashboard_table_schema"]["limit_tokens"],
        )
        # 加载数据库分析提示词
        db_analyzer_prompt = await load_prompt(
            path=prompt_dir, name="dashboard_db_analyzer"
        )
        db_analyzer_prompt = db_analyzer_prompt.format(
            current_time=get_current_time_with_weekday(),
            table_description=table_description,
        )

        messages = [{"role": "user", "content": "基于当前数据库，生成仪表盘主题建议"}]

        DB_agent = AAgent(
            name="DB_analyzer_agent",
            description="数据库分析与仪表盘主题推荐",
            system=db_analyzer_prompt,
            model_config=config_data["llm"],
        )

        logger.info("【仪表盘】数据库分析智能体(DB_analyzer_agent)开始")
        async for _ in run_agent_stream(
            agent=DB_agent,
            messages=messages,
            resp_raw=llm_data["resp_raw"],
            talk_id=talk_id,
        ):
            yield llm_data["resp_raw"]
        logger.info("【仪表盘】数据库分析智能体(DB_analyzer_agent)结束")

    except HTTPException as e:
        logger.exception(return_error(code=e.status_code, e=e))
        yield return_error(code=e.status_code, e=e)

    except Exception as e:
        logger.exception(e)
        yield return_error(code=500, e=e)


async def dashboard_questions(
    user_id: str,
    data_id: str = None,
    data_confirm_info: dict = None,
    template: list = None,
    template_name: str = None,
    talk_id: str = None,
    user_ask: str = None,
    stream: bool = True,
):
    logger.info(f"【仪表盘】dashboard_questions 方法开始，user_id={user_id}, data_id={data_id}, talk_id={talk_id}")
    """
    根据用户选定的仪表盘模板，进行模板填充

    此函数通过多智能体协同工作方式，实现高质量仪表盘生成:
    1. 需求分析智能体(Requirement_Analysis): 分析数据特征
    2. 模板填充智能体(Template_Filling): 根据数据特征填充模板
    3. 数据关联智能体(Correlation_Validation): 确保各图表之间有业务关联性

    Args:
        user_id (str): 用户ID
        data_id (str, optional): 数据源ID
        template (list, optional): 用户选择的仪表盘模板
        template_name (str, optional): 仪表盘模板名称
        data_confirm_info (dict, optional): 数组范围组确认的JSON结果
        talk_id (str, optional): 对话ID
        user_ask (str, optional): 用户问题作为仪表盘主题
        stream (bool, optional): 是否使用流式返回结果

    Returns:
        如果stream=True，返回异步迭代器用于流式传输
        如果stream=False，返回完整的结果
    """
    try:
        llm_data = {
            "resp_raw": await create_user_resp(
                user_ask="数据表范围组确认完毕，生成仪表盘"
            ),
            "talk_id": talk_id,
            "user_ask": user_ask,
            "current_time": get_current_time_with_weekday(),
            "template_name": template_name,
        }

        empty_template = {}
        if template:
            empty_template = copy.deepcopy(template)
            empty_template = clear_template_results(empty_template)
        llm_data["empty_template"] = empty_template

        # 获取数据源信息
        if data_id:
            data_info_res = await fetch_data_source_dy_id(
                user_id=user_id, data_id=data_id, mschema=True
            )
            if data_info_res["code"] != 200:
                raise HTTPException(
                    status_code=data_info_res["code"],
                    detail=data_info_res["msg"],
                )
            data_info = data_info_res["data"]["data_info"]
            selected_tables = [k for k in data_confirm_info.keys()]
            tables_description = extract_specific_tables_mschema(
                data_info.get("mschema", {}), selected_tables
            )
            tables_description_limit = get_table_structure_by_token_limit(
                tables_description,
                limit_tokens=config_data["chat_dashboard_table_schema"]["limit_tokens"],
            )

            llm_data["tables_description"] = tables_description
            llm_data["tables_description_limit"] = tables_description_limit

        llm_data['chart_type'] = await load_prompt(path=prompt_dir,name='chart_type')
        # 1. 需求分析阶段 - 分析数据特征
        logger.info("【仪表盘】需求分析智能体(Requirement_Analysis)开始")
        async for _ in _dashboard_requirement_analysis(llm_data=llm_data):
            yield llm_data["resp_raw"]
        logger.info("【仪表盘】需求分析智能体(Requirement_Analysis)结束")

        # 2. 模板填充阶段 - 根据分析结果填充模板
        logger.info("【仪表盘】模板填充智能体(Template_Filling)开始")
        async for _ in _dashboard_template_filling(
            user_id=user_id,
            data_id=data_id,
            llm_data=llm_data,
        ):
            yield llm_data["resp_raw"]
        logger.info("【仪表盘】模板填充智能体(Template_Filling)结束")

        # 3. 数据关联验证阶段 - 确保图表间关联性
        logger.info("【仪表盘】数据关联智能体(Correlation_Validation)开始")
        async for _ in _dashboard_correlation_validation(
            llm_data=llm_data,
        ):
            yield llm_data["resp_raw"]
        logger.info("【仪表盘】数据关联智能体(Correlation_Validation)结束")

        new_msg_id = "chatcmpl-" + random_uuid()
        resp_raw = llm_data["resp_raw"]
        resp_raw[new_msg_id] = {
            "name": "Dashboard_Builder",
            "description": "仪表盘模版构建完成",
            "ans": {
                "role": "assistant",
                "content": "持续深入分析实际数据，仪表盘元素可能会进一步优化。",
            },
        }

        yield llm_data["resp_raw"]

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"仪表盘生成错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"仪表盘生成错误: {str(e)}")


async def dashboard_to_report(
    user_id: str, data_id: str, final_template: list, stream: bool = True
):
    logger.info(f"【仪表盘】dashboard_to_report 方法开始，user_id={user_id}, data_id={data_id}")
    """
    将仪表盘转换为报告并自动优化空数据图表

    通过多智能体协同工作流程：
    1. 报告生成智能体：将仪表盘模版转换为报告
    2. 报告优化智能体：修复报告中的空数据图表

    Args:
        user_id (str): 用户ID
        data_id (str): 数据源ID
        final_template (dict): 仪表盘模板
        stream (bool): 是否使用流式返回

    Returns:
        如果stream=True，返回异步迭代器用于流式传输
        如果stream=False，返回完整的结果
    """
    try:
        llm_data = {
            "resp_raw": await create_user_resp(
                user_ask="仪表盘生成完毕，开始生成报告"
            )
        }

        # 1. 报告生成智能体：填充仪表盘模板
        base_report = await generate_dashboard(
            user_id=user_id,
            data_id=data_id,
            final_template=copy.deepcopy(final_template),
        )
        logger.info("【仪表盘】报告生成智能体(Report_Generator)开始")

        # 2. 检查是否有空数据帧的图表
        empty_charts = []
        for chart_line_index, chart_line in enumerate(base_report):
            for chart_index, chart in enumerate(chart_line):
                # 检查result中是否有数据
                result = chart.get("result", {})
                data_frame = result.get("data_frame", [])

                # 检测空数据的更完善逻辑
                if (
                    not data_frame
                    or (isinstance(data_frame, list) and len(data_frame) == 0)
                    or (
                        # 检查result_stats中的统计数据
                        result.get("result_stats")
                        and all(
                            value.get("count", 0) == 0
                            for key, value in result.get("result_stats", {}).items()
                            if isinstance(value, dict)
                        )
                    )
                ):
                    empty_charts.append(
                        {
                            "chart_title": chart.get("chart_title", ""),
                            "content": chart.get("content", ""),
                            "chart_type": chart.get("chart_type", ""),
                            "target_tables": chart.get("target_tables", []),
                            "key": chart.get("key", ""),
                            "position": [
                                chart_line_index,
                                chart_index,
                            ],  # 记录位置便于后续更新
                        }
                    )

        # 更新状态为"生成报告中"，触发前端状态更新
        new_msg_id = "chatcmpl-" + random_uuid()
        llm_data["resp_raw"][new_msg_id] = {
            "name": "Report_Generator",
            "description": "报告生成",
            "ans": {
                "role": "assistant",
                "content": "正在生成报告数据，请稍候...",
            },
            "metadata": {
                "stage": "report_generation",
                "is_success": True,
                "msg": "报告生成中",
            },
        }

        yield llm_data["resp_raw"]

        # 3. 如果有空图表，使用报告优化智能体进行优化
        if empty_charts:
            logger.info(f"【仪表盘】报告优化智能体(Report_Optimizer)开始，检测到{len(empty_charts)}个空数据图表")


            # 获取数据库结构信息
            data_info_res = await fetch_data_source_dy_id(
                user_id=user_id, data_id=data_id, mschema=True
            )
            if data_info_res["code"] != 200:
                raise HTTPException(
                    status_code=data_info_res["code"],
                    detail=data_info_res["msg"],
                )
            data_info = data_info_res["data"]["data_info"]
            full_mschema = data_info.get("mschema", {})

            # 准备为每个空图表推荐最合适的数据表
            filter_tables_list = []
            filter_tables_tasks = []
            filter_config = config_data.get("chat_data_table_filter", {})

            # 并发执行表筛选任务
            for empty_chart in empty_charts:
                content = empty_chart.get("content", "")
                if content:
                    filter_tables_tasks.append(
                        filter_tables(
                            user_id=user_id,
                            user_ask=content,
                            data_id=data_id,
                            filter_config=filter_config,
                        )
                    )

            # 等待所有表筛选任务完成
            if filter_tables_tasks:
                filter_tables_results = await asyncio.gather(*filter_tables_tasks)

                # 将每个空图表的推荐表添加到filter_tables_list
                for i, result in enumerate(filter_tables_results):
                    if result:
                        # 将推荐表添加到对应的空图表中
                        empty_charts[i]["recommended_tables"] = result
                        filter_tables_list.extend(result)

            # 去重，获取所有推荐表的唯一列表
            filter_tables_list = list(dict.fromkeys(filter_tables_list))

            # 提取这些表的结构信息
            filter_tables_info = extract_specific_tables_mschema(
                full_mschema, filter_tables_list
            )

            # 创建报告优化智能体
            optimizer_agent = await create_report_optimizer_agent(
                prompt_dir=prompt_dir,
                base_report=base_report,
                empty_charts=empty_charts,
                data_info=filter_tables_info,
            )

            messages = [
                {
                    "role": "user",
                    "content": "请分析空数据图表并提供优化建议，根据图表内容和推荐表重构chart_title、content和target_tables字段，确保能生成有效数据",
                }
            ]

            # 更新状态为"优化空图表中"，触发前端状态更新
            new_msg_id = "chatcmpl-" + random_uuid()
            llm_data["resp_raw"][new_msg_id] = {
                "name": "Report_Optimizer",
                "description": "报告优化",
                "ans": {
                    "role": "assistant",
                    "content": f"检测到{len(empty_charts)}个图表数据为空，正在进行智能优化...",
                },
                "metadata": {
                    "stage": "report_optimization",
                    "is_success": True,
                    "msg": "图表优化中",
                    "empty_charts_count": len(empty_charts),
                },
            }

            yield llm_data["resp_raw"]

            # 使用流式运行优化智能体
            logger.info(f"【仪表盘】数据重生成智能体(Chart_Regenerator)开始，为{len(empty_charts)}个图表重生成数据")
            async for _ in run_agent_stream(
                agent=optimizer_agent, messages=messages, resp_raw=llm_data["resp_raw"]
            ):
                yield llm_data["resp_raw"]
            logger.info(f"【仪表盘】数据重生成智能体(Chart_Regenerator)结束")

            # 使用最后一个消息ID
            temp_id = list(llm_data["resp_raw"].keys())[-1]
            response_content = llm_data["resp_raw"][temp_id]["ans"]["raw_content"]

            # 提取优化建议
            optimization_result, success = await extract_json_from_response(
                response_content
            )

            # 应用优化建议
            modified_charts = []
            if (
                success
                and isinstance(optimization_result, dict)
                and "template" in optimization_result
            ):
                optimized_template = optimization_result.get("template", [])

                # 检查优化结果是否有效
                if isinstance(optimized_template, list):
                    logger.info(f"【仪表盘】成功提取优化建议，准备应用修改")

                    # 更新状态为"重新生成数据中"，触发前端状态更新
                    new_msg_id = "chatcmpl-" + random_uuid()
                    llm_data["resp_raw"][new_msg_id] = {
                        "name": "Chart_Regenerator",
                        "description": "数据重生成",
                        "ans": {
                            "role": "assistant",
                            "content": f"正在为{len(empty_charts)}个优化后的图表重新生成数据...",
                        },
                        "metadata": {
                            "stage": "data_regeneration",
                            "is_success": True,
                            "msg": "数据重生成中",
                            "modified_charts_count": len(empty_charts),
                        },
                    }

                    yield llm_data["resp_raw"]

                    # 准备需要重新生成数据的图表任务
                    chart_tasks = []

                    # 遍历优化后的模板，寻找被标记为"修改"的图表
                    for chart_line in optimized_template:
                        for chart in chart_line:
                            # 检查图表是否被修改
                            if chart.get("modified", "未修改") == "修改":
                                chart_key = chart.get("key", "")
                                # 查找对应的空图表位置
                                for empty_chart in empty_charts:
                                    if empty_chart.get("key") == chart_key:
                                        position = empty_chart.get("position", [0, 0])
                                        # 更新原始图表的内容
                                        if position and len(position) == 2:
                                            # 更新图表标题和内容
                                            base_report[position[0]][position[1]][
                                                "chart_title"
                                            ] = chart.get(
                                                "chart_title",
                                                base_report[position[0]][position[1]][
                                                    "chart_title"
                                                ],
                                            )
                                            base_report[position[0]][position[1]][
                                                "content"
                                            ] = chart.get(
                                                "content",
                                                base_report[position[0]][position[1]][
                                                    "content"
                                                ],
                                            )
                                            base_report[position[0]][position[1]][
                                                "target_tables"
                                            ] = chart.get(
                                                "target_tables",
                                                base_report[position[0]][position[1]][
                                                    "target_tables"
                                                ],
                                            )

                                            # 添加到需要重新生成数据的图表列表
                                            modified_charts.append(
                                                base_report[position[0]][position[1]]
                                            )

                                            # 创建图表数据生成任务
                                            chart_tasks.append(
                                                gen_single_chart(
                                                    user_id=user_id,
                                                    data_id=data_id,
                                                    chart_type=base_report[position[0]][
                                                        position[1]
                                                    ]["chart_type"],
                                                    content=base_report[position[0]][
                                                        position[1]
                                                    ]["content"],
                                                    tables=base_report[position[0]][
                                                        position[1]
                                                    ]["target_tables"],
                                                )
                                            )
                                        break

                    # 并发执行所有图表生成任务
                    if chart_tasks:
                        try:
                            logger.info(
                                f"【仪表盘】开始为{len(chart_tasks)}个修改后的图表重新生成数据"
                            )
                            chart_results = await asyncio.gather(*chart_tasks)

                            # 将结果更新到对应的图表中
                            for i, modified_chart in enumerate(modified_charts):
                                # 找到原始图表的位置
                                for chart_line_index, chart_line in enumerate(
                                    base_report
                                ):
                                    for chart_index, chart in enumerate(chart_line):
                                        if chart.get("key") == modified_chart.get(
                                            "key"
                                        ):
                                            # 更新图表数据
                                            base_report[chart_line_index][chart_index][
                                                "result"
                                            ] = chart_results[i]
                                            logger.info(
                                                f"【仪表盘】成功更新图表 '{chart.get('chart_title')}' 的数据"
                                            )
                                            break

                            logger.info(f"【仪表盘】所有修改后的图表数据重新生成完成")
                        except Exception as e:
                            logger.exception(f"【仪表盘】重新生成图表数据时出错: {str(e)}")
            else:
                logger.warning(
                    f"【仪表盘】无法提取有效的优化建议，优化结果: {success}, 返回类型: {type(optimization_result).__name__}"
                )

        # 更新状态为"完成"，返回最终结果
        new_msg_id = "chatcmpl-" + random_uuid()
        llm_data["resp_raw"][new_msg_id] = {
            "name": "Report_Complete",
            "description": "报告生成完成",
            "ans": {
                "role": "assistant",
                "content": "报告生成完成，已成功转换仪表盘为报告。",
            },
            "metadata": {
                "stage": "completed",
                "is_success": True,
                "msg": "已完成报告生成",
                "template": base_report,
            },
        }

        yield llm_data["resp_raw"]
        logger.info("【仪表盘】报告生成智能体(Report_Generator)结束")

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"【仪表盘】报告生成错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"【仪表盘】报告生成错误: {str(e)}")


async def _dashboard_requirement_analysis(llm_data: dict):
    logger.info("【仪表盘】_dashboard_requirement_analysis 方法开始")
    talk_id = llm_data.get("talk_id", None)
    user_ask = llm_data.get("user_ask", None)
    empty_template = llm_data.get("empty_template", None)
    resp_raw = llm_data.get("resp_raw", {})
    tables_description_limit = llm_data.get("tables_description_limit", {})
    # 创建需求分析智能体
    RA_agent = await create_requirement_analysis_agent(
        prompt_dir=prompt_dir,
        template=empty_template,
        data_info=tables_description_limit,
    )

    # 构建消息
    messages = [{"role": "user", "content": f"当前仪表盘主题为：{user_ask}"}]

    # 调用智能体
    logger.info("【仪表盘】需求分析智能体(Requirement_Analysis)执行开始")
    async for _ in run_agent_stream(
        agent=RA_agent, messages=messages, resp_raw=resp_raw, talk_id=talk_id
    ):
        yield resp_raw
    logger.info("【仪表盘】需求分析智能体(Requirement_Analysis)执行结束")

    # 使用最后一个消息ID
    temp_id = list(resp_raw.keys())[-1]

    # 提取需求分析结果
    analysis_result = resp_raw[temp_id]["ans"]["content"]
    llm_data["requirement_analysis"] = analysis_result

    # 添加元数据
    update_stage_metadata(
        resp_raw, temp_id, "requirement_analysis", is_success=True, msg="需求分析完成"
    )


async def _dashboard_template_filling(user_id, data_id, llm_data: dict):
    logger.info(f"【仪表盘】_dashboard_template_filling 方法开始，user_id={user_id}, data_id={data_id}")
    resp_raw = llm_data.get("resp_raw", {})
    requirement_analysis = llm_data.get("requirement_analysis", "")
    user_ask = llm_data.get("user_ask", "")
    empty_template = llm_data.get("empty_template", {})
    chart_type=llm_data.get("chart_type",{})
    tables_description_limit = llm_data.get("tables_description_limit", {})
    # 创建模板填充智能体

    TF_agent = await create_template_filling_agent(
        user_id=user_id,
        data_id=data_id,
        prompt_dir=prompt_dir,
        requirement_analysis=requirement_analysis,
        template=empty_template,
        chart_type=chart_type,
        data_info=tables_description_limit,
        user_ask=user_ask,
    )

    # 准备消息
    messages = [
        {
            "role": "user",
            "content": "请根据需求分析和数据结构，填充仪表盘模板与报告名称。",
        }
    ]

    # 使用流式调用
    logger.info("【仪表盘】模板填充智能体(Template_Filling)执行开始")
    async for _ in run_agent_stream(
        agent=TF_agent, messages=messages, resp_raw=resp_raw
    ):
        yield resp_raw
    logger.info("【仪表盘】模板填充智能体(Template_Filling)执行结束")

    # 使用最后一个消息ID
    temp_id = list(resp_raw.keys())[-1]

    # 处理模板填充结果
    await process_template_filling_result(
        temp_id=temp_id, resp_raw=resp_raw, llm_data=llm_data
    )

    resp_raw[temp_id]["ans"][
        "content"
    ] = "根据需求分析和数据结构，我已完成仪表盘模板的填充。"


import json_repair


async def gen_single_chart(
    user_id: str,
    data_id: str,
    chart_type: str,
    content: str,
    tables: list,
):
    logger.info(f"【仪表盘】gen_single_chart 方法开始，user_id={user_id}, data_id={data_id}, chart_type={chart_type}")
    """生成单个图表的SQL结果

    Args:
        user_id (str): 用户ID
        data_id (str): 数据源ID
        content (str): 图表内容描述
        tables (list): 目标表列表

    Returns:
        dict: SQL执行结果
    """
    # 加载图表类型提示词
    chart_type_str = await load_prompt(path=prompt_dir, name="chart_type")
    # 将 str 转换为 json
    chart_type_json = json_repair.loads(chart_type_str)
    chart_type_json = chart_type_json.get(chart_type, "Table")

    # 获取图表类型的名称、描述和数据要求
    name = chart_type_json["name"]
    description = chart_type_json["description"]
    data_requirements = chart_type_json["data_requirements"]

    # 附加图表要求
    content = f"帮我按 {name} 查询 {content}\n"
    content += f"需要 {description} \n"
    content += f"要求 {data_requirements} \n"

    try:
        llm_data = {"resp_raw": {}, "candidate_sql": []}
        await chat_analysis(
            user_id=user_id,
            user_ask=content,
            data_id=data_id,
            qa_list=[],
            tables=tables,
            stream=False,
            llm_data=llm_data,
        )

        sql_return = await chat_gen_sql(
            user_id=user_id,
            user_ask=content,
            data_id=data_id,
            qa_list=[],
            tables=tables,
            return_ans=True,
            stream=False,
            llm_data=llm_data,
        )

        metadata = sql_return.get("metadata", {})
        struct_sql = metadata.get("struct_sql", {})
        final_sql_result = struct_sql.get("data", {})

        return final_sql_result
    except Exception as e:
        logger.exception(f"生成单个图表失败: {e}")
        return {}


async def generate_dashboard(
    user_id: str,
    data_id: str,
    final_template: list,
):
    logger.info(f"【仪表盘】generate_dashboard 方法开始，user_id={user_id}, data_id={data_id}")
    """生成完整仪表盘（为所有图表生成数据）

    Args:
        user_id (str): 用户ID
        data_id (str): 数据源ID
        final_template (dict): 最终模板

    Returns:
        dict: 带有数据结果的完整仪表盘模板
    """
    try:
        # 为 final_template 增加 key
        tasks = []
        for chart_line in final_template:
            for chart in chart_line:
                chart["key"] = random_uuid()
                tasks.append(
                    gen_single_chart(
                        user_id=user_id,
                        data_id=data_id,
                        chart_type=chart["chart_type"],
                        content=chart["content"],
                        tables=chart["target_tables"],
                    )
                )

        # 并行执行所有图表数据生成任务
        all_result = await asyncio.gather(*tasks)

        # 将结果放入对应的图表中
        result_index = 0
        for chart_line in final_template:
            for chart in chart_line:
                chart["result"] = all_result[result_index]
                result_index += 1

        return final_template
    except Exception as e:
        logger.exception(f"生成完整仪表盘失败: {e}")
        return final_template


async def dashboard_data_confirm(
    user_id: str,
    data_id: str,
    template: dict,
    user_ask: str,
    talk_id: str = None,
    last_table_selection: list = None,
    stream: bool = True,
):
    logger.info(f"【仪表盘】dashboard_data_confirm 方法开始，user_id={user_id}, data_id={data_id}, talk_id={talk_id}")
    """
    根据用户传入的模板与问题，挑选合适的模版数据源，输出仪表盘中每一张表起到的业务作用。

    Args:
        user_id (str): 用户ID
        data_id (str): 数据源ID
        template (dict): 仪表盘模板
        talk_id (str, optional): 对话ID
        stream (bool, optional): 是否使用流式返回

    Returns:
        如果stream=True，返回异步迭代器用于流式传输
        如果stream=False，返回完整的结果
    """
    try:
        llm_data = {
            "resp_raw": await create_user_resp(user_ask=user_ask),
            "talk_id": talk_id,
            "user_ask": user_ask,
            "last_table_selection": last_table_selection,
        }

        empty_template = {}
        if template:
            empty_template = copy.deepcopy(template)
            empty_template = clear_template_results(empty_template)
        llm_data["template"] = empty_template

        # 获取数据源信息
        data_info_res = await fetch_data_source_dy_id(
            user_id=user_id, data_id=data_id, mschema=True
        )
        if data_info_res["code"] != 200:
            raise HTTPException(
                status_code=data_info_res["code"],
                detail=data_info_res["msg"],
            )
        data_info = data_info_res["data"]["data_info"]
        full_mschema = data_info.get("mschema", {})
        tables_description = get_table_structure_by_token_limit(
            full_mschema,
            limit_tokens=config_data["chat_dashboard_table_schema"]["limit_tokens"],
        )

        # 创建数据确认智能体
        DC_agent = await create_data_confirm_agent(
            prompt_dir=prompt_dir,
            template=empty_template,
            tables_mschema=tables_description,
            last_table_selection=last_table_selection,
            user_ask=user_ask,
        )

        messages = [
            {
                "role": "user",
                "content": "请解释仪表盘中各个表的业务作用和关系。",
            }
        ]
        logger.info("【仪表盘】数据确认智能体(Data_Confirm_Agent)开始")
        async for _ in run_agent_stream(
            agent=DC_agent,
            messages=messages,
            resp_raw=llm_data["resp_raw"],
            talk_id=talk_id,
        ):
            yield llm_data["resp_raw"]
        logger.info("【仪表盘】数据确认智能体(Data_Confirm_Agent)结束")

        # 使用最后一个消息ID
        temp_id = list(llm_data["resp_raw"].keys())[-1]
        # 在流式响应结束后，尝试再次从完整响应中提取JSON
        final_response_content = llm_data["resp_raw"][temp_id]["ans"]["raw_content"]
        try:
            json_content, success = await extract_json_from_response(
                final_response_content
            )
            if success and isinstance(json_content, dict):
                # 添加或更新表格说明到metadata中
                ensure_metadata_exists(llm_data["resp_raw"], temp_id)
                # 创建包含json_content和last_table_selection的数据字典
                data_dict = {
                    "tables_info": json_content,
                    "last_table_selection": last_table_selection,
                }
                update_stage_metadata(
                    llm_data["resp_raw"],
                    temp_id,
                    "data_confirmation",
                    is_success=True,
                    data=data_dict,
                    msg="数据确认完成",
                )
        except Exception as e:
            logger.info(f"最终提取表格业务作用失败: {e}")

        yield llm_data["resp_raw"]

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"数据确认错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据确认错误: {str(e)}")


async def modify_report(
    user_id: str,
    user_ask: str,
    data_id: str,
    report: dict,
    chart_key: str,
    talk_id: str = "",
    stream: bool = True,
):
    logger.info(f"【仪表盘】modify_report 方法开始，user_id={user_id}, data_id={data_id}, chart_key={chart_key}")
    """
    基于用户反馈，对仪表盘指定key的图表进行修改，重新选择chart_type、chart_title、content、target_tables

    Args:
        user_id (str): 用户ID
        user_ask (str): 用户反馈内容
        data_id (str): 数据源ID
        report (dict): 当前报告
        chart_key (str): 需要修改的图表key
        talk_id (str, optional): 对话ID，用于多轮对话。Defaults to "".
        stream (bool, optional): 是否使用流式返回. Defaults to True.

    Returns:
        如果stream=True，返回异步迭代器用于流式传输
        如果stream=False，返回修改后的仪表盘
    """
    try:
        llm_data = {
            "resp_raw": await create_user_resp(user_ask=user_ask),
            "talk_id": talk_id,
        }

        # 找到需要修改的图表
        target_chart = None
        chart_position = [0, 0]

        for chart_line_index, chart_line in enumerate(report):
            for chart_index, chart in enumerate(chart_line):
                if chart.get("key", "") == chart_key:
                    target_chart = chart
                    chart_position = [chart_line_index, chart_index]
                    break
            if target_chart:
                break

        if not target_chart:
            raise HTTPException(status_code=404, detail=f"未找到key为{chart_key}的图表")

        # 获取数据库结构信息
        data_info_res = await fetch_data_source_dy_id(
            user_id=user_id, data_id=data_id, mschema=True
        )
        if data_info_res["code"] != 200:
            raise HTTPException(
                status_code=data_info_res["code"], detail=data_info_res["msg"]
            )
        data_info = data_info_res["data"]["data_info"]
        full_mschema = data_info.get("mschema", {})

        # 准备状态更新消息
        new_msg_id = "chatcmpl-" + random_uuid()
        llm_data["resp_raw"][new_msg_id] = {
            "name": "Chart_Modifier",
            "description": "图表修改",
            "ans": {
                "role": "assistant",
                "content": "正在分析用户反馈并修改图表...",
            },
            "metadata": {
                "stage": "chart_modification",
                "is_success": True,
                "msg": "图表修改中",
                "chart_key": chart_key,
            },
        }

        yield llm_data["resp_raw"]

        # 创建图表修改智能体
        modifier_agent = await create_chart_modifier_agent(
            prompt_dir=prompt_dir,
            report=report,
            target_chart=target_chart,
            data_id=data_id,
            user_id=user_id,
            full_mschema=full_mschema,
        )

        messages = [{"role": "user", "content": user_ask}]

        # 使用流式调用
        logger.info("【仪表盘】图表修改智能体(Chart_Modifier)开始")
        async for _ in run_agent_stream(
            agent=modifier_agent,
            messages=messages,
            resp_raw=llm_data["resp_raw"],
            talk_id=talk_id,
        ):
            yield llm_data["resp_raw"]
        logger.info("【仪表盘】图表修改智能体(Chart_Modifier)结束")

        # 获取修改建议
        temp_id = list(llm_data["resp_raw"].keys())[-1]
        response_content = llm_data["resp_raw"][temp_id]["ans"]["raw_content"]

        # 提取修改建议
        modification, success = await extract_json_from_response(response_content)

        if not success:
            # 更新失败状态
            new_msg_id = "chatcmpl-" + random_uuid()
            llm_data["resp_raw"][new_msg_id] = {
                "name": "Chart_Modifier",
                "description": "图表修改",
                "ans": {
                    "role": "assistant",
                    "content": "无法提取有效的图表修改建议，请重新尝试。",
                },
                "metadata": {
                    "stage": "chart_modification",
                    "is_success": False,
                    "msg": "修改失败",
                    "chart_key": chart_key,
                },
            }

            yield llm_data["resp_raw"]
            raise HTTPException(status_code=500, detail="无法提取有效的图表修改建议")

        # 更新状态为"正在应用修改"
        new_msg_id = "chatcmpl-" + random_uuid()
        llm_data["resp_raw"][new_msg_id] = {
            "name": "Chart_Modifier",
            "description": "图表修改",
            "ans": {
                "role": "assistant",
                "content": "正在应用修改并重新生成图表数据...",
            },
            "metadata": {
                "stage": "chart_modification",
                "is_success": True,
                "msg": "应用修改中",
                "chart_key": chart_key,
            },
        }

        yield llm_data["resp_raw"]

        # 创建修改记录
        chart_changes = {
            "chart_key": chart_key,
            "position": chart_position,
            "changes": {},
        }

        # 直接修改report中的图表
        if "new_chart_title" in modification:
            report[chart_position[0]][chart_position[1]]["chart_title"] = modification[
                "new_chart_title"
            ]
            chart_changes["changes"]["chart_title"] = modification["new_chart_title"]

        if "new_content" in modification:
            report[chart_position[0]][chart_position[1]]["content"] = modification[
                "new_content"
            ]
            chart_changes["changes"]["content"] = modification["new_content"]

        if "new_target_tables" in modification:
            report[chart_position[0]][chart_position[1]]["target_tables"] = (
                modification["new_target_tables"]
            )
            chart_changes["changes"]["target_tables"] = modification[
                "new_target_tables"
            ]

        if "new_chart_type" in modification:
            report[chart_position[0]][chart_position[1]]["chart_type"] = modification[
                "new_chart_type"
            ]
            chart_changes["changes"]["chart_type"] = modification["new_chart_type"]

        # 为修改后的图表重新生成数据
        try:
            modified_chart_data = await gen_single_chart(
                user_id=user_id,
                data_id=data_id,
                chart_type=report[chart_position[0]][chart_position[1]]["chart_type"],
                content=report[chart_position[0]][chart_position[1]]["content"],
                tables=report[chart_position[0]][chart_position[1]]["target_tables"],
            )

            # 更新图表数据 - 直接在report中更新
            report[chart_position[0]][chart_position[1]]["result"] = modified_chart_data
            chart_changes["changes"]["result"] = "已更新"

            logger.info(
                f"【仪表盘】成功更新图表 '{report[chart_position[0]][chart_position[1]].get('chart_title')}' 的数据"
            )
        except Exception as e:
            logger.error(f"生成图表数据失败: {e}")
            # 即使数据生成失败，也保留其他修改
            chart_changes["changes"]["result"] = "生成失败"

        # 更新状态为"完成"，返回最终结果
        new_msg_id = "chatcmpl-" + random_uuid()
        llm_data["resp_raw"][new_msg_id] = {
            "name": "Chart_Modifier",
            "description": "图表修改完成",
            "ans": {
                "role": "assistant",
                "content": "图表修改完成，已成功应用修改。",
            },
            "metadata": {
                "stage": "completed",
                "is_success": True,
                "msg": "修改完成",
                "chart_key": chart_key,
                "report": report,  # 只返回完整报告
            },
        }

        yield llm_data["resp_raw"]
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"【仪表盘】修改图表失败: {e}")
        raise HTTPException(status_code=500, detail=f"【仪表盘】修改图表失败: {str(e)}")


async def _dashboard_correlation_validation(llm_data: dict):
    logger.info("【仪表盘】_dashboard_correlation_validation 方法开始")
    resp_raw = llm_data["resp_raw"]
    filled_template = llm_data.get("filled_template", {})
    data_info = llm_data.get("tables_description_limit", {})
    user_ask = llm_data.get("user_ask", "")

    # 创建关联验证智能体
    CV_agent = await create_correlation_validation_agent(
        prompt_dir=prompt_dir,
        template=filled_template,
        data_info=data_info,
        user_ask=user_ask,
    )

    # 准备消息
    messages = [
        {
            "role": "user",
            "content": "请验证仪表盘中各图表之间的关联性。",
        }
    ]

    # 调用智能体
    logger.info("【仪表盘】数据关联智能体(Correlation_Validation)执行开始")
    async for _ in run_agent_stream(
        agent=CV_agent, messages=messages, resp_raw=resp_raw
    ):
        yield resp_raw
    logger.info("【仪表盘】数据关联智能体(Correlation_Validation)执行结束")

    # 使用最后一个消息ID
    temp_id = list(resp_raw.keys())[-1]

    # 处理关联验证结果
    await process_correlation_validation_result(
        temp_id=temp_id, resp_raw=resp_raw, llm_data=llm_data
    )
