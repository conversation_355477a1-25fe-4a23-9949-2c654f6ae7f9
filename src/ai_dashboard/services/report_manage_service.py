import os
import sys
import asyncio

from pathlib import Path
from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.lifespan.db_client import db_client
from src.common.utils.error_codes import return_error

from datetime import datetime
from fastapi import HTTPException
from pymongo.errors import PyMongoError
from src.common.utils.object_id import convert_to_object_id
from src.sql_engine.utils.type_engine import TypeEngine


async def create_report(
    user_id: str,
    data_id: str,
    report_name: str,
    template: list,
):
    try:

        # 组装数据以便插入MongoDB
        now_time = datetime.now()
        report_info = {
            "user_id": user_id,
            "data_id": data_id,
            "report_name": report_name,
            "template": template,
            "status": 0,
            "create_time": now_time,
            "update_time": now_time,
        }

        # 将 report 保存到 MongoDB
        try:
            result = await db_client["mongo_col"].report.insert_one(report_info)
            report_id = str(result.inserted_id)
        except PyMongoError as e:
            return return_error(code=2100, e=e)
        if not report_id:
            return return_error(code=2100)

        logger.info(
            f"{user_id:<15} | create Mongo data: {report_id}"
        )

        return {
            "code": 200,
            "data": {
                "report_id": report_id,
            },
            "msg": "成功创建报表",
        }
    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def delete_report(
    user_id: str,
    report_ids: list,
):
    try:
        object_ids = [convert_to_object_id(report_id) for report_id in report_ids]
        now_time = datetime.now()

        # 批量删除操作
        try:
            result = await db_client["mongo_col"].report.update_many(
                {"user_id": user_id, "_id": {"$in": object_ids}},
                {
                    "$set": {
                        "status": 1,
                        "update_time": now_time,
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2300, e=e)
        if not result.matched_count > 0:
            return return_error(code=2301)
        if not result.modified_count > 0:
            return return_error(code=2300)

        logger.info(
            f"{user_id:<15} | delete Mongo data: {report_ids}"
        )

        return {
            "code": 200,
            "data": {
                "report_ids": report_ids,
            },
            "msg": "成功删除报表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_report_list(
    user_id: str,
    key_word: str,
    page_size: int,
    page_number: int,
    enable_page: bool,
):
    try:
        condition = {
            "user_id": user_id,
            "status": 0,
        }

        if key_word:
            condition["report_name"] = {"$regex": key_word, "$options": "i"}

        try:
            total_number = await db_client["mongo_col"].report.count_documents(
                condition
            )

            # 构建查询
            query = (
                db_client["mongo_col"]
                .report.find(condition, {"template": 0})
                .sort({"update_time": -1})
            )

            # 分页
            if enable_page:
                skip_count = (page_number - 1) * page_size
                query = query.skip(skip_count).limit(page_size)

            # 获取数据
            report_ids = await query.to_list(length=None)
        except PyMongoError as e:
            return return_error(code=2400, e=e)

        for item in report_ids:
            item["report_id"] = str(item.pop("_id"))
            item["create_time"] = item.get("create_time", datetime.now()).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            item["update_time"] = item.get("update_time", datetime.now()).strftime(
                "%Y-%m-%d %H:%M:%S"
            )

        return {
            "code": 200,
            "data": {
                "report_list": report_ids,
                "total_number": total_number,
            },
            "msg": "成功获取报表列表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_report_dy_id(
    user_id: str,
    report_id: str,
):
    try:
        object_id = convert_to_object_id(report_id)

        # 查询 MongoDB
        try:
            report_info = await db_client["mongo_col"].report.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not report_info:
            return return_error(code=2401)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {report_id}"
        )

        report_info["report_id"] = str(report_info.pop("_id"))
        report_info["create_time"] = report_info.get(
            "create_time", datetime.now()
        ).strftime("%Y-%m-%d %H:%M:%S")
        report_info["update_time"] = report_info.get(
            "update_time", datetime.now()
        ).strftime("%Y-%m-%d %H:%M:%S")

        return {
            "code": 200,
            "data": {"report_info": report_info},
            "msg": "成功查询报表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def fetch_report_tip_dy_id(
    user_id: str,
    report_id: str,
):
    try:
        object_id = convert_to_object_id(report_id)
        type_engine = TypeEngine()
        # 查询 MongoDB
        try:
            report_info = await db_client["mongo_col"].report.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not report_info:
            return return_error(code=2401)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {report_id}"
        )

        data_id = report_info.get("data_id", "")
        data_info_res = await fetch_data_source_dy_id(
            user_id=user_id, data_id=data_id, mschema=True
        )
        if data_info_res["code"] != 200:
            raise HTTPException(
                status_code=data_info_res["code"],
                detail=data_info_res["msg"],
            )
        data_info = data_info_res["data"]["data_info"]
        data_mschema = data_info.get("mschema", {})

        report_tip = {}
        report_template = report_info.get("template", [])
        for line in report_template:
            for chart in line:
                chart_title = chart.get("chart_title", "")
                chart_tables = chart.get("target_tables", [])
                report_tip[chart_title] = {
                    "dimension": [],
                    "measure": [],
                }

                for table in chart_tables:
                    data_mschema_table = data_mschema["tables"].get(table, {})
                    for column_name, column_info in data_mschema_table.get(
                        "fields", {}
                    ).items():
                        rename = column_info.get("rename", "")
                        dim_or_meas = column_info.get("dim_or_meas", "")
                        
                        if dim_or_meas == type_engine.dimension_label:
                            report_tip[chart_title]["dimension"].append(
                                {
                                    "name": column_name,
                                    "rename": rename,
                                }
                            )
                        elif dim_or_meas == type_engine.measure_label:
                            report_tip[chart_title]["measure"].append(
                                {
                                    "name": column_name,
                                    "rename": rename,
                                }
                            )

        return {
            "code": 200,
            "data": {"report_tip": report_tip},
            "msg": "成功查询报表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.ai_chat_data.services.chat_data_service import _run_sql
from src.ai_data_source.services.data_source_service import fetch_data_source_dy_id
from src.sql_engine.utils.db_mschema import MSchema
from src.sql_engine.utils.db_config import DBConfig
from src.sql_engine.utils.db_init import init_db_conn


async def flush_report_dy_id(
    user_id: str,
    report_id: str,
):
    try:
        object_id = convert_to_object_id(report_id)

        # 查询 MongoDB
        try:
            report_info = await db_client["mongo_col"].report.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not report_info:
            return return_error(code=2401)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {report_id}"
        )

        template = report_info["template"]
        data_id = report_info["data_id"]

        # 获取数据库连接
        data_info_res = await fetch_data_source_dy_id(
            user_id=user_id, data_id=data_id, mschema=True
        )
        if data_info_res["code"] != 200:
            raise HTTPException(
                status_code=data_info_res["code"],
                detail=data_info_res["msg"],
            )
        data_info = data_info_res["data"]["data_info"]

        data_config = data_info.get("data_config", {})
        include_tables = data_info.get("include_tables", [])

        mschema_dict = data_info.get("mschema", {})
        mschema = MSchema()
        mschema.load_from_dict(mschema_dict)

        # 连接 data_conn
        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )
        data_conn = init_db_conn(
            db_config, include_tables=include_tables, mschema=mschema
        )

        tasks = []
        for chart_line in template:
            for chart in chart_line:
                tasks.append(_run_sql(data_conn, chart["result"]["sql"]))

        all_result = await asyncio.gather(*tasks)

        # 将结果放入 chart 中
        result_index = 0
        for chart_line in template:
            for chart in chart_line:
                chart["result"] = all_result[result_index]
                result_index += 1

        # 保存到 MongoDB
        try:
            result = await db_client["mongo_col"].report.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {
                    "$set": {
                        "template": template,
                        "update_time": datetime.now(),
                    }
                },
                upsert=False,
            )
        except PyMongoError as e:
            logger.error(return_error(code=2200, e=e))
        if not result.matched_count > 0:
            logger.error(return_error(code=2201))
        if not result.modified_count > 0:
            logger.error(return_error(code=2202))

        report_info["report_id"] = str(report_info.pop("_id"))
        report_info["create_time"] = report_info.get(
            "create_time", datetime.now()
        ).strftime("%Y-%m-%d %H:%M:%S")
        report_info["update_time"] = report_info.get(
            "update_time", datetime.now()
        ).strftime("%Y-%m-%d %H:%M:%S")

        return {
            "code": 200,
            "data": {"report_info": report_info},
            "msg": "成功刷新报表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


async def update_report_dy_id(
    user_id: str,
    report_id: str,
    report_name: str | None,
    template: list | None,
):
    try:
        object_id = convert_to_object_id(report_id)

        report_info = {
            "report_name": report_name,
            "template": template,
            "update_time": datetime.now(),
        }
        report_info = {
            key: value for key, value in report_info.items() if value is not None
        }

        # 查询 MongoDB
        try:
            result = await db_client["mongo_col"].report.update_one(
                {"user_id": user_id, "_id": object_id, "status": 0},
                {"$set": report_info},
                upsert=False,
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not result.matched_count > 0:
            return return_error(code=2201)
        if not result.modified_count > 0:
            return return_error(code=2202)

        logger.info(
            f"{user_id:<15} | update MongoDB data: {report_id}"
        )

        return {
            "code": 200,
            "data": {"report_id": report_id},
            "msg": "成功更新报表",
        }

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        return return_error(code=e.status_code, e=e)
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发生未知错误: {str(e)}")


from src.aagent.utils.load_prompt import load_prompt
from src.common.utils.msg_utils import create_user_resp
from src.common.utils.time import get_current_time_with_weekday

from src.ai_chat_data.services.chat_data_service import chat_data
from src.ai_chat_data.services.chat_data_service import _str_user_info
from src.ai_chat_data.services.chat_data_service import _str_data_info


current_dir = Path(__file__).resolve().parent.parent
prompt_dir = current_dir / "prompts"


async def chat_report(
    user_id: str,
    user_ask: str,
    talk_id: str,
    report_id: str,
    qa_list: list,
    stream: bool = True,
):
    try:

        resp_raw = await create_user_resp(user_ask=user_ask)
        llm_data = {"resp_raw": resp_raw, "candidate_sql": []}

        # 查询 MongoDB
        object_id = convert_to_object_id(report_id)
        try:
            report_info = await db_client["mongo_col"].report.find_one(
                {"user_id": user_id, "_id": object_id, "status": 0}
            )
        except PyMongoError as e:
            return return_error(code=2400, e=e)
        if not report_info:
            return return_error(code=2401)

        logger.info(
            f"{user_id:<15} | fetch MongoDB data: {report_id}"
        )

        data_id = report_info.get("data_id")
        data_info_res = await fetch_data_source_dy_id(
            user_id=user_id, data_id=data_id, mschema=True
        )
        if data_info_res["code"] != 200:
            raise HTTPException(
                status_code=data_info_res["code"],
                detail=data_info_res["msg"],
            )
        data_info = data_info_res["data"]["data_info"]
        db_info_str = _str_data_info(data_info)

        current_time = get_current_time_with_weekday()
        user_info = await _str_user_info(user_id)
        template = _str_template_info(report_info["template"])

        chat_prompt = await load_prompt(path=prompt_dir, name="chat_ask")
        chat_prompt = chat_prompt.format(
            current_time=current_time,
            user_info=user_info,
            db_info=db_info_str,
            template=template,
        )

        return await chat_data(
            user_id=user_id,
            data_ids=[data_id],
            dir_ids=[],
            user_ask=user_ask,
            talk_id=talk_id,
            qa_list=qa_list,
            stream=stream,
        )
    except Exception as e:
        logger.error(return_error(code=e.status_code, e=e))
    finally:
        if data_conn := llm_data.get("data_conn", None):
            data_conn.close()


def _str_template_info(template: dict):
    """去除模板中的复杂信息，减少token数量

    Args:
        template: 输入
    """

    for line in template:
        for chart in line:
            chart.pop("key", None)
            # chart.pop("chart_type", None)
            # chart.pop("target_tables", None)
            result = chart.pop("result", None)
            result.pop("data_frame", None)
            chart["result"] = result

    return template
