import json
from typing import Dict, List, Any, Optional
from pathlib import Path

from src.aagent.agent.aagent import AAgent
from src.aagent.utils.load_prompt import load_prompt
from src.common.utils.logger import logger
from src.common.utils.time import get_current_time_with_weekday
from src.common.utils.config import config_data
from src.ai_dashboard.utils.template_utils import validate_template_structure
from src.ai_dashboard.utils.agent_utils import extract_json_from_response
from src.ai_dashboard.utils.response_utils import (
    update_dashboard_metadata, 
    update_stage_metadata, 
    ensure_metadata_exists
)
from src.ai_dashboard.utils.template_utils import clear_template_results
from src.ai_chat_data.utils.chat_data_utils import filter_tables
from src.ai_dashboard.utils.table_utils import extract_specific_tables_mschema

async def create_requirement_analysis_agent(
    prompt_dir: Path,
    template: list = None,
    data_info: dict = None,
) -> AAgent:
    """创建需求分析智能体
    
    Args:
        prompt_dir (Path): 提示词目录
        template (dict, optional): 仪表盘模板
        data_info (dict, optional): 数据库信息
        
    Returns:
        AAgent: 需求分析智能体
    """
    # 加载需求分析提示词
    ra_prompt = await load_prompt(path=prompt_dir, name="dashboard_requirement_analyzer")
        
    # 格式化提示词参数
    template_str = json.dumps(template, ensure_ascii=False) if template else "无"
    data_info_str = data_info if data_info else "无"
    
    
    # 格式化提示词
    ra_sys_prompt = ra_prompt.format(
        current_time=get_current_time_with_weekday(),
        template=template_str,
        data_info=data_info_str,
    )
    
    # 创建需求分析智能体
    RA_agent = AAgent(
        name="Requirement_Analysis",
        description="需求分析",
        system=ra_sys_prompt,
        model_config=config_data["llm"],
    )
    
    return RA_agent

from src.ai_chat_data.services.chat_data_service import _get_few_shot_str

async def create_template_filling_agent(
    user_id,
    data_id,
    prompt_dir,
    requirement_analysis: str,
    chart_type: dict,
    template: list,
    data_info: dict,
    user_ask: str
) -> AAgent:
    """创建模板填充智能体
    
    Args:
        prompt_dir: 提示词目录
        requirement_analysis (str): 需求分析结果
        chart_type(dict):图表说明
        template (list): 仪表盘模板
        data_info (dict): 数据源信息
        user_ask (str): 用户原始问题
    Returns:
        AAgent: 模板填充智能体
    """
    # 加载模板填充提示词
    example = await _get_few_shot_str(user_id, data_id, user_ask, example_type="template") 
    
    template_filling_prompt = await load_prompt(path=prompt_dir, name="dashboard_template_filling")
    template_filling_prompt = template_filling_prompt.format(
        requirement_analysis=requirement_analysis,
        template=json.dumps(template, ensure_ascii=False),
        chart_type=chart_type,
        data_info=data_info,
        example=example,
        user_ask=user_ask,
    )
    
    # 创建模板填充智能体
    return AAgent(
        name="Template_Filling",
        description="仪表盘模板填充",
        system=template_filling_prompt,
        model_config=config_data["llm"],
    )

async def create_correlation_validation_agent(
    prompt_dir,
    template: list,
    data_info: Optional[Dict] = None,
    user_ask: str = None
) -> AAgent:
    """创建数据关联验证智能体
    
    Args:
        prompt_dir: 提示词目录
        template (Dict): 仪表盘模板
        data_info (Dict, optional): 数据源信息
        user_ask (str, optional): 用户原始问题
    Returns:
        AAgent: 数据关联验证智能体
    """
    # 加载合并后的关联验证提示词
    correlation_combined_prompt = await load_prompt(path=prompt_dir, name="dashboard_correlation_combined")
    
    correlation_combined_prompt = correlation_combined_prompt.format(
        template=json.dumps(template, ensure_ascii=False),
        data_info=data_info,
        user_ask=user_ask
    )
    
    # 创建关联验证智能体
    return AAgent(
        name="Correlation_Validation",
        description="仪表盘关联验证",
        system=correlation_combined_prompt,
        model_config=config_data["llm"],
    )

async def process_template_filling_result(
    temp_id: str, 
    resp_raw: Dict[str, Any], 
    llm_data: Dict[str, Any]
) -> None:
    """处理模板填充结果
    
    Args:
        temp_id (str): 消息ID
        resp_raw (Dict[str, Any]): 响应字典
        llm_data (Dict[str, Any]): LLM数据字典
    """
    empty_template = llm_data.get("empty_template", [])
    # 从回答中提取JSON
    filled_template_json = resp_raw[temp_id]["ans"]["raw_content"]
    try:
        filled_template_json, success = await extract_json_from_response(filled_template_json)
        if success:
            filled_template = filled_template_json.get("template", [])
            report_name = filled_template_json.get("report_name", "")
        else:
            filled_template = empty_template
            report_name = ""
        logger.info(f"填充成功，仪表盘模版名称为：{report_name}")
    except Exception as e:
        logger.exception(f"模板填充结果解析失败: {e}")
        filled_template = empty_template
    
    # 保存填充的模板
    llm_data["filled_template"] = filled_template
    llm_data["report_name"] = report_name
    # 确保metadata字段存在
    ensure_metadata_exists(resp_raw, temp_id)
    
    # 添加元数据以保持一致性
    update_stage_metadata(
        resp_raw, 
        temp_id, 
        "template_filling", 
        is_success=True, 
        data={"template": filled_template, "report_name": report_name}, 
        msg="模板填充完成"
    )
    
    # 在metadata中也添加仪表盘模板（作为主要输出）
    update_dashboard_metadata(
        resp_raw, 
        temp_id, 
        filled_template, 
        is_updated=True, 
        msg="仪表盘模板已更新",
        extra_data={"report_name": report_name}
    )

async def create_report_optimizer_agent(
    prompt_dir,
    base_report: list,
    empty_charts: List[list],
    data_info: dict
) -> AAgent:
    """创建报告优化智能体
    
    Args:
        prompt_dir: 提示词目录
        base_report (list): 填充的仪表盘模板
        empty_charts (List[Dict]): 空数据图表列表，包含各图表推荐表信息及mschema_info
    Returns:
        AAgent: 报告优化智能体
    """
    # 加载报告优化提示词
    report_optimizer_prompt = await load_prompt(path=prompt_dir, name="report_optimizer")
    base_report_str = clear_template_results(base_report)
    base_report_str = json.dumps(base_report_str, ensure_ascii=False, indent=2)
    empty_charts_str = json.dumps(empty_charts, ensure_ascii=False, indent=2)
    
    # 格式化提示词
    report_optimizer_prompt = report_optimizer_prompt.format(
        base_report_str=base_report_str,
        empty_charts=empty_charts_str,
        data_info=data_info
    )
    
    # 创建报告优化智能体
    return AAgent(
        name="Report_Optimizer",
        description="报告优化专家",
        system=report_optimizer_prompt,
        model_config=config_data["llm"],
    )

async def create_chart_modifier_agent(
    prompt_dir,
    report: Dict,
    target_chart: Dict,
    data_id: str,
    user_id: str,
    full_mschema: Dict = None
) -> AAgent:
    """创建图表修改智能体
    
    Args:
        prompt_dir: 提示词目录
        report (Dict): 完整仪表盘模板
        target_chart (Dict): 目标图表
        data_id (str): 数据源ID
        user_id (str): 用户ID
        full_mschema (Dict, optional): 完整的数据表结构信息
        
    Returns:
        AAgent: 图表修改智能体
    """
    # 获取目标图表的内容
    content = target_chart.get("content", "")
    
    # 为图表选择最优表
    chart_mschema = {"tables": {}}
    
    if content and data_id and user_id:
        # 获取配置信息
        filter_config = config_data.get("chat_data_table_filter", {})
        
        # 调用filter_tables获取最优表
        selected_tables = await filter_tables(
            user_id=user_id,
            user_ask=content,
            data_id=data_id,
            filter_config=filter_config
        )
        
        # 为图表添加对应推荐表的mschema详细信息
        if full_mschema and selected_tables:
            # 从完整的mschema中提取指定表的信息
            chart_mschema = extract_specific_tables_mschema(full_mschema, selected_tables)
        else:
            # 如果该图表没有推荐表或推荐表不在mschema中，则使用默认表
            logger.info(f"图表 {target_chart.get('key', '')} 没有可用的推荐表或推荐表不在mschema中")
            if full_mschema:
                # 选择一些可能与内容相关的默认表
                default_tables = list(full_mschema.get("tables", {}).keys())[:3]  # 最多取前3个表作为默认
                chart_mschema = extract_specific_tables_mschema(full_mschema, default_tables)
    
    # 加载图表类型提示词
    chart_type_prompt = await load_prompt(path=prompt_dir, name="chart_type")
    
    # 加载图表修改提示词
    modify_chart_prompt = await load_prompt(path=prompt_dir, name="chart_modifier")
    
    # 准备参数
    dashboard_structure = json.dumps(report, ensure_ascii=False, indent=2)
    target_chart_str = json.dumps(target_chart, ensure_ascii=False, indent=2)
    recommended_tables_str = json.dumps(chart_mschema, ensure_ascii=False, indent=2)
    
    # 格式化提示词
    modify_chart_prompt = modify_chart_prompt.format(
        dashboard_structure=dashboard_structure,
        target_chart=target_chart_str,
        recommended_tables=recommended_tables_str,
        chart_type_info=chart_type_prompt
    )
    
    # 创建图表修改智能体
    return AAgent(
        name="Chart_Modifier",
        description="图表修改专家",
        system=modify_chart_prompt,
        model_config=config_data["llm"],
    )

async def create_data_confirm_agent(
    prompt_dir,
    template: Dict,
    tables_mschema: Dict,
    last_table_selection: List = None,
    user_ask: str = None
) -> AAgent:
    """创建数据确认智能体
    
    Args:
        prompt_dir (Path): 提示词目录路径
        template (Dict): 仪表盘模板
        tables_mschema (Dict): 所有相关表的mschema信息
        last_table_selection (List): 上次选择的表列表
        user_ask (str): 用户原始问题
    Returns:
        AAgent: 数据确认智能体
    """
    # 加载数据确认提示词
    data_confirm_prompt = await load_prompt(path=prompt_dir, name="dashboard_data_confirm")
    
    # 获取当前时间
    current_time = get_current_time_with_weekday()
    
    if len(last_table_selection) > 0:
        last_table_selection_json = json.dumps(last_table_selection, ensure_ascii=False, indent=2)
    else:
        last_table_selection_json = ""

    data_confirm_prompt = data_confirm_prompt.format(
        current_time=current_time,
        template_json=json.dumps(template, ensure_ascii=False, indent=2),
        tables_mschema_json=json.dumps(tables_mschema, ensure_ascii=False, indent=2),
        last_table_selection_json=last_table_selection_json,
        user_ask=user_ask
    )
    
    # 创建数据确认智能体
    DC_agent = AAgent(
        name="Data_Confirmation_Agent",
        description="仪表盘数据确认智能体，分析各表的业务作用",
        system=data_confirm_prompt,
        model_config=config_data["llm"],
    )
    
    return DC_agent

async def process_correlation_validation_result(
    temp_id: str, 
    resp_raw: Dict[str, Any], 
    llm_data: Dict[str, Any]
) -> None:
    """处理关联验证结果
    
    Args:
        temp_id (str): 消息ID
        resp_raw (Dict[str, Any]): 响应字典
        llm_data (Dict[str, Any]): LLM数据字典
    """
    filled_template = llm_data.get("filled_template", [])
    report_name = llm_data.get("report_name", "")
    template_name = llm_data.get("template_name", "")
    # 提取验证结果
    validation_result = resp_raw[temp_id]["ans"]["raw_content"]
    llm_data["correlation_validation"] = validation_result
    
    # 确保metadata字段存在
    ensure_metadata_exists(resp_raw, temp_id)
    
    try:
        # 尝试提取JSON部分
        validation_json,success = await extract_json_from_response(validation_result)
 
        if success:
            optimized_template = validation_json
            optimized_template = optimized_template.get("template", [])
            # 验证优化的模板是否保持了原结构，并且只修改了chart_title和content
            if validate_template_structure(filled_template, optimized_template):
                # 更新填充的模板
                llm_data["filled_template"] = optimized_template
                
                # 更新元数据
                update_stage_metadata(
                    resp_raw, 
                    temp_id, 
                    "is_optimized", 
                    is_success=True, 
                    data=True, 
                    msg="关联验证完成"
                )
                
                # 更新仪表盘元数据
                update_dashboard_metadata(
                    resp_raw, 
                    temp_id, 
                    optimized_template, 
                    is_updated=True, 
                    msg="仪表盘模板已优化",
                    extra_data={"report_name": report_name,"template_name": template_name}
                )
            else:
                # 结构不符合要求，保留原来的模板
                update_stage_metadata(
                    resp_raw, 
                    temp_id, 
                    "is_optimized", 
                    is_success=True, 
                    data=False, 
                    msg="优化结果不符合结构要求，保留原模板"
                )
                
                # 保持原仪表盘元数据
                update_dashboard_metadata(
                    resp_raw, 
                    temp_id, 
                    filled_template, 
                    is_updated=False,
                    extra_data={"report_name": report_name,"template_name": template_name}
                )
        else:
            # 解析失败，保留原来的模板
            update_stage_metadata(
                resp_raw, 
                temp_id, 
                "is_optimized", 
                is_success=True, 
                data=False, 
                msg="关联验证完成"
            )
            
            # 保持原仪表盘元数据
            update_dashboard_metadata(
                resp_raw, 
                temp_id, 
                filled_template, 
                is_updated=False,
                extra_data={"report_name": report_name,"template_name": template_name}
            )
    except Exception as e:
        # 解析失败，保留原来的模板
        logger.exception(f"关联验证结果解析失败: {e}")
        update_stage_metadata(
            resp_raw, 
            temp_id, 
            "is_optimized", 
            is_success=False, 
            data=False, 
            msg="关联验证失败"       
              )
        
        # 保持原仪表盘元数据
        update_dashboard_metadata(
            resp_raw, 
            temp_id, 
            filled_template, 
            is_updated=False,
            extra_data={"report_name": report_name,"template_name": template_name}
        )
