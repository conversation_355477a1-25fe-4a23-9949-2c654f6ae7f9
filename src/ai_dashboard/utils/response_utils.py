import copy
from typing import Dict, Any, Optional

def ensure_metadata_exists(resp: Dict[str, Any], key: str) -> None:
    """确保响应中存在元数据字段
    
    Args:
        resp (Dict[str, Any]): 响应字典
        key (str): 消息ID
    """
    if "metadata" not in resp[key]:
        resp[key]["metadata"] = {}

def update_dashboard_metadata(resp: Dict[str, Any], key: str, template: Dict, is_updated: bool = True, msg: str = "仪表盘模板已更新", extra_data: Optional[Dict] = None) -> None:
    """更新仪表盘元数据
    
    Args:
        resp (Dict[str, Any]): 响应字典
        key (str): 消息ID
        template (Dict): 仪表盘模板
        is_updated (bool, optional): 是否更新了模板。默认为True。
        msg (str, optional): 消息文本。默认为"仪表盘模板已更新"。
        extra_data (Dict, optional): 额外的数据，会被添加到data字段中。默认为None。
    """
    ensure_metadata_exists(resp, key)
    
    dashboard_data = {"template": template}
    
    # 如果有额外数据，添加到data字段
    if extra_data:
        dashboard_data.update(extra_data)
    
    resp[key]["metadata"]["dashboard"] = {
        "code": 200,
        "data": dashboard_data,
        "msg": msg if is_updated else "仪表盘模板保持不变"
    }

def update_stage_metadata(
    resp: Dict[str, Any], 
    key: str, 
    stage_name: str, 
    is_success: bool = True, 
    data: Any = None, 
    msg: str = None
) -> None:
    """更新阶段性元数据
    
    Args:
        resp (Dict[str, Any]): 响应字典
        key (str): 消息ID
        stage_name (str): 阶段名称
        is_success (bool, optional): 是否成功。默认为True。
        data (Any, optional): 数据对象。默认为None。
        msg (str, optional): 消息文本。如果为None，将根据stage_name生成默认消息。
    """
    ensure_metadata_exists(resp, key)
    
    if msg is None:
        msg = f"{stage_name}完成"
    
    metadata_content = {
        "code": 200,
        "data": data if data is not None else "",
        "msg": msg
    }
    
    resp[key]["metadata"][stage_name] = metadata_content

