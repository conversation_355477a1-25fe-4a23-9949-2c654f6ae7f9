import copy
import json
from typing import Dict, List, Any, AsyncGenerator, Callable, Union, Optional

from src.aagent.agent.aagent import AAgent
from src.aagent.utils.utils import random_uuid
from src.common.utils.extract_json import ej_agent
from src.common.utils.logger import logger
from src.ai_chat_data.utils.chat_data_utils import match_metadata


async def run_agent_stream(
    agent: AAgent,
    messages: List[Dict[str, str]], 
    resp_raw: Dict[str, Any],
    talk_id: str = None) -> AsyncGenerator[Dict[str, Any], None]:
    """以流式方式运行智能体并处理结果
    
    Args:
        agent (AAgent): 智能体实例
        messages (List[Dict[str, str]]): 消息列表
        resp_raw (Dict[str, Any]): 存储响应的字典
        talk_id (str, optional): 会话ID
        
    Yields:
        Dict[str, Any]: 响应字典的副本
    """
    async for chunk in agent.run(messages=messages):
        temp_id = chunk.pop("id")
        
        # 保存原始响应内容
        resp_raw[temp_id] = copy.deepcopy(chunk)
        
        raw_ans = chunk["ans"]["content"]
        # 匹配JSON代码块
        _metadata = match_metadata(raw_ans, (r"```(json)?", "```"))
        if _metadata["metadata"]:
            before_text = _metadata["before_text"]
            after_text = _metadata["after_text"]
            resp_raw[temp_id]["ans"]["content"] = before_text + after_text
        
        # 添加talk_id到响应中
        if talk_id:
            resp_raw[temp_id]["talk_id"] = talk_id
                       
        yield resp_raw
    resp_raw[temp_id]["ans"]["raw_content"] = raw_ans

async def extract_json_from_response(response_content: str) -> tuple:
    """从响应内容中提取JSON
    
    Args:
        response_content (str): 响应内容
        
    Returns:
        tuple: (提取的JSON对象, 是否成功)
    """
    try:
        json_content, success = await ej_agent.get_json(response_content)
        return json_content, success
    except Exception as e:
        logger.exception(f"JSON提取失败: {e}")
        return None, False 