"""
表结构处理的辅助函数
"""

import json


def extract_table_structure(mschema: dict) -> dict:
    """
    从数据库schema中提取表结构信息，将其转换为简化格式
    
    Args:
        mschema (dict): 原始数据库schema
        
    Returns:
        dict: 简化后的表结构信息，包含表描述和字段信息
    """
    tables_info = {}
    
    if isinstance(mschema, dict) and "tables" in mschema:
        for table_name, table_info in mschema.get("tables", {}).items():
            table_data = {
                "describe": table_info.get("remark") or table_info.get("comment") or "",
                "fields": {}
            }
            
            # 获取列信息
            for field_name, field_info in table_info.get("fields", {}).items():
                table_data["fields"][field_name] = {
                    "describe": field_info.get("remark") or field_info.get("comment") or ""
                }
            
            tables_info[table_name] = table_data
    
    return tables_info


def extract_specific_tables_mschema(mschema: dict, table_names: list) -> dict:
    """
    从完整的mschema中提取指定表的信息
    
    Args:
        mschema (dict): 原始数据库schema
        table_names (list): 要提取的表名列表
        
    Returns:
        dict: 仅包含指定表的mschema子集
    """
    if not isinstance(mschema, dict) or "tables" not in mschema:
        return {"tables": {}}
    
    # 创建一个新的mschema结构，只包含指定的表
    selected_mschema = {
        "db_name": mschema.get("db_name", ""),
        "db_info": mschema.get("db_info", ""),
        "schema": mschema.get("schema", None),
        "tables": {},
        "foreign_keys": []  # 可以选择是否保留外键信息
    }
    
    # 只复制指定表的信息
    for table_name in table_names:
        if table_name in mschema.get("tables", {}):
            selected_mschema["tables"][table_name] = mschema["tables"][table_name]
    
    return selected_mschema


def get_simplified_table_structure(mschema: dict, include_fields: bool = True) -> dict:
    """
    获取简化的表结构信息
    
    Args:
        mschema (dict): 原始数据库schema
        include_fields (bool): 是否包含字段信息，默认为True
        
    Returns:
        dict: 简化后的表结构信息
    """
    tables_info = extract_table_structure(mschema)
    
    if not include_fields:
        # 如果不需要包含字段信息，则只保留表级别的描述
        simple_tables = {}
        for table_name, table_info in tables_info.items():
            simple_tables[table_name] = {
                "describe": table_info["describe"]
            }
        return {"tables": simple_tables}
    
    return {"tables": tables_info}


def get_table_structure_json(mschema: dict, include_fields: bool = True) -> str:
    """
    获取表结构的JSON字符串
    
    Args:
        mschema (dict): 原始数据库schema
        include_fields (bool): 是否包含字段信息，默认为True
        
    Returns:
        str: 表结构的JSON字符串
    """
    structure = get_simplified_table_structure(mschema, include_fields)
    return json.dumps(structure, ensure_ascii=False)


def get_table_structure_by_token_limit(mschema: dict, limit_tokens: int = 7000) -> dict:
    """
    根据token限制获取适当的表结构信息
    
    首先尝试获取包含字段的完整表结构，如果token数量超过限制，
    则返回不包含字段信息的简化表结构
    
    Args:
        mschema (dict): 原始数据库schema
        limit_tokens (int): token数量限制，默认为7000
        
    Returns:
        dict: 根据token限制返回的表结构信息
    """
    from src.ai_dashboard.utils.estimate_tokens import estimate_tokens
    
    # 获取完整表结构
    full_structure = get_simplified_table_structure(mschema, include_fields=True)
    full_json = json.dumps(full_structure, ensure_ascii=False)
    
    # 如果token数量在限制范围内，返回完整表结构
    if estimate_tokens(full_json) <= limit_tokens:
        return full_structure
    
    # 如果超出限制，返回简化表结构（不包含字段信息）
    return get_simplified_table_structure(mschema, include_fields=False)


def get_table_structure_json_by_token_limit(mschema: dict, limit_tokens: int = 7000) -> str:
    """
    根据token限制获取适当的表结构JSON字符串
    
    Args:
        mschema (dict): 原始数据库schema
        limit_tokens (int): token数量限制，默认为7000
        
    Returns:
        str: 根据token限制返回的表结构JSON字符串
    """
    structure = get_table_structure_by_token_limit(mschema, limit_tokens)
    return json.dumps(structure, ensure_ascii=False) 