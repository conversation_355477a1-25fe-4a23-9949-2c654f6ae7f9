import copy
import json

def clear_template_results(template):
    """清空模板中的结果字段
    
    Args:
        template (dict): 原始模板
        
    Returns:
        dict: 清空result字段后的模板副本
    """
    empty_template = copy.deepcopy(template)
    if not empty_template:
        return empty_template
        
    for line_idx, chart_line in enumerate(empty_template):
        for chart_idx, chart in enumerate(chart_line):
            chart["result"] = {}
            chart["target_tables"] = []
            chart["content"] = ''
            chart["chart_title"] = ''
            

    
    return empty_template

def validate_template_structure(original_template, modified_template):
    """验证修改后的模板是否保持了原结构
    
    Args:
        original_template (dict): 原始模板
        modified_template (dict): 修改后的模板
        
    Returns:
        bool: 如果修改后的模板结构与原模板一致则返回True，否则返回False
    """
    # 检查层级数量是否相同
    if len(original_template) != len(modified_template):
        return False
    
    for line_idx in range(len(original_template)):
        # 检查每层的图表数量是否相同
        if len(original_template[line_idx]) != len(modified_template[line_idx]):
            return False
            
    return True


