{"template_name": "【默认】时间序列模版", "template_description": "时间序列仪表盘模版。包含指标卡和趋势图，适合展示数据随时间变化的情况，例如：金融指标的波动趋势。", "template_content": [[{"span": 6, "chart_type": "IndicatorBoard", "chart_title": "当前销售额", "content": "最近一个月的总销售额", "target_tables": ["orders"], "key": "t1bf52f7cec043dd92c75ffff7f74001", "result": {"sql": "SELECT \n    SUM(sales_amount) AS '销售总额'\nFROM \n    sales_data\nWHERE \n    sales_date >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH), '%Y-%m-01')\nLIMIT 100;", "data_frame": [{"销售总额": 2836542.89}], "markdown_res": "| 销售总额 |\n| --- |\n| 2836542.89 |", "result_stats": {"销售总额": {"count": 1.0, "mean": 2836542.89, "std": 2836542.89, "min": 2836542.89, "25%": 2836542.89, "50%": 2836542.89, "75%": 2836542.89, "max": 2836542.89}}, "error": ""}}, {"span": 6, "chart_type": "IndicatorBoard", "chart_title": "当前销售量", "content": "最近一个月的总销售量", "target_tables": ["orders"], "key": "t2cf52f8cec043dd92c75ffff7f74002", "result": {"sql": "SELECT \n    SUM(sales_quantity) AS '销售总量'\nFROM \n    sales_data\nWHERE \n    sales_date >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH), '%Y-%m-01')\nLIMIT 100;", "data_frame": [{"销售总量": 9845}], "markdown_res": "| 销售总量 |\n| --- |\n| 9845 |", "result_stats": {"销售总量": {"count": 1.0, "mean": 9845.0, "std": 9845.0, "min": 9845.0, "25%": 9845.0, "50%": 9845.0, "75%": 9845.0, "max": 9845.0}}, "error": ""}}], [{"span": 12, "chart_type": "Trend", "chart_title": "销售额月度趋势", "content": "销售额随时间变化的趋势，基于销售数据表", "target_tables": ["orders", "products"], "key": "t3df52f9cec043dd92c75ffff7f74003", "result": {"sql": "SELECT \n    DATE_FORMAT(sales_date, '%Y-%m') AS '月份',\n    SUM(sales_amount) AS '销售总额',\n    AVG(sales_amount) AS '平均销售额'\nFROM \n    sales_data\nWHERE \n    sales_date >= DATE_SUB(CURRENT_DATE, INTERVAL 24 MONTH)\nGROUP BY \n    DATE_FORMAT(sales_date, '%Y-%m')\nORDER BY \n    月份\nLIMIT 100;", "data_frame": [{"月份": "2022-07", "销售总额": 1542000.42, "平均销售额": 302.75}, {"月份": "2022-08", "销售总额": 1537000.37, "平均销售额": 301.82}, {"月份": "2022-09", "销售总额": 1551000.51, "平均销售额": 304.89}, {"月份": "2022-10", "销售总额": 1548000.48, "平均销售额": 303.93}, {"月份": "2022-11", "销售总额": 1562000.62, "平均销售额": 306.01}, {"月份": "2022-12", "销售总额": 1765000.05, "平均销售额": 346.33}, {"月份": "2023-01", "销售总额": 1612000.12, "平均销售额": 316.41}, {"月份": "2023-02", "销售总额": 1619000.19, "平均销售额": 317.52}, {"月份": "2023-03", "销售总额": 1625000.25, "平均销售额": 318.59}, {"月份": "2023-04", "销售总额": 1631000.31, "平均销售额": 319.67}, {"月份": "2023-05", "销售总额": 1638000.38, "平均销售额": 321.72}, {"月份": "2023-06", "销售总额": 1642000.42, "平均销售额": 322.78}, {"月份": "2023-07", "销售总额": 1649000.49, "平均销售额": 323.85}, {"月份": "2023-08", "销售总额": 1653000.53, "平均销售额": 324.91}, {"月份": "2023-09", "销售总额": 1659000.59, "平均销售额": 325.98}, {"月份": "2023-10", "销售总额": 1664000.64, "平均销售额": 326.03}, {"月份": "2023-11", "销售总额": 1671000.71, "平均销售额": 328.09}, {"月份": "2023-12", "销售总额": 1895000.95, "平均销售额": 372.32}, {"月份": "2024-01", "销售总额": 1702000.02, "平均销售额": 334.41}, {"月份": "2024-02", "销售总额": 1708000.08, "平均销售额": 335.47}, {"月份": "2024-03", "销售总额": 1715000.15, "平均销售额": 336.53}, {"月份": "2024-04", "销售总额": 1721000.21, "平均销售额": 337.59}, {"月份": "2024-05", "销售总额": 1726000.26, "平均销售额": 338.64}, {"月份": "2024-06", "销售总额": 1732000.32, "平均销售额": 339.71}], "markdown_res": "| 月份 | 销售总额 | 平均销售额 |\n| --- | --- | --- |\n| 2022-07 | 1542000.42 | 302.75 |\n| 2022-08 | 1537000.37 | 301.82 |\n| 2022-09 | 1551000.51 | 304.89 |\n| 2022-10 | 1548000.48 | 303.93 |\n| 2022-11 | 1562000.62 | 306.01 |\n| 2022-12 | 1765000.05 | 346.33 |\n| 2023-01 | 1612000.12 | 316.41 |\n| 2023-02 | 1619000.19 | 317.52 |\n| 2023-03 | 1625000.25 | 318.59 |\n| 2023-04 | 1631000.31 | 319.67 |\n| 2023-05 | 1638000.38 | 321.72 |\n| 2023-06 | 1642000.42 | 322.78 |\n| 2023-07 | 1649000.49 | 323.85 |\n| 2023-08 | 1653000.53 | 324.91 |\n| 2023-09 | 1659000.59 | 325.98 |\n| 2023-10 | 1664000.64 | 326.03 |\n| 2023-11 | 1671000.71 | 328.09 |\n| 2023-12 | 1895000.95 | 372.32 |\n| 2024-01 | 1702000.02 | 334.41 |\n| 2024-02 | 1708000.08 | 335.47 |\n| 2024-03 | 1715000.15 | 336.53 |\n| 2024-04 | 1721000.21 | 337.59 |\n| 2024-05 | 1726000.26 | 338.64 |\n| 2024-06 | 1732000.32 | 339.71 |", "result_stats": {"月份": {"count": 24, "unique": 24, "top": "2022-07", "freq": 1}, "销售总额": {"count": 24.0, "mean": 1659000.46, "std": 81000.61, "min": 1537000.37, "25%": 1620000.99, "50%": 1655000.56, "75%": 1705000.05, "max": 1895000.95}, "平均销售额": {"count": 24.0, "mean": 325.83, "std": 16.63, "min": 301.82, "25%": 318.28, "50%": 324.95, "75%": 335.44, "max": 372.32}}, "error": ""}}], [{"span": 12, "chart_type": "Trend", "chart_title": "销售量月度趋势", "content": "销售量随时间变化的趋势，基于销售数据表", "target_tables": ["orders", "products"], "key": "t4ef52f6cec043dd92c75ffff7f74004", "result": {"sql": "SELECT \n    DATE_FORMAT(sales_date, '%Y-%m') AS '月份',\n    SUM(sales_quantity) AS '销售总量',\n    AVG(sales_quantity) AS '平均单笔销量',\n    COUNT(*) AS '订单数量'\nFROM \n    sales_data\nWHERE \n    sales_date >= DATE_SUB(CURRENT_DATE, INTERVAL 24 MONTH)\nGROUP BY \n    DATE_FORMAT(sales_date, '%Y-%m')\nORDER BY \n    月份\nLIMIT 100;", "data_frame": [{"月份": "2022-07", "销售总量": 7568, "平均单笔销量": 1.49, "订单数量": 5080}, {"月份": "2022-08", "销售总量": 7843, "平均单笔销量": 1.52, "订单数量": 5160}, {"月份": "2022-09", "销售总量": 7895, "平均单笔销量": 1.52, "订单数量": 5195}, {"月份": "2022-10", "销售总量": 7932, "平均单笔销量": 1.5, "订单数量": 5288}, {"月份": "2022-11", "销售总量": 8245, "平均单笔销量": 1.54, "订单数量": 5354}, {"月份": "2022-12", "销售总量": 9632, "平均单笔销量": 1.68, "订单数量": 5733}, {"月份": "2023-01", "销售总量": 8321, "平均单笔销量": 1.58, "订单数量": 5266}, {"月份": "2023-02", "销售总量": 8143, "平均单笔销量": 1.61, "订单数量": 5058}, {"月份": "2023-03", "销售总量": 8321, "平均单笔销量": 1.59, "订单数量": 5233}, {"月份": "2023-04", "销售总量": 8456, "平均单笔销量": 1.62, "订单数量": 5220}, {"月份": "2023-05", "销售总量": 8543, "平均单笔销量": 1.64, "订单数量": 5209}, {"月份": "2023-06", "销售总量": 8578, "平均单笔销量": 1.64, "订单数量": 5230}, {"月份": "2023-07", "销售总量": 8612, "平均单笔销量": 1.63, "订单数量": 5284}, {"月份": "2023-08", "销售总量": 8654, "平均单笔销量": 1.63, "订单数量": 5309}, {"月份": "2023-09", "销售总量": 8789, "平均单笔销量": 1.64, "订单数量": 5359}, {"月份": "2023-10", "销售总量": 8932, "平均单笔销量": 1.66, "订单数量": 5381}, {"月份": "2023-11", "销售总量": 9065, "平均单笔销量": 1.67, "订单数量": 5428}, {"月份": "2023-12", "销售总量": 10354, "平均单笔销量": 1.82, "订单数量": 5689}, {"月份": "2024-01", "销售总量": 9121, "平均单笔销量": 1.71, "订单数量": 5334}, {"月份": "2024-02", "销售总量": 9145, "平均单笔销量": 1.73, "订单数量": 5287}, {"月份": "2024-03", "销售总量": 9321, "平均单笔销量": 1.75, "订单数量": 5326}, {"月份": "2024-04", "销售总量": 9567, "平均单笔销量": 1.77, "订单数量": 5404}, {"月份": "2024-05", "销售总量": 9765, "平均单笔销量": 1.79, "订单数量": 5456}, {"月份": "2024-06", "销售总量": 9845, "平均单笔销量": 1.81, "订单数量": 5439}], "markdown_res": "| 月份 | 销售总量 | 平均单笔销量 | 订单数量 |\n| --- | --- | --- | --- |\n| 2022-07 | 7568 | 1.49 | 5080 |\n| 2022-08 | 7843 | 1.52 | 5160 |\n| 2022-09 | 7895 | 1.52 | 5195 |\n| 2022-10 | 7932 | 1.50 | 5288 |\n| 2022-11 | 8245 | 1.54 | 5354 |\n| 2022-12 | 9632 | 1.68 | 5733 |\n| 2023-01 | 8321 | 1.58 | 5266 |\n| 2023-02 | 8143 | 1.61 | 5058 |\n| 2023-03 | 8321 | 1.59 | 5233 |\n| 2023-04 | 8456 | 1.62 | 5220 |\n| 2023-05 | 8543 | 1.64 | 5209 |\n| 2023-06 | 8578 | 1.64 | 5230 |\n| 2023-07 | 8612 | 1.63 | 5284 |\n| 2023-08 | 8654 | 1.63 | 5309 |\n| 2023-09 | 8789 | 1.64 | 5359 |\n| 2023-10 | 8932 | 1.66 | 5381 |\n| 2023-11 | 9065 | 1.67 | 5428 |\n| 2023-12 | 10354 | 1.82 | 5689 |\n| 2024-01 | 9121 | 1.71 | 5334 |\n| 2024-02 | 9145 | 1.73 | 5287 |\n| 2024-03 | 9321 | 1.75 | 5326 |\n| 2024-04 | 9567 | 1.77 | 5404 |\n| 2024-05 | 9765 | 1.79 | 5456 |\n| 2024-06 | 9845 | 1.81 | 5439 |", "result_stats": {"月份": {"count": 24, "unique": 24, "top": "2022-07", "freq": 1}, "销售总量": {"count": 24.0, "mean": 8739.46, "std": 733.64, "min": 7568.0, "25%": 8225.75, "50%": 8684.5, "75%": 9311.5, "max": 10354.0}, "平均单笔销量": {"count": 24.0, "mean": 1.65, "std": 0.09, "min": 1.49, "25%": 1.59, "50%": 1.64, "75%": 1.73, "max": 1.82}, "订单数量": {"count": 24.0, "mean": 5308.88, "std": 146.77, "min": 5058.0, "25%": 5229.75, "50%": 5293.0, "75%": 5392.75, "max": 5733.0}}, "error": ""}}]]}