{"template_name": "【默认】混合图表模版", "template_description": "混合图表仪表盘模版。包含指标卡、柱状图、饼图等多种图表类型，适合多维度数据展示。", "template_content": [[{"span": 6, "chart_type": "IndicatorBoard", "chart_title": "销售总额", "content": "当前销售总额", "target_tables": ["orders", "products"], "key": "m1af52f7cec043dd92c75ffff7f73001", "result": {"sql": "SELECT \n    SUM(products.price * orders.quantity) AS '销售总额'\nFROM \n    products \nJOIN \n    orders \nON \n    products.product_id = orders.product_id\nLIMIT 100;", "data_frame": [{"销售总额": 3812795.18}], "markdown_res": "| 销售总额 |\n| --- |\n| 3812795.18 |", "result_stats": {"销售总额": {"count": 1.0, "mean": 3812795.18, "std": 3812795.18, "min": 3812795.18, "25%": 3812795.18, "50%": 3812795.18, "75%": 3812795.18, "max": 3812795.18}}, "error": ""}}, {"span": 6, "chart_type": "IndicatorBoard", "chart_title": "利润率", "content": "当前利润率", "target_tables": ["orders", "products"], "key": "m2bf52f8cec043dd92c75ffff7f73002", "result": {"sql": "WITH revenue AS (\n    SELECT \n        SUM(products.price * orders.quantity) AS total_revenue\n    FROM \n        products \n    JOIN \n        orders \n    ON \n        products.product_id = orders.product_id\n),\ncosts AS (\n    SELECT \n        SUM(products.cost_price * orders.quantity) AS total_cost\n    FROM \n        products \n    JOIN \n        orders \n    ON \n        products.product_id = orders.product_id\n)\nSELECT \n    ROUND(((revenue.total_revenue - costs.total_cost) / revenue.total_revenue) * 100, 2) AS '利润率'\nFROM \n    revenue, costs\nLIMIT 100;", "data_frame": [{"利润率": 37.82}], "markdown_res": "| 利润率 |\n| --- |\n| 37.82 |", "result_stats": {"利润率": {"count": 1.0, "mean": 37.82, "std": 37.82, "min": 37.82, "25%": 37.82, "50%": 37.82, "75%": 37.82, "max": 37.82}}, "error": ""}}], [{"span": 6, "chart_type": "Proportion", "chart_title": "各部门销售情况", "content": "各部门销售额占比", "target_tables": ["departments", "employees", "orders", "products"], "key": "m3cf52f9cec043dd92c75ffff7f73003", "result": {"sql": "SELECT \n    d.department_name AS '部门',\n    SUM(p.price * o.quantity) AS '销售额'\nFROM \n    departments d\nJOIN \n    employees e ON d.department_id = e.department_id\nJOIN \n    orders o ON e.employee_id = o.employee_id\nJOIN \n    products p ON o.product_id = p.product_id\nGROUP BY \n    d.department_name\nORDER BY \n    销售额 DESC\nLIMIT 100;", "data_frame": [{"部门": "销售部", "销售额": 1524678.45}, {"部门": "市场部", "销售额": 985215.65}, {"部门": "客户服务部", "销售额": 755431.28}, {"部门": "技术支持部", "销售额": 345896.47}, {"部门": "产品开发部", "销售额": 201573.33}], "markdown_res": "| 部门 | 销售额 |\n| --- | --- |\n| 销售部 | 1524678.45 |\n| 市场部 | 985215.65 |\n| 客户服务部 | 755431.28 |\n| 技术支持部 | 345896.47 |\n| 产品开发部 | 201573.33 |", "result_stats": {"部门": {"count": 5, "unique": 5, "top": "销售部", "freq": 1}, "销售额": {"count": 5, "mean": 762559.04, "std": 526747.56, "min": 201573.33, "25%": 345896.47, "50%": 755431.28, "75%": 985215.65, "max": 1524678.45}}, "error": ""}}, {"span": 6, "chart_type": "Proportion", "chart_title": "各地区销售额情况", "content": "各地区销售额占比", "target_tables": ["customers", "orders", "products"], "key": "m4df52f6cec043dd92c75ffff7f73004", "result": {"sql": "SELECT \n    c.region AS '地区',\n    SUM(p.price * o.quantity) AS '销售额'\nFROM \n    customers c\nJOIN \n    orders o ON c.customer_id = o.customer_id\nJOIN \n    products p ON o.product_id = p.product_id\nGROUP BY \n    c.region\nORDER BY \n    销售额 DESC\nLIMIT 100;", "data_frame": [{"地区": "华东", "销售额": 1234567.89}, {"地区": "华北", "销售额": 954321.65}, {"地区": "华南", "销售额": 765432.18}, {"地区": "西南", "销售额": 543216.79}, {"地区": "西北", "销售额": 315246.67}], "markdown_res": "| 地区 | 销售额 |\n| --- | --- |\n| 华东 | 1234567.89 |\n| 华北 | 954321.65 |\n| 华南 | 765432.18 |\n| 西南 | 543216.79 |\n| 西北 | 315246.67 |", "result_stats": {"地区": {"count": 5, "unique": 5, "top": "华东", "freq": 1}, "销售额": {"count": 5, "mean": 762557.04, "std": 354926.73, "min": 315246.67, "25%": 543216.79, "50%": 765432.18, "75%": 954321.65, "max": 1234567.89}}, "error": ""}}], [{"span": 6, "chart_type": "Trend", "chart_title": "近12个月销售趋势", "content": "近12个月销售额和订单量变化趋势", "target_tables": ["orders", "products"], "key": "m5ef52f5cec043dd92c75ffff7f73005", "result": {"sql": "SELECT \n    DATE_FORMAT(o.order_date, '%Y-%m') AS '月份',\n    COUNT(DISTINCT o.order_id) AS '订单量',\n    SUM(p.price * o.quantity) AS '销售额'\nFROM \n    orders o\nJOIN \n    products p ON o.product_id = p.product_id\nWHERE \n    o.order_date >= DATE_SUB(CURRENT_DATE, INTERVAL 12 MONTH)\nGROUP BY \n    DATE_FORMAT(o.order_date, '%Y-%m')\nORDER BY \n    月份\nLIMIT 100;", "data_frame": [{"月份": "2023-07", "订单量": 156, "销售额": 121169.16}, {"月份": "2023-08", "订单量": 142, "销售额": 84697.06}, {"月份": "2023-09", "订单量": 178, "销售额": 195315.07}, {"月份": "2023-10", "订单量": 189, "销售额": 226203.71}, {"月份": "2023-11", "订单量": 195, "销售额": 303897.12}, {"月份": "2023-12", "订单量": 203, "销售额": 231599.14}, {"月份": "2024-01", "订单量": 187, "销售额": 275884.75}, {"月份": "2024-02", "订单量": 168, "销售额": 182420.9}, {"月份": "2024-03", "订单量": 172, "销售额": 154131.42}, {"月份": "2024-04", "订单量": 193, "销售额": 329099.11}, {"月份": "2024-05", "订单量": 175, "销售额": 229468.19}, {"月份": "2024-06", "订单量": 164, "销售额": 168504.42}], "markdown_res": "| 月份 | 订单量 | 销售额 |\n| --- | --- | --- |\n| 2023-07 | 156 | 121169.16 |\n| 2023-08 | 142 | 84697.06 |\n| 2023-09 | 178 | 195315.07 |\n| 2023-10 | 189 | 226203.71 |\n| 2023-11 | 195 | 303897.12 |\n| 2023-12 | 203 | 231599.14 |\n| 2024-01 | 187 | 275884.75 |\n| 2024-02 | 168 | 182420.90 |\n| 2024-03 | 172 | 154131.42 |\n| 2024-04 | 193 | 329099.11 |\n| 2024-05 | 175 | 229468.19 |\n| 2024-06 | 164 | 168504.42 |", "result_stats": {"月份": {"count": 12, "unique": 12, "top": "2023-07", "freq": 1}, "订单量": {"count": 12.0, "mean": 176.83, "std": 17.74, "min": 142.0, "25%": 166.0, "50%": 176.5, "75%": 191.0, "max": 203.0}, "销售额": {"count": 12.0, "mean": 208532.5, "std": 73991.26, "min": 84697.06, "25%": 154131.42, "50%": 195315.07, "75%": 276907.38, "max": 329099.11}}, "error": ""}}, {"span": 6, "chart_type": "Rank", "chart_title": "产品销售TOP5", "content": "销售额排名前五的产品", "target_tables": ["orders", "products"], "key": "m6ff52f4cec043dd92c75ffff7f73006", "result": {"sql": "SELECT \n    p.product_name AS '产品名称',\n    SUM(p.price * o.quantity) AS '销售额'\nFROM \n    products p\nJOIN \n    orders o ON p.product_id = o.product_id\nGROUP BY \n    p.product_name\nORDER BY \n    销售额 DESC\nLIMIT 5;", "data_frame": [{"产品名称": "M60手机16+512G", "销售额": 528000.0}, {"产品名称": "P30手机8+256G", "销售额": 387500.0}, {"产品名称": "OPPO Reno7 Pro", "销售额": 198400.0}, {"产品名称": "iPhone 14", "销售额": 185400.0}, {"产品名称": "2L大容量空气炸锅", "销售额": 171120.0}], "markdown_res": "| 产品名称 | 销售额 |\n| --- | --- |\n| M60手机16+512G | 528000.00 |\n| P30手机8+256G | 387500.00 |\n| OPPO Reno7 Pro | 198400.00 |\n| iPhone 14 | 185400.00 |\n| 2L大容量空气炸锅 | 171120.00 |", "result_stats": {"产品名称": {"count": 5, "unique": 5, "top": "M60手机16+512G", "freq": 1}, "销售额": {"count": 5.0, "mean": 294084.0, "std": 153834.91, "min": 171120.0, "25%": 185400.0, "50%": 198400.0, "75%": 387500.0, "max": 528000.0}}, "error": ""}}], [{"span": 12, "chart_type": "Table", "chart_title": "业务数据明细", "content": "重点业务数据明细表", "target_tables": ["customers", "orders", "products"], "key": "m7af52f3cec043dd92c75ffff7f73007", "result": {"sql": "SELECT \n    o.order_id AS '订单编号',\n    o.order_date AS '订单日期',\n    c.customer_name AS '客户名称',\n    p.product_name AS '产品名称',\n    o.quantity AS '数量',\n    p.price AS '单价',\n    (p.price * o.quantity) AS '金额',\n    o.status AS '订单状态'\nFROM \n    orders o\nJOIN \n    customers c ON o.customer_id = c.customer_id\nJOIN \n    products p ON o.product_id = p.product_id\nORDER BY \n    o.order_date DESC\nLIMIT 20;", "data_frame": [{"订单编号": 1215, "订单日期": "2025-01-20", "客户名称": "张三", "产品名称": "M60手机16+512G", "数量": 2, "单价": 8000.0, "金额": 16000.0, "订单状态": "已完成"}, {"订单编号": 1214, "订单日期": "2025-01-19", "客户名称": "李四", "产品名称": "2L大容量空气炸锅", "数量": 1, "单价": 899.0, "金额": 899.0, "订单状态": "已完成"}, {"订单编号": 1213, "订单日期": "2025-01-18", "客户名称": "王五", "产品名称": "iPhone 14", "数量": 1, "单价": 6999.0, "金额": 6999.0, "订单状态": "已完成"}, {"订单编号": 1212, "订单日期": "2025-01-17", "客户名称": "赵六", "产品名称": "2L大容量便携式榨汁机", "数量": 2, "单价": 599.0, "金额": 1198.0, "订单状态": "已完成"}, {"订单编号": 1211, "订单日期": "2025-01-16", "客户名称": "钱七", "产品名称": "2L大容量电热水壶", "数量": 3, "单价": 329.0, "金额": 987.0, "订单状态": "已完成"}, {"订单编号": 1210, "订单日期": "2025-01-15", "客户名称": "孙八", "产品名称": "OPPO Reno7 Pro", "数量": 1, "单价": 3799.0, "金额": 3799.0, "订单状态": "运输中"}, {"订单编号": 1209, "订单日期": "2025-01-14", "客户名称": "周九", "产品名称": "1L迷你便携式榨汁机", "数量": 2, "单价": 399.0, "金额": 798.0, "订单状态": "运输中"}, {"订单编号": 1208, "订单日期": "2025-01-13", "客户名称": "吴十", "产品名称": "1L迷你全自动豆浆机", "数量": 1, "单价": 499.0, "金额": 499.0, "订单状态": "已完成"}, {"订单编号": 1207, "订单日期": "2025-01-12", "客户名称": "郑十一", "产品名称": "GTX1080显卡", "数量": 1, "单价": 4999.0, "金额": 4999.0, "订单状态": "已完成"}, {"订单编号": 1206, "订单日期": "2025-01-11", "客户名称": "王十二", "产品名称": "P30手机8+256G", "数量": 1, "单价": 3500.0, "金额": 3500.0, "订单状态": "运输中"}], "markdown_res": "| 订单编号 | 订单日期 | 客户名称 | 产品名称 | 数量 | 单价 | 金额 | 订单状态 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| 1215 | 2025-01-20 | 张三 | M60手机16+512G | 2 | 8000.00 | 16000.00 | 已完成 |\n| 1214 | 2025-01-19 | 李四 | 2L大容量空气炸锅 | 1 | 899.00 | 899.00 | 已完成 |\n| 1213 | 2025-01-18 | 王五 | iPhone 14 | 1 | 6999.00 | 6999.00 | 已完成 |\n| 1212 | 2025-01-17 | 赵六 | 2L大容量便携式榨汁机 | 2 | 599.00 | 1198.00 | 已完成 |\n| 1211 | 2025-01-16 | 钱七 | 2L大容量电热水壶 | 3 | 329.00 | 987.00 | 已完成 |\n| 1210 | 2025-01-15 | 孙八 | OPPO Reno7 Pro | 1 | 3799.00 | 3799.00 | 运输中 |\n| 1209 | 2025-01-14 | 周九 | 1L迷你便携式榨汁机 | 2 | 399.00 | 798.00 | 运输中 |\n| 1208 | 2025-01-13 | 吴十 | 1L迷你全自动豆浆机 | 1 | 499.00 | 499.00 | 已完成 |\n| 1207 | 2025-01-12 | 郑十一 | GTX1080显卡 | 1 | 4999.00 | 4999.00 | 已完成 |\n| 1206 | 2025-01-11 | 王十二 | P30手机8+256G | 1 | 3500.00 | 3500.00 | 运输中 |", "result_stats": {"订单编号": {"count": 10.0, "mean": 1210.5, "std": 3.03, "min": 1206.0, "25%": 1208.0, "50%": 1210.5, "75%": 1213.0, "max": 1215.0}, "订单日期": {"count": 10, "unique": 10, "top": "2025-01-20", "freq": 1}, "客户名称": {"count": 10, "unique": 10, "top": "张三", "freq": 1}, "产品名称": {"count": 10, "unique": 10, "top": "M60手机16+512G", "freq": 1}, "数量": {"count": 10.0, "mean": 1.5, "std": 0.71, "min": 1.0, "25%": 1.0, "50%": 1.0, "75%": 2.0, "max": 3.0}, "单价": {"count": 10.0, "mean": 2992.2, "std": 2838.23, "min": 329.0, "25%": 499.0, "50%": 2149.5, "75%": 4999.0, "max": 8000.0}, "金额": {"count": 10.0, "mean": 3967.8, "std": 4767.14, "min": 499.0, "25%": 899.0, "50%": 2093.5, "75%": 4999.0, "max": 16000.0}, "订单状态": {"count": 10, "unique": 2, "top": "已完成", "freq": 7}}, "error": ""}}]]}