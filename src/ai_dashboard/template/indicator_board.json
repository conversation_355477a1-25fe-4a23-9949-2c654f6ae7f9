{"template_name": "【默认】指标卡仪表模版", "template_description": "指标卡仪表盘模版,9个图表元素。全部为指标卡布局，适合展示多个关键指标的当前值，例如：KPI指标的最新情况。", "template_content": [[{"span": 4, "chart_type": "IndicatorBoard", "chart_title": "本月销售额", "content": "本月销售总额", "target_tables": ["orders", "products"], "key": "a9bf62f7cec043dd92c75ffff7f72001", "result": {"sql": "SELECT \n    SUM(products.price * orders.quantity) AS '本月销售总额'\nFROM \n    products \nJOIN \n    orders \nON \n    products.product_id = orders.product_id\nWHERE \n    DATE_FORMAT(orders.order_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')\nLIMIT 100;", "data_frame": [{"本月销售总额": 329099.11}], "markdown_res": "| 本月销售总额 |\n| --- |\n| 329099.11 |", "result_stats": {"本月销售总额": {"count": 1.0, "mean": 329099.11, "std": "", "min": 329099.11, "25%": 329099.11, "50%": 329099.11, "75%": 329099.11, "max": 329099.11}}, "error": ""}}, {"span": 4, "chart_type": "IndicatorBoard", "chart_title": "本月订单量", "content": "本月订单总数", "target_tables": ["orders"], "key": "b7ef42f8cec043dd92c75ffff7f72002", "result": {"sql": "SELECT \n    COUNT(order_id) AS '本月订单总数'\nFROM \n    orders\nWHERE \n    DATE_FORMAT(order_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')\nLIMIT 100;", "data_frame": [{"本月订单总数": 125}], "markdown_res": "| 本月订单总数 |\n| --- |\n| 125 |", "result_stats": {"本月订单总数": {"count": 1.0, "mean": 125.0, "std": 125.0, "min": 125.0, "25%": 125.0, "50%": 125.0, "75%": 125.0, "max": 125.0}}, "error": ""}}, {"span": 4, "chart_type": "IndicatorBoard", "chart_title": "客户转化率", "content": "本月客户转化率", "target_tables": ["customers", "orders"], "key": "c5df42f9cec043dd92c75ffff7f72003", "result": {"sql": "SELECT \n    ROUND((COUNT(DISTINCT customer_id) / (SELECT COUNT(*) FROM customers)) * 100, 2) AS '客户转化率'\nFROM \n    orders\nWHERE \n    DATE_FORMAT(order_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')\nLIMIT 100;", "data_frame": [{"客户转化率": 42.0}], "markdown_res": "| 客户转化率 |\n| --- |\n| 42.00 |", "result_stats": {"客户转化率": {"count": 1.0, "mean": 42.0, "std": 42.0, "min": 42.0, "25%": 42.0, "50%": 42.0, "75%": 42.0, "max": 42.0}}, "error": ""}}], [{"span": 4, "chart_type": "IndicatorBoard", "chart_title": "月度增长率", "content": "销售额月度增长率", "target_tables": ["orders", "products"], "key": "d3cf42f6cec043dd92c75ffff7f72004", "result": {"sql": "WITH current_month AS (\n    SELECT \n        SUM(products.price * orders.quantity) AS total\n    FROM \n        products \n    JOIN \n        orders \n    ON \n        products.product_id = orders.product_id\n    WHERE \n        DATE_FORMAT(orders.order_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')\n),\nprevious_month AS (\n    SELECT \n        SUM(products.price * orders.quantity) AS total\n    FROM \n        products \n    JOIN \n        orders \n    ON \n        products.product_id = orders.product_id\n    WHERE \n        DATE_FORMAT(orders.order_date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH), '%Y-%m')\n)\nSELECT \n    ROUND(((current_month.total - previous_month.total) / previous_month.total) * 100, 2) AS '月度增长率'\nFROM \n    current_month, previous_month\nLIMIT 100;", "data_frame": [{"月度增长率": 17.35}], "markdown_res": "| 月度增长率 |\n| --- |\n| 17.35 |", "result_stats": {"月度增长率": {"count": 1.0, "mean": 17.35, "std": 17.35, "min": 17.35, "25%": 17.35, "50%": 17.35, "75%": 17.35, "max": 17.35}}, "error": ""}}, {"span": 4, "chart_type": "IndicatorBoard", "chart_title": "客单价", "content": "平均客单价", "target_tables": ["orders", "products"], "key": "e2bf42f5cec043dd92c75ffff7f72005", "result": {"sql": "SELECT \n    ROUND(AVG(order_total), 2) AS '平均客单价'\nFROM \n    (\n        SELECT \n            orders.order_id,\n            SUM(products.price * orders.quantity) AS order_total\n        FROM \n            products \n        JOIN \n            orders \n        ON \n            products.product_id = orders.product_id\n        GROUP BY \n            orders.order_id\n    ) AS order_totals\nLIMIT 100;", "data_frame": [{"平均客单价": 2631.79}], "markdown_res": "| 平均客单价 |\n| --- |\n| 2631.79 |", "result_stats": {"平均客单价": {"count": 1.0, "mean": 2631.79, "std": 2631.79, "min": 2631.79, "25%": 2631.79, "50%": 2631.79, "75%": 2631.79, "max": 2631.79}}, "error": ""}}, {"span": 4, "chart_type": "IndicatorBoard", "chart_title": "库存周转率", "content": "当前库存周转率", "target_tables": ["inventory", "orders", "products"], "key": "f1af42f4cec043dd92c75ffff7f72006", "result": {"sql": "WITH sold_cost AS (\n    SELECT \n        SUM(products.cost_price * orders.quantity) AS total_cost\n    FROM \n        products \n    JOIN \n        orders \n    ON \n        products.product_id = orders.product_id\n    WHERE \n        DATE_FORMAT(orders.order_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')\n),\naverage_inventory AS (\n    SELECT \n        AVG(products.cost_price * inventory.stock_level) AS avg_inventory_value\n    FROM \n        products \n    JOIN \n        inventory \n    ON \n        products.product_id = inventory.product_id\n)\nSELECT \n    ROUND((sold_cost.total_cost / average_inventory.avg_inventory_value), 2) AS '库存周转率'\nFROM \n    sold_cost, average_inventory\nLIMIT 100;", "data_frame": [{"库存周转率": 3.56}], "markdown_res": "| 库存周转率 |\n| --- |\n| 3.56 |", "result_stats": {"库存周转率": {"count": 1.0, "mean": 3.56, "std": 3.56, "min": 3.56, "25%": 3.56, "50%": 3.56, "75%": 3.56, "max": 3.56}}, "error": ""}}], [{"span": 4, "chart_type": "IndicatorBoard", "chart_title": "人力成本率", "content": "当前人力成本率", "target_tables": ["employees", "orders", "products"], "key": "g0af42f3cec043dd92c75ffff7f72007", "result": {"sql": "WITH monthly_revenue AS (\n    SELECT \n        SUM(products.price * orders.quantity) AS total_revenue\n    FROM \n        products \n    JOIN \n        orders \n    ON \n        products.product_id = orders.product_id\n    WHERE \n        DATE_FORMAT(orders.order_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')\n),\nmonthly_salary AS (\n    SELECT \n        SUM(salary) AS total_salary\n    FROM \n        employees\n)\nSELECT \n    ROUND((monthly_salary.total_salary / monthly_revenue.total_revenue) * 100, 2) AS '人力成本率'\nFROM \n    monthly_salary, monthly_revenue\nLIMIT 100;", "data_frame": [{"人力成本率": 22.45}], "markdown_res": "| 人力成本率 |\n| --- |\n| 22.45 |", "result_stats": {"人力成本率": {"count": 1.0, "mean": 22.45, "std": 22.45, "min": 22.45, "25%": 22.45, "50%": 22.45, "75%": 22.45, "max": 22.45}}, "error": ""}}, {"span": 4, "chart_type": "IndicatorBoard", "chart_title": "新客户数量", "content": "本月新增客户数", "target_tables": ["customers"], "key": "h9af42f2cec043dd92c75ffff7f72008", "result": {"sql": "SELECT \n    COUNT(customer_id) AS '本月新增客户数'\nFROM \n    customers\nWHERE \n    DATE_FORMAT(registration_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')\nLIMIT 100;", "data_frame": [{"本月新增客户数": 28}], "markdown_res": "| 本月新增客户数 |\n| --- |\n| 28 |", "result_stats": {"本月新增客户数": {"count": 1.0, "mean": 28.0, "std": 28.0, "min": 28.0, "25%": 28.0, "50%": 28.0, "75%": 28.0, "max": 28.0}}, "error": ""}}, {"span": 4, "chart_type": "IndicatorBoard", "chart_title": "客户满意度", "content": "本月客户满意度评分", "target_tables": ["feedback", "orders"], "key": "i8af42f1cec043dd92c75ffff7f72009", "result": {"sql": "SELECT \n    ROUND(AVG(rating), 2) AS '客户满意度评分'\nFROM \n    feedback\nJOIN \n    orders \nON \n    feedback.order_id = orders.order_id\nWHERE \n    DATE_FORMAT(orders.order_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m')\nLIMIT 100;", "data_frame": [{"客户满意度评分": 4.65}], "markdown_res": "| 客户满意度评分 |\n| --- |\n| 4.65 |", "result_stats": {"客户满意度评分": {"count": 1.0, "mean": 4.65, "std": 4.65, "min": 4.65, "25%": 4.65, "50%": 4.65, "75%": 4.65, "max": 4.65}}, "error": ""}}]]}