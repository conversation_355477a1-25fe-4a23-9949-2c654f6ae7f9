import os
import sys
import json

from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.auth.authorize import get_user_id

from typing import *
from fastapi import APIRouter, Security
from fastapi.responses import StreamingResponse, JSONResponse

router = APIRouter()

from src.ai_dashboard.schemas.report_manage_schema import CreateReport
from src.ai_dashboard.schemas.report_manage_schema import CreateReportResponse
from src.ai_dashboard.services.report_manage_service import create_report


@router.post(
    "/create_report",
    response_model=CreateReportResponse,
    summary="报表配置: 保存报表",
)
async def _create_report(
    input_data: CreateReport, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await create_report(
        user_id=user_id,
        data_id=input_data.data_id,
        report_name=input_data.report_name,
        template=input_data.template,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_dashboard.schemas.report_manage_schema import DeleteReport
from src.ai_dashboard.schemas.report_manage_schema import DeleteReportResponse
from src.ai_dashboard.services.report_manage_service import delete_report


@router.post(
    "/delete_report",
    response_model=DeleteReportResponse,
    summary="报表配置: 删除报表",
)
async def _delete_report(
    input_data: DeleteReport, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await delete_report(
        user_id=user_id,
        report_ids=input_data.report_ids,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_dashboard.schemas.report_manage_schema import FetchReportList
from src.ai_dashboard.schemas.report_manage_schema import FetchReportListResponse
from src.ai_dashboard.services.report_manage_service import fetch_report_list


@router.post(
    "/fetch_report_list",
    response_model=FetchReportListResponse,
    summary="报表配置: 获取报表列表",
)
async def _fetch_report_list(
    input_data: FetchReportList, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_report_list(
        user_id=user_id,
        key_word=input_data.key_word,
        page_size=input_data.page_size,
        page_number=input_data.page_number,
        enable_page=input_data.enable_page,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_dashboard.schemas.report_manage_schema import FetchReportByID
from src.ai_dashboard.schemas.report_manage_schema import FetchReportByIDResponse
from src.ai_dashboard.services.report_manage_service import fetch_report_dy_id


@router.post(
    "/fetch_report_dy_id",
    response_model=FetchReportByIDResponse,
    summary="报表配置: 获取报表信息",
)
async def _fetch_report_dy_id(
    input_data: FetchReportByID, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_report_dy_id(
        user_id=user_id,
        report_id=input_data.report_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_dashboard.schemas.report_manage_schema import FetchReportTipByID
from src.ai_dashboard.schemas.report_manage_schema import FetchReportTipByIDResponse
from src.ai_dashboard.services.report_manage_service import fetch_report_tip_dy_id


@router.post(
    "/fetch_report_tip_dy_id",
    response_model=FetchReportTipByIDResponse,
    summary="报表配置: 获取报表提示信息",
)
async def _fetch_report_tip_dy_id(
    input_data: FetchReportTipByID, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await fetch_report_tip_dy_id(
        user_id=user_id,
        report_id=input_data.report_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_dashboard.schemas.report_manage_schema import FlushReportByID
from src.ai_dashboard.schemas.report_manage_schema import FlushReportByIDResponse
from src.ai_dashboard.services.report_manage_service import flush_report_dy_id


@router.post(
    "/flush_report_dy_id",
    response_model=FlushReportByIDResponse,
    summary="报表配置: 刷新报表信息",
)
async def _flush_report_dy_id(
    input_data: FlushReportByID, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await flush_report_dy_id(
        user_id=user_id,
        report_id=input_data.report_id,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.ai_dashboard.schemas.report_manage_schema import UpdateReportByID
from src.ai_dashboard.schemas.report_manage_schema import UpdateReportByIDResponse
from src.ai_dashboard.services.report_manage_service import update_report_dy_id


@router.post(
    "/update_report_dy_id",
    response_model=UpdateReportByIDResponse,
    summary="报表配置: 更新报表信息",
)
async def _update_report_dy_id(
    input_data: UpdateReportByID, user_id: str = Security(get_user_id)
):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    response_data = await update_report_dy_id(
        user_id=user_id,
        report_id=input_data.report_id,
        report_name=input_data.report_name,
        template=input_data.template,
    )

    logger.info(
        f"{user_id:<15} | output | {response_data}"
    )

    return response_data


from src.common.utils.serialize import serialize
from src.ai_dashboard.schemas.report_manage_schema import ChatReport
from src.ai_dashboard.services.report_manage_service import chat_report
from src.common.utils.msg_utils import (
    save_qa_record,
    create_qa_record,
    build_record_data,
)


@router.post("/chat_report", summary="看板对话")
async def _chat_report(input_data: ChatReport, user_id: str = Security(get_user_id)):
    """ """

    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )

    result = await chat_report(
        user_id=user_id,
        user_ask=input_data.user_ask,
        talk_id=input_data.talk_id,
        report_id=input_data.report_id,
        qa_list=input_data.qa_list,
        stream=input_data.stream,
    )

    if input_data.stream:
        qa_id = await create_qa_record(
            user_id=user_id,
            talk_id=input_data.talk_id,
            qa_list=input_data.qa_list,
        )

        async def generator(result):
            async for resp_raw in result:
                resp_msg = await build_record_data(
                    user_id=user_id,
                    talk_id=input_data.talk_id,
                    qa_list=input_data.qa_list,
                    qa_id=qa_id,
                    resp_raw=resp_raw,
                )

                yield "data:" + json.dumps(resp_msg, default=serialize) + "\n\n"

            await save_qa_record(
                user_id=user_id,
                talk_id=input_data.talk_id,
                qa_id=qa_id,
                qa_record=resp_msg,
            )

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    else:
        result = json.loads(json.dumps(result, default=serialize))
        return JSONResponse(result)
