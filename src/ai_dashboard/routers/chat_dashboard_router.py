import os
import sys
import json

from fastapi import APIRouter, Security, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse

from src.common.utils.serialize import serialize
from src.common.utils.logger import logger
from src.common.auth.authorize import get_user_id
from src.ai_dashboard.schemas.chat_dashboard_schema import *
from src.ai_dashboard.services.chat_dashboard_service import *
from src.ai_chat_data.utils.talk_record_utils import update_talk_record
from src.common.utils.msg_utils import (
    save_qa_record,
    create_qa_record,
    build_record_data,
)
router = APIRouter()



@router.post("/select_dashboard_template", summary="仪表盘模版选取")
async def _select_dashboard_template(
     user_id: str = Security(get_user_id)
):
    logger.info(f"{user_id:<15} | input  | {user_id}")   
    result = await get_dashboard_template(user_id)
    return result

@router.post("/select_dashboard_database", summary="仪表盘数据库选取")
async def _select_dashboard_database(
    input_data: Dashboard_DB_Selector, user_id: str = Security(get_user_id)
):
    """选取数据库后生成数据库简要的描述和可以生成的仪表盘问题"""
    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )
    
    try:
        await update_talk_record(user_id, [input_data.data_id], [], input_data.talk_id)
        # 创建QA记录
        qa_id = await create_qa_record(
            user_id=user_id,
            talk_id=input_data.talk_id,
            qa_list=[]
        )
        
        result = select_dashboard_database(
            user_id=user_id,
            data_id=input_data.data_id,
            talk_id=input_data.talk_id
        )
        
        async def generator(result):
            resp_msg = None
            try:
                async for resp_raw in result:
                    resp_msg = await build_record_data(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_list=[],
                        resp_raw=resp_raw,
                    )

                    yield "data:" + json.dumps(resp_msg, default=serialize) + "\n\n"
                
                # 保存记录到数据库
                if resp_msg:
                    await save_qa_record(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_record=resp_msg,
                    )
            except Exception as e:
                logger.exception(f"处理流响应时出错: {str(e)}")
                error_msg = {
                    "code": 500,
                    "msg": f"处理响应时出错: {str(e)}",
                    "data": None
                }
                yield "data:" + json.dumps(error_msg, default=serialize) + "\n\n"

        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    except Exception as e:
        error_msg = f"处理请求时出错: {str(e)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "msg": error_msg,
                "data": None
            }
        )

@router.post("/dashboard_data_confirm", summary="数据表范围组确认")
async def _dashboard_data_confirm(
    input_data: Dashboard_DataConfirm, user_id: str = Security(get_user_id)
):
    """根据用户传入的模板，直接输出仪表盘中每一张表起到的业务作用"""
    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )
    
    try:
        # 创建QA记录
        qa_id = await create_qa_record(
            user_id=user_id,
            talk_id=input_data.talk_id,
            qa_list=input_data.qa_list
        )
        
        result = dashboard_data_confirm(
            user_id=user_id,
            data_id=input_data.data_id,
            template=input_data.template,
            talk_id=input_data.talk_id,
            user_ask=input_data.user_ask,
            last_table_selection=input_data.last_table_selection
        )
        
        async def generator(result):
            resp_msg = None
            try:
                async for resp_raw in result:
                    resp_msg = await build_record_data(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_list=input_data.qa_list,
                        resp_raw=resp_raw,
                    )
                    
                    yield "data:" + json.dumps(resp_msg, default=serialize) + "\n\n"
                
                # 保存记录到数据库
                if resp_msg:
                    await save_qa_record(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_record=resp_msg,
                    )
            except Exception as e:
                logger.exception(f"处理流响应时出错: {str(e)}")
                error_msg = {
                    "code": 500,
                    "msg": f"处理响应时出错: {str(e)}",
                    "data": None
                }
                yield "data:" + json.dumps(error_msg, default=serialize) + "\n\n"
        
        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    except HTTPException as e:
        error_msg = f"数据确认异常: {str(e.detail)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=e.status_code,
            content={
                "code": e.status_code,
                "msg": error_msg,
                "data": None
            }
        )
    except Exception as e:
        error_msg = f"处理请求时出错: {str(e)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "msg": error_msg,
                "data": None
            }
        )

@router.post("/dashboard_questions", summary="仪表盘模版问题生成")
async def _dashboard_questions(
    input_data: Dashboard_Questions, user_id: str = Security(get_user_id)
):
    """根据用户选定的模板，直接生成仪表盘模版（不支持用户聊天交互）"""
    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )
    
    try:
        # 创建QA记录
        qa_id = await create_qa_record(
            user_id=user_id,
            talk_id=input_data.talk_id,
            qa_list=input_data.qa_list
        )
        
        result = dashboard_questions(
            user_id=user_id,
            data_id=input_data.data_id,
            template=input_data.template,
            template_name=input_data.template_name,
            talk_id=input_data.talk_id,
            data_confirm_info=input_data.data_confirm_info,
            user_ask=input_data.user_ask
        )
        
        async def generator(result):
            resp_msg = None
            try:
                async for resp_raw in result:
                    resp_msg = await build_record_data(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_list=input_data.qa_list,
                        resp_raw=resp_raw,
                    )
                    
                    yield "data:" + json.dumps(resp_msg, default=serialize) + "\n\n"
                
                # 保存记录到数据库
                if resp_msg:
                    await save_qa_record(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_record=resp_msg,
                    )
            except Exception as e:
                logger.exception(f"处理流响应时出错: {str(e)}")
                error_msg = {
                    "code": 500,
                    "msg": f"处理响应时出错: {str(e)}",
                    "data": None
                }
                yield "data:" + json.dumps(error_msg, default=serialize) + "\n\n"
        
        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    except HTTPException as e:
        error_msg = f"仪表盘生成异常: {str(e.detail)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=e.status_code,
            content={
                "code": e.status_code,
                "msg": error_msg,
                "data": None
            }
        )
    except Exception as e:
        error_msg = f"处理请求时出错: {str(e)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "msg": error_msg,
                "data": None
            }
        )

@router.post("/dashboard_to_report", summary="仪表盘转换为报告")
async def _dashboard_to_report(
    input_data: Dashboard_ToReport, user_id: str = Security(get_user_id)
):
    """
    将仪表盘转换为报告并自动优化空数据图表
    
    通过多智能体协同工作流程:
    1. 生成报告：填充仪表盘模板数据
    2. 优化报告：分析并优化空数据图表
    3. 重新生成：为修改后的图表重新生成数据
    """
    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )
    
    try:
        # 创建QA记录
        qa_id = await create_qa_record(
            user_id=user_id,
            talk_id=input_data.talk_id,
            qa_list=input_data.qa_list
        )
        
        # 流式调用
        result = dashboard_to_report(
            user_id=user_id,
            data_id=input_data.data_id,
            final_template=input_data.final_template
        )
        
        async def generator(result):
            resp_msg = None
            try:
                async for resp_raw in result:
                    resp_msg = await build_record_data(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_list=input_data.qa_list,
                        resp_raw=resp_raw,
                    )
                    
                    yield "data:" + json.dumps(resp_msg, default=serialize) + "\n\n"
                
                # 保存记录到数据库
                if resp_msg:
                    await save_qa_record(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_record=resp_msg,
                    )
            except Exception as e:
                logger.exception(f"处理流响应时出错: {str(e)}")
                error_msg = {
                    "code": 500,
                    "msg": f"处理响应时出错: {str(e)}",
                    "data": None
                }
                yield "data:" + json.dumps(error_msg, default=serialize) + "\n\n"
        
        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    except HTTPException as e:
        error_msg = f"报告生成异常: {str(e.detail)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=e.status_code,
            content={
                "code": e.status_code,
                "msg": error_msg,
                "data": None
            }
        )
    except Exception as e:
        error_msg = f"处理请求时出错: {str(e)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "msg": error_msg,
                "data": None
            }
        )

@router.post("/modify_report", summary="报告修改")
async def _modify_report(
    input_data: Modify_Report, user_id: str = Security(get_user_id)
):
    """传入模版和用户反馈，对模版的指定key的图表进行修改"""
    logger.info(
        f"{user_id:<15} | input  | {input_data}"
    )
    
    try:
        # 创建QA记录
        qa_id = await create_qa_record(
            user_id=user_id,
            talk_id=input_data.talk_id,
            qa_list=input_data.qa_list,
        )
        
        # 流式调用
        result = modify_report(
            user_id=user_id,
            user_ask=input_data.user_ask,
            data_id=input_data.data_id,
            report=input_data.report,
            chart_key=input_data.chart_key,
            talk_id=input_data.talk_id
        )
        
        async def generator(result):
            resp_msg = None
            try:
                async for resp_raw in result:
                    resp_msg = await build_record_data(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_list=input_data.qa_list,
                        qa_id=qa_id,
                        resp_raw=resp_raw,
                    )
                    
                    yield "data:" + json.dumps(resp_msg, default=serialize) + "\n\n"
                
                # 保存记录到数据库
                if resp_msg:
                    await save_qa_record(
                        user_id=user_id,
                        talk_id=input_data.talk_id,
                        qa_id=qa_id,
                        qa_record=resp_msg,
                    )
            except Exception as e:
                logger.exception(f"处理流响应时出错: {str(e)}")
                error_msg = {
                    "code": 500,
                    "msg": f"处理响应时出错: {str(e)}",
                    "data": None
                }
                yield "data:" + json.dumps(error_msg, default=serialize) + "\n\n"
        
        return StreamingResponse(
            generator(result),
            media_type="text/event-stream",
        )
    except HTTPException as e:
        error_msg = f"图表修改异常: {str(e.detail)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=e.status_code,
            content={
                "code": e.status_code,
                "msg": error_msg,
                "data": None
            }
        )
    except Exception as e:
        error_msg = f"处理请求时出错: {str(e)}"
        logger.exception(error_msg)
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "msg": error_msg,
                "data": None
            }
        )


