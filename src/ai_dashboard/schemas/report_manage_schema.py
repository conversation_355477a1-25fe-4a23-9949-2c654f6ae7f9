from typing import *
from pydantic import BaseModel, Field

from src.ai_dashboard.schemas.chat_dashboard_schema import Dashboard_Template
from src.ai_chat_data.schemas.chat_llm_schema import User<PERSON><PERSON>, TalkID, QAList
from src.ai_chat_data.schemas.chat_data_schema import Stream
from src.ai_data_source.schemas.data_source_schema import (
    DataID,
    Page,
    KeyWord,
    TotalNumber,
    BaseResponse,
    BaseMetaInfo,
)


class ReportName(BaseModel):
    report_name: str | None = Field(
        ...,
        title="仪表盘名称",
        description="保存的仪表盘名称",
    )


class ReportID(BaseModel):
    report_id: str = Field(
        ...,
        title="仪表盘 ID",
        description="仪表盘的唯一标识符",
        examples=["67e2703743e482eb4b327538"],
    )


class ReportIDs(BaseModel):
    report_ids: List[str] = Field(
        ...,
        title="仪表盘 ID 列表",
        description="仪表盘的唯一标识符",
        examples=[["67e2703743e482eb4b327538"]],
    )


class ReportMetaData(ReportID, ReportName, BaseMetaInfo):
    pass


class ReportList(TotalNumber):
    report_list: List[ReportMetaData] = Field(
        ..., title="报表列表", description="报表的列表，不包括具体数据"
    )


################################################################################


class CreateReport(DataID, ReportName, Dashboard_Template):
    pass


class DeleteReport(ReportIDs):
    pass


class FetchReportList(Page, KeyWord):
    pass


class FetchReportByID(ReportID):
    pass


class FetchReportTipByID(ReportID):
    pass


class FlushReportByID(ReportID):
    pass


class UpdateReportByID(ReportID, ReportName, Dashboard_Template):
    pass


class ChatReport(UserAsk, TalkID, ReportID, QAList, Stream):
    pass


################################################################################


class CreateReportResponse(BaseResponse):
    data: ReportID | None = Field(..., title="数据", description="具体内容")


class DeleteReportResponse(BaseResponse):
    data: ReportIDs | None = Field(..., title="数据", description="具体内容")


class FetchReportListResponse(BaseResponse):
    data: ReportList | None = Field(..., title="数据", description="具体内容")


class FetchReportByIDResponse(BaseResponse):
    data: Dict | None = Field(..., title="数据", description="具体内容")


class FetchReportTipByIDResponse(BaseResponse):
    data: Dict | None = Field(..., title="数据", description="具体内容")


class FlushReportByIDResponse(BaseResponse):
    data: Dict | None = Field(..., title="数据", description="具体内容")


class UpdateReportByIDResponse(BaseResponse):
    data: ReportID | None = Field(..., title="数据", description="具体内容")
