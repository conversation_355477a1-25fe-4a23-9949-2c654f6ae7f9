from typing import *
from pydantic import BaseModel, Field
from typing import Optional, Dict, List


from src.ai_chat_data.schemas.chat_data_schema import (
    TalkID,
    QAList
)

# 创建新的Stream类，默认值为True
class Stream(BaseModel):
    stream: bool = Field(
        default=True,
        title="流式输出",
        description="是否使用流式响应",
    )

import json
from pathlib import Path

current_dir = Path(__file__).resolve().parent.parent
template_dir=current_dir / "template"
template_example=json.load(open(template_dir / "time_series.json", "r", encoding="utf-8"))['template_content']

from src.ai_dashboard.utils.template_utils import clear_template_results
template_example_clear=clear_template_results(template_example)


class DataID(BaseModel):
    data_id: str = Field(
        ...,
        title="数据源 ID",
        description="数据源的唯一标识符",
        examples=["67f75d144dc2ff0d254849ff"],
    )


class Dashboard_Template(BaseModel):
    template: List = Field(
        ...,
        title="仪表盘模版",
        description="仪表盘模版框架",
        example=template_example
            )

class Dashboard_Template_Name(BaseModel):
    template_name: str = Field(
        default="无",
        title="仪表盘模版名称",
        description="仪表盘模版名称",
        example="【默认】指标卡仪表模版"
    )

class Dashboard_Questions(DataID, TalkID, Dashboard_Template, Dashboard_Template_Name, QAList, Stream):
    """
    仪表盘模版问题生成，不支持用户聊天交互
    
    通过指定数据源和模板直接生成仪表盘模版
    """
    user_ask: str = Field(
        ...,
        title="用户问题",
        description="用户的问题",
        examples=["销售主题的仪表盘", "订单主题的仪表盘"]
    )
    data_confirm_info: dict = Field(
        ...,
        title="数据表确认信息",
        description="数据表确认信息",
        example={
            "orders": "用于计算最近一个月的总销售额和销售量，以及销售额和销售量的月度趋势。",
            "products": "用于获取每个订单产品的单价，支持销售额的计算。"
        }
    )


class Dashboard_ToReport(DataID, Stream,QAList):
    """
    将仪表盘转换为报告并自动优化空数据图表
    
    通过多智能体协同工作实现：
    1. 报告生成：填充数据到仪表盘模板
    2. 空数据优化：智能分析并修改空数据图表
    3. 数据重生成：为修改后的图表重新查询数据
    """
    final_template: List = Field(
        ...,
        title="仪表盘模版",
        description="已经选定的仪表盘模板",
        example=template_example_clear
    )
    talk_id: str = Field(
        default="",
        title="对话 ID",
        description="当前对话的唯一标识",
    )


class Modify_Report(DataID, Stream,QAList):
    """基于用户反馈修改报告中的指定图表"""
    user_ask: str = Field(
        ...,
        title="用户反馈",
        description="用户对指定图表的修改反馈",
        example="这个图表没有显示我想要的销量趋势，请修改为显示过去12个月的每月销售额变化"
    )
    chart_key: str = Field(
        ...,
        title="图表Key",
        description="需要修改的图表唯一标识",
        examples=["t2cf52f8cec043dd92c75ffff7f74002", "t3df52f9cec043dd92c75ffff7f74003", "t4ef52f6cec043dd92c75ffff7f74004"]
    )
    report: List = Field(
        ...,
        title="仪表盘报告",
        description="带有图表的仪表盘报告",
        example=template_example
    )
    talk_id: str = Field(
        default="",
        title="对话 ID",
        description="当前对话的唯一标识",
    )

class Dashboard_DB_Selector(DataID, Stream):
    """选择数据库以生成仪表盘主题建议"""
    talk_id: str = Field(
        ...,
        title="对话 ID",
        description="当前对话的唯一标识",
    )


class Dashboard_DataConfirm(DataID, TalkID, QAList, Stream):
    """确认仪表盘中表格的作用和关系"""
    template: List = Field(
        ...,
        title="仪表盘模版",
        description="仪表盘模板内容",
        example=template_example_clear
    )
    last_table_selection: List = Field(
        default=[],
        title="上次表选择",
        description="上次选择的表格列表",
        example=["product", "customer"]
    )
    user_ask: str = Field(
        ...,
        title="用户问题",
        description="用户的问题",
        examples=["销售主题的仪表盘", "订单主题的仪表盘"]
    )


class Dashboard_Reading_Guide(DataID, TalkID, QAList, Stream):
    """为仪表盘提供阅读指引和分析思路"""
    template: List = Field(
        ...,
        title="仪表盘模版",
        description="需要生成阅读指引的仪表盘模板",
        example=template_example
    )