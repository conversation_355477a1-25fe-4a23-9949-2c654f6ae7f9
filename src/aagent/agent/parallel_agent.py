import time
import asyncio

from typing import AsyncIterator, Dict, List, Tuple

from ..config.config import config_data
from ..utils.logger import logger
from ..utils.load_prompt import load_prompt

from .aagent import AAgent


class ParallelAgent(AAgent):

    def __init__(
        self,
        parallel_size: int | None = 10,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.parallel_size = parallel_size
        self.parallel_progress = {
            "state": "idle",
            "parallel_size": self.parallel_size,
            "total_parallel_num": 0,
            "processed_parallel_num": 0,
        }

    async def _run(
        self, messages: List[Dict], **kwargs
    ) -> AsyncIterator[Dict] | Dict:
        return await super()._run(messages=messages, **kwargs)

    async def parallel_run(
        self, parallel_messages: List[List[Dict]], parallel_size=None, **kwargs
    ) -> List[Dict]:

        time1 = time.time()

        parallel_size = parallel_size or self.parallel_size

        self.parallel_progress["total_parallel_num"] = len(parallel_messages)
        self.parallel_progress["state"] = "running"

        semaphore = asyncio.Semaphore(parallel_size)
        results = [None] * len(parallel_messages)  # 保证结果顺序
        failures = 0  # 记录失败任务的数量

        async def worker(index, message):
            nonlocal failures  # 声明使用外部变量
            async with semaphore:
                try:
                    result = await self.run_nonstream(message, **kwargs)
                    results[index] = result  # 保证顺序
                except Exception as e:
                    failures += 1  # 记录失败
                    logger.error(f"任务失败: {e}")
                    results[index] = ""  # 失败时返回空字符串
                self.parallel_progress["processed_parallel_num"] += 1

        tasks = [
            worker(index, message) for index, message in enumerate(parallel_messages)
        ]
        await asyncio.gather(*tasks)

        time2 = time.time()
        logger.info(f"并行耗时: {time2 - time1}")
        self.parallel_progress["state"] = "idle"

        # 如果所有任务都失败，抛出错误
        if failures == self.parallel_progress["total_parallel_num"]:
            logger.error("并行任务失败，请检查输入或网络状态。")
            self.parallel_progress["state"] = "error"
            raise RuntimeError("并行任务失败，请检查输入或网络状态。")

        return results
