from typing import Any, Dict, List, AsyncIterator, Union

from ..config.config import config_data
from ..utils.logger import logger
from ..utils.load_prompt import load_prompt
from ..utils.extract_json import extract_code_block, validate_json

from .aagent import AAgent


from pathlib import Path

# 构建 prompt 文件夹的路径
current_dir = Path(__file__).resolve().parent
prompt_dir = current_dir / "prompt"


class ExtractJson(AAgent):

    async def _run(self, messages: List[Dict], **kwargs) -> AsyncIterator[Dict] | Dict:
        if not messages[-1].get("role") == "user":
            raise ValueError("messages 末尾 role 不为 user")
        user_question = messages[-1].get("content")

        SYS = await load_prompt(prompt_dir, "extract_json_sys")
        messages = [
            {"role": "system", "content": SYS},
            {"role": "user", "content": user_question},
        ]

        return await super()._run(messages=messages, **kwargs)

    async def get_block(self, text: str, language: str = None) -> Any:
        if text:
            code_block, sccess = extract_code_block(text, language)
            return code_block, sccess
        else:
            return text, False

    async def get_json(self, text: str):
        if text:
            extract_block, _ = extract_code_block(text)
            extract_json, sccess = validate_json(extract_block)
            
            if sccess:
                return extract_json, True
            else:
                for _ in range(3):
                    logger.warning(f"无法解析json，使用大模型进行处理, json text: {text}")
                    messages = [
                        {"role": "user", "content": text},
                    ]
                    
                    resp = await self.run_nonstream(messages)
                    rtext = resp["ans"]["content"]
                    
                    logger.warning(f"大模型进行处理结果: {rtext}")

                    extract_block, _ = extract_code_block(rtext)
                    extract_json, sccess = validate_json(extract_block)

                    if sccess:
                        return extract_json, True
        else:
            return text, False
