import httpx
import json
import copy
import regex
import asyncio
import json_repair

from typing import AsyncIterator, Dict, List, Union, Callable
from openai import AsyncOpenAI

from ..utils.logger import logger
from ..utils.load_prompt import load_template
from ..utils.fun_to_json import get_json_schema
from ..utils.utils import random_uuid

AgentTools = Callable[[], Union[str, list, dict, "AAgent"]]

from pathlib import Path

# 构建 prompt 文件夹的路径
current_dir = Path(__file__).resolve().parent
prompt_dir = current_dir / "prompt"


class AAgent:
    """Agent的基类。"""

    def __init__(
        self,
        model_config: dict,
        name: str = "Agent",
        system: str = "你是博闻(<PERSON>), 由武汉光谷信息创建，你是一个乐于助人的助手。",
        description: str = "一个标准智能体",
        tools: List[AgentTools] | None = None,
        manual_tools: List[AgentTools] | None = None,
        **kwargs,
    ):
        """初始化

        Args:
            name: 智能体的名称.
            description: 智能体的描述.
            tools: 可以自动使用的工具列表.
            manual_tools: 需要手动处理的工具列表.
        """
        self.name = name
        self.description = description
        self.system = system

        self.tools = tools or []
        self.manual_tools = manual_tools or []
        self.manual_tool_names = [f.__name__ for f in self.manual_tools]

        # tools 列表
        tools_json = [get_json_schema(f) for f in self.tools]
        manual_tools_json = [get_json_schema(f) for f in self.manual_tools]
        self.merged_tools_json = tools_json + manual_tools_json

        self.model_config = model_config
        if not model_config.get("model", None):
            raise ValueError("model is empty")
        if not model_config.get("base_url", None):
            raise ValueError("base_url is empty")

        self.client = AsyncOpenAI(
            api_key=model_config.get("api_key", None),
            base_url=model_config.get("base_url"),
        )

    def ans_to_resp(
        self,
        id: str = "",
        name: str = "",
        description: str = "",
        ans: dict | None = None,
        metadata: dict | None = None,
    ) -> Dict:
        """将文本转化为标准的格式"""

        resp = {
            "id": id,
            "name": "",
            "description": "",
            "ans": ans or {},
            "metadata": metadata or {},
        }

        if name or self.name:
            resp["name"] = name or self.name
        if description or self.description:
            resp["description"] = description or self.description

        return resp

    async def run_nonstream(
        self,
        messages: List[Dict],
        max_turns: int = float("inf"),
        **kwargs,
    ) -> Dict:
        kwargs.pop("stream", None)
        messages = copy.deepcopy(messages)

        if messages[0]["role"] == "system":
            messages[0]["content"] = self.system + "\n" + messages[0]["content"]
        else:
            messages.insert(0, {"role": "system", "content": self.system})

        init_len = len(messages)

        while (len(messages) - init_len) < max_turns:
            # 调用 _run 方法获取 LLM 结果
            responses = await self._run(
                messages=messages,
                tools=self.merged_tools_json,
                stream=False,
                **kwargs,
            )

            # 解析响应中的文本信息
            msg_id = responses.id
            msg_content = responses.choices[0].message.model_dump()
            msg_content = {k: v for k, v in msg_content.items() if v}

            messages.append(msg_content)

            if tool_calls := messages[-1].get("tool_calls", None):
                tool_map = {f.__name__: f for f in self.tools}

                for tool_call in tool_calls:
                    call_id: str = tool_call["id"]
                    if fn_call := tool_call.get("function"):
                        fn_name: str = fn_call["name"]

                        # 需要用户确认
                        needs_user_confirmation = fn_name in self.manual_tool_names
                        if needs_user_confirmation:
                            break  # 暂时中断，等待用户确认

                        fn_args: dict = json_repair.loads(fn_call["arguments"])
                        fn_description: str = AAgent._get_description(
                            self.merged_tools_json, fn_name
                        )

                        # 异步调用的方式
                        async def execute_function(fn_name: str, fn_args: dict):
                            fn = tool_map.get(fn_name)
                            if fn is None:
                                raise ValueError(f"Function {fn_name} not found")

                            if asyncio.iscoroutinefunction(fn):  # 检查是否为异步函数
                                return await fn(**fn_args)
                            else:  # 同步函数直接调用
                                return fn(**fn_args)

                        # 使用异步函数
                        async def get_function_result(fn_name: str, fn_args: dict):
                            fn_res = await execute_function(fn_name, fn_args)
                            return str(fn_res)

                        fn_res = await get_function_result(fn_name, fn_args)

                        tool_content = {
                            "role": "tool",
                            "content": fn_res,
                            "tool_call_id": call_id,
                        }
                        messages.append(tool_content)
            else:
                break

            if needs_user_confirmation:
                break  # 暂时中断，等待用户确认

        resp = self.ans_to_resp(id=msg_id, ans=msg_content)

        return resp

    async def run(
        self,
        messages: List[Dict],
        max_turns: int = float("inf"),
        **kwargs,
    ) -> AsyncIterator[Dict]:
        kwargs.pop("stream", None)
        messages = copy.deepcopy(messages)

        if messages[0]["role"] == "system":
            messages[0]["content"] = self.system + messages[0]["content"]
        else:
            messages.insert(0, {"role": "system", "content": self.system})

        init_len = len(messages)

        while len(messages) - init_len < max_turns:
            # 调用 _run 方法获取 LLM 结果
            responses = await self._run(
                messages=messages,
                tools=self.merged_tools_json,
                stream=True,
                **kwargs,
            )

            text_content = ""
            tool_list = []
            tool_call_idx = -1
            async for chunk in responses:
                resp_id = str(chunk.id)

                if chunk.choices[0].delta.tool_calls:
                    try:
                        tool_call = chunk.choices[0].delta.tool_calls[0]

                        if tool_call.index != tool_call_idx:
                            tool_call_idx = chunk.choices[0].delta.tool_calls[0].index
                            tool_list.append(
                                {
                                    "id": None,
                                    "function": {"name": None, "arguments": ""},
                                    "type": None,
                                }
                            )

                        if tool_call.id:
                            tool_list[tool_call_idx]["id"] = str(tool_call.id)

                        if tool_call.function:
                            if tool_call.function.name:
                                tool_list[tool_call_idx]["function"][
                                    "name"
                                ] = tool_call.function.name
                                tool_list[tool_call_idx]["type"] = "function"

                            if tool_call.function.arguments:
                                tool_list[tool_call_idx]["function"][
                                    "arguments"
                                ] += tool_call.function.arguments
                    except Exception as e:
                        logger.exception(e)

                else:
                    if chunk.choices[0].delta.content:
                        text_content += chunk.choices[0].delta.content

                ans_content = {
                    "role": "assistant",
                    "content": text_content or None,
                }

                if tool_list:
                    ans_content["tool_calls"] = tool_list

                yield self.ans_to_resp(id=resp_id, ans=ans_content)

            messages.append(
                {
                    "role": "assistant",
                    "content": text_content or None,
                    "tool_calls": tool_list,
                }
            )

            if tool_list:
                tool_map = {f.__name__: f for f in self.tools}

                for tool_call in tool_list:
                    call_id: str = tool_call["id"]
                    fn_name: str = tool_call["function"]["name"]

                    # 需要用户确认
                    needs_user_confirmation = fn_name in self.manual_tool_names
                    if needs_user_confirmation:
                        break  # 暂时中断，等待用户确认

                    fn_args: dict = json_repair.loads(
                        tool_call["function"]["arguments"]
                    )
                    fn_description: str = AAgent._get_description(
                        self.merged_tools_json, fn_name
                    )

                    # 异步调用的方式
                    async def execute_function(fn_name: str, fn_args: dict):
                        fn = tool_map.get(fn_name)
                        if fn is None:
                            raise ValueError(f"Function {fn_name} not found")

                        if asyncio.iscoroutinefunction(fn):  # 检查是否为异步函数
                            return await fn(**fn_args)
                        else:  # 同步函数直接调用
                            return fn(**fn_args)

                    # 使用异步函数
                    async def get_function_result(fn_name: str, fn_args: dict):
                        fn_res = await execute_function(fn_name, fn_args)
                        return fn_res

                    fn_res = await get_function_result(fn_name, fn_args)

                    tool_content = {
                        "role": "tool",
                        "content": str(fn_res),
                        "tool_call_id": call_id,
                    }
                    messages.append(tool_content)

                    new_msg_id = "chatcmpl-" + random_uuid()
                    resp = self.ans_to_resp(
                        id=new_msg_id,
                        ans=tool_content,
                        name=fn_name,
                        description=fn_description,
                    )
                    yield resp
            else:
                break

            if needs_user_confirmation:
                break  # 暂时中断，等待用户确认

    async def _run(
        self,
        messages: List[Dict],
        **kwargs,
    ) -> AsyncIterator[Dict] | Dict:

        stream = await self.client.chat.completions.create(
            model=self.model_config["model"],
            messages=messages,
            stream=kwargs.pop("stream", True),
            tools=kwargs.pop("tools", []),
            temperature=kwargs.pop("temperature", self.model_config.get("temperature", 0.6)),
            max_tokens=kwargs.pop("max_tokens", 8192),
            **kwargs,
        )

        return stream

    @staticmethod
    def _get_description(fn_json: list[dict], name: str) -> str | None:
        """
        根据给定的函数名称获取对应的描述。
        """
        for item in fn_json:
            if (
                item.get("type") == "function"
                and item.get("function", {}).get("name") == name
            ):
                return item["function"].get("description")
        return None
