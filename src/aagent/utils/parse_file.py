import io
import os
import json
import httpx
import markdown
import aiohttp
import tempfile

import xml.etree.ElementTree as ET
import pandas as pd

from bs4 import BeautifulSoup
from utils.file_schema import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.call_llm import upload_and_parse_file, get_tokenize

def parse_text(file_content: bytes) -> str:
    return file_content.decode("utf-8")


def parse_csv(file_content: bytes) -> str:
    df = pd.read_csv(io.BytesIO(file_content))
    return df.to_string()


def parse_excel(file_content: bytes) -> str:
    df = pd.read_excel(io.BytesIO(file_content))
    return df.to_string()


def parse_json(file_content: bytes) -> str:
    data = json.loads(file_content)
    return json.dumps(data, indent=2)


def parse_xml(file_content: bytes) -> str:
    root = ET.fromstring(file_content)
    return ET.tostring(root, encoding="utf8", method="xml").decode()


def parse_html(file_content: bytes) -> str:
    soup = BeautifulSoup(file_content, "html.parser")
    return soup.get_text()


async def parse_file(file_url: bytes, file_type:str) -> str:
    results = await upload_and_parse_file(file_url, file_type)
    text = []
    for result in results["result"]:
        text.append(result["text"])
        
    return "\n\n".join(text)


def parse_md(file_content: bytes) -> str:
    return markdown.markdown(file_content.decode("utf-8"))


async def fetch_file_content(url: str) -> bytes:
    async with httpx.AsyncClient() as client:
        response = await client.get(url)
        response.raise_for_status()
        return response.content


def process_file_content(file_content: bytes, file_type: str):
    # Define parsers for different file types
    parsers = {
        "text": parse_text,
        # "eml": parse_file,
        "csv": parse_csv,
        "excel": parse_excel,
        "json": parse_json,
        "xml": parse_xml,
        # "epub": parse_file,
        "html": parse_html,
        # "pdf": parse_file,
        # "doc": parse_file,
        # "ppt": parse_file,
        "markdown": parse_md,
    }

    if file_type in parsers:
        return parsers[file_type](file_content)
    else:
        raise ValueError("Unsupported file type")


async def get_file_content(file_url: str, max_len:int = 0):
    """从文件中提取信息

    Args:
        file_url: 文件地址
        max_len: 提取的最大token数. Defaults to 0.

    Returns:
        _type_: 文件的文本内容
    """
    file_content = await fetch_file_content(file_url)

    # 文件类型
    checker = FileTypeChecker()
    file_type = checker.get_file_type(file_url)
    file_name = file_url.split('/')[-1]

    # 提取文件内容
    if file_type in ["eml", "epub", "pdf", "doc", "ppt"]:
        processed_content = await parse_file(file_url, file_type)
    else:
        processed_content = process_file_content(file_content, file_type)
        
    if max_len:
        processed_content = await get_tokenize(processed_content, max_len)

    return processed_content


async def fetch_file_content(source: str) -> bytes:
    if os.path.isfile(source):  # 检查是否为本地文件
        with open(source, 'rb') as file:
            return file.read()
    else:  # 否则假设是 URL
        async with httpx.AsyncClient() as client:
            response = await client.get(source)
            response.raise_for_status()
            return response.content
    

async def download_file(url):
    """
    异步从给定的 URL 下载文件并返回临时文件路径。

    :param url: 文件的 URL
    :return: 临时文件路径
    """
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()  # 确保请求成功
            content = await response.read()

            # 在临时文件中写入下载的内容
            with tempfile.NamedTemporaryFile(delete=True) as temp_file:
                temp_file.write(content)
                temp_file.flush()  # 确保写入内容

                return temp_file.name
            
if __name__ == "__main__":
    import asyncio

    file_url = (
        "http://192.168.7.140:9000/agent-files/test2-2024-09-07-10-52-04-962862.pdf"
    )

    result = asyncio.run(get_file_content(file_url))

    # 输出处理后的内容
    print(result)
