from typing import Dict
from .call_llm import get_tokenize

import regex

async def split_token(
        file_content: str, chunks_size: int = 100, chunks_strde: int = 8000
    ) -> Dict:

        file_chunks = await get_tokenize(file_content, chunks_size, chunks_strde)
        file_chunks = file_chunks[0]["text"]

        return file_chunks

async def split_doc(
        file_content: str, chunks_size: int = None, 
    ):

        # 正则表达式匹配句子及其分隔符
        pattern = r"([^。,.，\n\t]+[。,.，\n\t]*)"
        sentences = regex.findall(pattern, file_content)

        chunks = []

        current_chunk = ""
        previous_sentence = ""
        for sentence in sentences:
            if len(current_chunk) + len(sentence) <= math.floor(chunks_size):
                current_chunk += sentence  # 添加句子
                previous_sentence = sentence
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())  # 添加当前段落
                current_chunk = previous_sentence + sentence  # 重置为当前句子
                previous_sentence = sentence

        if current_chunk:  # 添加最后一段
            chunks.append(current_chunk.strip())

        return chunks