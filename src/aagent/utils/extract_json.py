import json_repair
import regex
import re


def extract_code_block(text, languages: list = None):
    if not languages:
        languages = [
            "sql",
            "json",
            "code",
            "markdown",
            "md",
            "python",
            "javascript",
            "html",
            "css",
        ]

    # 正则模式，匹配 ```lang 或 ``` 包裹的代码块
    pattern = rf'```(?:{"|".join(languages)}|)\s*\n?(.*?)\n?```'
    matches = regex.findall(pattern, text, regex.DOTALL)

    return (matches[0], True) if matches else (text, False)


def validate_json(json_string):

    try:
        clear_text = remove_outer_text(json_string)
        real_json = json_repair.loads(clear_text)
        
        return real_json, True
    except:
        return json_string, False


def remove_outer_text(text):
    first_bracket = -1
    last_bracket = -1

    for i, char in enumerate(text):
        if first_bracket == -1 and char in ['[', '{']:
            first_bracket = i  # 记录第一个 [ 或 {
        if char in [']', '}']:
            last_bracket = i  # 更新最后一个 ] 或 }

    # 如果没有找到任何括号，返回原文本
    if first_bracket == -1 or last_bracket == -1:
        return text

    # 确保最后一个括号在第一个括号之后
    if last_bracket < first_bracket:
        return text

    return text[first_bracket : last_bracket + 1]

if __name__ == "__main__":

    json_str = """
{
    "template": [
        [{"span": 6, "chart_type": "IndicatorBoard", "chart_title": "当月总销售额", "content": "计算当月所有订单的销售额总和", "target_tables": ["orders", "products"], "key": "a1b2c3d4e5f6", "result": {}}, 
         {"span": 6, "chart_type": "IndicatorBoard", "chart_title": "当月销售数量", "content": "计算当月所有订单的销售数量总和", "target_tables": ["orders"], "key": "f6e5d4c3b2a1", "result": {}}],
        [{"span": 12, "chart_type": "LineChart", "chart_title": "过去12个月销售趋势", "content": "展示过去12个月每月的总销售额和销售数量趋势", "target_tables": ["orders", "products"], "key": "1a2b3c4d5e6f", "result": {}}],
        [{"span": 6, "chart_type": "BarChart", "chart_title": "客户地区销售额对比", "content": "按客户所在地区对比销售额", "target_tables": ["orders", "customers"], "key": "6f5e4d3c2b1a", "result": {}}, 
         {"span": 6, "chart_type": "PieChart", "chart_title": "产品类别销售额占比", "content": "展示各产品类别的销售额占比", "target_tables": ["orders", "products"], "key": "abcdef123456", "result": {}}]
    ]
}asdasd
    """

    json_str = remove_outer_text(json_str)
    aaa,_=validate_json(json_str)
    print(aaa)
    print(type(aaa))
    
    