from transformers.utils import get_json_schema

"""
此函数根据给定函数的文档字符串和类型提示为其生成 JSON 模式。
这主要用于将工具列表传递给聊天模板。
JSON 模式包含函数的名称和说明，以及每个参数的名称、类型和说明。
get_json_schema()`要求函数有一个 docstring，并且每个参数在 docstring 中都有描述，标准的 Google docstring 格式如下所示。
它还要求所有函数参数都有有效的 Python 类型提示。

虽然不是必需的，但也可以添加一个 `Returns` 块，它将包含在模式中。这是可选项，因为大多数聊天模板都会忽略函数的返回值。

Args:
    func: The function to generate a JSON schema for.

Returns:
    A dictionary containing the JSON schema for the function.

Examples:
```python
>>> def multiply(x: float, y: float):
>>>    '''
>>>    A function that multiplies two numbers
>>>
>>>    Args:
>>>        x: The first number to multiply
>>>        y: The second number to multiply
>>>    '''
>>>    return x * y
>>>
>>> print(get_json_schema(multiply))
{
    "name": "multiply",
    "description": "A function that multiplies two numbers",
    "parameters": {
        "type": "object",
        "properties": {
            "x": {"type": "number", "description": "The first number to multiply"},
            "y": {"type": "number", "description": "The second number to multiply"}
        },
        "required": ["x", "y"]
    }
}
```

这些模式的一般用途是为支持它们的聊天模板生成工具描述，
就像这样：

```python
>>> from transformers import AutoTokenizer
>>> from transformers.utils import get_json_schema
>>>
>>> def multiply(x: float, y: float):
>>>    '''
>>>    A function that multiplies two numbers
>>>
>>>    Args:
>>>        x: The first number to multiply
>>>        y: The second number to multiply
>>>    return x * y
>>>    '''
>>>
>>> multiply_schema = get_json_schema(multiply)
>>> tokenizer = AutoTokenizer.from_pretrained("CohereForAI/c4ai-command-r-v01")
>>> messages = [{"role": "user", "content": "What is 179 x 4571?"}]
>>> formatted_chat = tokenizer.apply_chat_template(
>>>     messages,
>>>     tools=[multiply_schema],
>>>     chat_template="tool_use",
>>>     return_dict=True,
>>>     return_tensors="pt",
>>>     add_generation_prompt=True
>>> )
>>> # The formatted chat can now be passed to model.generate()
```
每个参数描述的末尾还可以有一个可选的 `(choices: ...)`，如 `(choices: ["tea", "coffee"])` ，它将被解析为模式中的一个 “enum ”字段。
请注意，只有当它位于行尾时才会被正确解析：

```python
>>> def drink_beverage(beverage: str):
>>>    '''
>>>    A function that drinks a beverage
>>>
>>>    Args:
>>>        beverage: The beverage to drink (choices: ["tea", "coffee"])
>>>    '''
>>>    pass
>>>
>>> print(get_json_schema(drink_beverage))
```
{
    'name': 'drink_beverage',
    'description': 'A function that drinks a beverage',
    'parameters': {
        'type': 'object',
        'properties': {
            'beverage': {
                'type': 'string',
                'enum': ['tea', 'coffee'],
                'description': 'The beverage to drink'
                }
            },
        'required': ['beverage']
    }
}
"""
    
    