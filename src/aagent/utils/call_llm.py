import httpx
from ..config.config import config_data
from .logger import logger

sign_key = config_data["llm_sign"]
llm_url = config_data["llm_api"]


async def get_generate(
    prompt: str = "",
    messages: list = [],
    text: str = "",
    stream: bool = False,
    **kwargs,
):
    url = f"{llm_url}/generate"

    if not any([prompt, messages, text]):
        logger.error("至少需要提供一个有效的 'prompt'、'messages' 或 'text' 参数。")
        raise ValueError("至少需要提供一个有效的 'prompt'、'messages' 或 'text' 参数。")

    payload = {
        "prompt": prompt,
        "messages": messages,
        "text": text,
        "delta_stream": False,
        "stream": stream,
        "sign": sign_key,
        **kwargs,
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json;charset=utf-8"},
            timeout=60,
        )

        if stream:
            return response.aiter_lines()
        else:
            return response.json()


async def get_tokenize(
    text: str, max_length: int | None = None, stride: int | None = None
):
    url = f"{llm_url}/tokenize"
    payload = {"text": text, "sign": sign_key}

    if max_length is not None:
        payload["max_length"] = max_length
    if stride is not None:
        payload["stride"] = stride

    async with httpx.AsyncClient() as client:
        response = await client.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json;charset=utf-8"},
            timeout=60,
        )
        return response.json()


async def apply_chat_template(
    messages, tools: list | None = None, chat_template: str | None = None
):
    url = f"{llm_url}/apply_chat_template"
    payload = {"messages": messages, "sign": sign_key}

    if tools is not None:
        payload["tools"] = tools

    if chat_template is not None:
        payload["chat_template"] = chat_template

    async with httpx.AsyncClient() as client:
        response = await client.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json;charset=utf-8"},
            timeout=60,
        )
        return response.json()


async def upload_and_parse_file(
    file_path: str,
    file_type: str = "pdf",
    url: str = "http://192.168.100.37:8000/upload_and_parse",
) -> list[dict]:
    """
    异步上传文件并解析，返回带图片链接的文本内容。

    参数：
    - file_path: str，文件路径
    - file_type: str，文件类型，默认为"pdf"
    - url: str，服务器地址和端口，默认为 "http://192.168.100.37:8000/upload_and_parse"

    返回：
    - list[dict]，包含解析后的文本和图像链接
    """
    async with httpx.AsyncClient() as client:
        # 打开文件并发送请求
        with open(file_path, "rb") as f:
            files = {"file_byte": (file_path, f, "application/pdf")}
            data = {"file_type": file_type}
            response = await client.post(url, files=files, data=data)

        # 处理响应
        result = response.json().get("result", [])

        # 替换文本中的图片标记
        for item in result:
            for img_cnt, img_url in item.get("images", [{}])[0].items():
                img_flag = f"<bowen_img_cnt:{img_cnt}>"
                item["text"] = item["text"].replace(img_flag, img_url)

    return result


# async def use_file_parse_api_async(file: bytes, file_name: str):
#     api_url = f"http://192.168.100.37:8000/upload_and_parse"
#     result = None
#     file_type = file_name.split('.')[-1]
#     print(f'{file_type=:}')
#     form_data = aiohttp.FormData()
#     form_data.add_field('file_byte', file, filename=file_name, content_type='application/octet-stream')
#     form_data.add_field('file_type', file_type)
#     async with aiohttp.ClientSession() as session:
#         async with session.post(api_url, data=form_data) as response:
#             if response.status == 200:
#                     result = await response.json()
#             else:
#                 print("parse_file接口调用失败")
#     return result
