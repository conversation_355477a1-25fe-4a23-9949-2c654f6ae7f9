import os
import aiofiles


# 异步读取指定路径中的文件内容
async def load_prompt(path: str, name: str) -> str:    
    name += '.txt'
    file_path = os.path.join(path, name)
    
    try:
        # 使用 aiofiles 异步读取文件
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
        return content
    except FileNotFoundError:
        return ""

# 异步读取指定路径中的文件内容
async def load_template(path: str, name: str) -> str:
    name += '.jinja'
    file_path = os.path.join(path, name)
    
    try:
        # 使用 aiofiles 异步读取文件
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
        return content
    except FileNotFoundError:
        return ""