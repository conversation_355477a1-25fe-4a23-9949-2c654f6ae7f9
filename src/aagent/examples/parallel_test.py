import asyncio
import os
import sys

PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(PATH)

from AAgent.agent.parallel_agent import ParallelAgent


async def main():
    from AAgent.tools.weather_tools import get_abcode, get_weather

    SYS = """你是一个天气预报小助手"""
    USER1 = """明天武汉市的天气"""
    USER2 = """明天北京的天气"""
    TOOLS = [get_abcode, get_weather]
    messages1 = [
        {
            "role": "user",
            "content": USER1,
        },
    ]
    messages2 = [
        {
            "role": "user",
            "content": USER2,
        },
    ]

    p_msg = [messages1,messages2]
    p_agent = ParallelAgent(name="天气助手", system="你是一个天气预报小助手", tools=TOOLS)

    ans = await p_agent.parallel_run(parallel_messages=p_msg)

asyncio.run(main())
