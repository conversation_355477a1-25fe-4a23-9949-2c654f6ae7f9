import asyncio
import os
import sys

PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(PATH)

from AAgent.agent.aagent import AAgent
from AAgent.tools.weather_tools import get_abcode, get_weather
from AAgent.tools.time_tools import get_current_date, get_weekday, get_current_time
from AAgent.tools.triage_tool import create_triage_tool


async def main():

    SYS = """您要对用户请求进行分类，并调用工具以转移到正确的智能体。准备好转移到正确的意图后，请调用triage_tool以转移到正确的智能体。您不需要知道具体细节，只需了解用户请求的主题。"""
    # USER = """告诉我你能做什么"""
    USER = """告诉我现在几点，星期几"""
    # USER = """告诉我下一个周六是几号"""
    # USER = """假设今天是2024年10月29日，那么今天是周几"""
    # USER = """上一个周一是几号"""

    DATE_TOOLS = [get_current_date, get_weekday, get_current_time]
    WEATHER_TOOLS = [get_abcode, get_weather]

    messages = [
        # {
        #     "role": "system",
        #     "content": "使用小朋友的语气回答用户的问题",
        # },
        {
            "role": "user",
            "content": USER,
        },
    ]

    date_agent = AAgent(
        name="日期助手",system="你是一个日期助手，可以查询日期以及星期几，不要相信你的记忆中的日期和星期几，不要虚构日期来查询星期几，永远通过tools进行查询", description="可以查询时间，日期以及星期几", tools=DATE_TOOLS
    )
    weather_agent = AAgent(
        name="天气助手", system="你是一个天气助手，可以指定地区的未来天气", description="可以指定地区的未来天气", tools=WEATHER_TOOLS
    )

    triage_tool = create_triage_tool([date_agent, weather_agent])
    triage_agent = AAgent(name="分流助手", description="一个用于使用切换适合的agent回答问题的分类智能体",system=SYS, tools=[triage_tool])

    # 非流式运行
    ans = await triage_agent.run_nonstream(messages=messages)
    print(ans)

    # # 流式运行
    # async for line in triage_agent.run(messages=messages):
    #     print(line)


asyncio.run(main())
