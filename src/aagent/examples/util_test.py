import asyncio
import os
import sys

PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(PATH)

from AAgent.agent.aagent import AAgent
from AAgent.utils.call_llm import *
from AAgent.utils.load_prompt import load_template
from AAgent.utils.fun_to_json import get_json_schema
from AAgent.tools.triage_tool import create_triage_tool

# TEXT = "在这个示例中，使用了信号量来控制并发数量，确保同时进行的任务不超过 parallel_size。你需要根据具体的消息处理逻辑修改 process_message 函数。"
TEXT = '{"a" : “asdad”}'
MSG = [
    {"role": "system", "content": "你是一个日期助手"},
    {"role": "user", "content": "告诉我现在几点，今天是星期几"},
    {
        "role": "assistant",
        "content": "",
        "tool_calls": [
            {
                "type": "function",
                "function": {"name": "get_current_time", "arguments": {}},
            },
            {
                "type": "function",
                "function": {"name": "get_current_date", "arguments": {}},
            },
        ],
    },
    {
        "role": "tool",
        "tool_name": "get_current_time",
        "tool_args": {},
        "content": "15:10:21",
    },
    {
        "role": "tool",
        "tool_name": "get_current_date",
        "tool_args": {},
        "content": "2024-10-31",
    },
    {
        "role": "assistant",
        "content": "现在的时间是 15:10:21，今天是 2024年10月31日。根据这个日期，我们来确定一下今天是星期几。",
        "tool_calls": [
            {
                "type": "function",
                "function": {
                    "name": "get_weekday",
                    "arguments": {"date_str": "2024-10-31"},
                },
            }
        ],
    },
    {
        "role": "tool",
        "tool_name": "get_weekday",
        "tool_args": {"date_str": "2024-10-31"},
        "content": "星期四",
    },
]


async def main():
    # 将 msg 根据 chat_template 转化为 text ,然后直接使用 text 获得 LLM 结果
    # chat_template = await load_template(
    #     path="/home/<USER>/project/my_agent/AAgent/prompt", name="chat_template"
    # )
    # resp = await apply_chat_template(messages=MSG, chat_template=chat_template)
    # print(resp)
    # ans = await get_generate(text=resp, stream=False)
    # print(ans)
    
    # 或者直接使用 chat_template 以及 msg 进行生成
    # ans = await get_generate(messages=MSG, stream=False, chat_template=chat_template)
    # print(ans)

    # 自动生成一个分流函数
    date_agent = AAgent(name="日期助手", description="可以查询日期以及星期几")
    weather_agent = AAgent(name="天气助手", description="可以指定地区的未来天气")
    triage_tool = create_triage_tool([date_agent, weather_agent])
    
    # 将函数转换为 json_schema
    json_schema = get_json_schema(triage_tool)
    print(json_schema["function"]["description"])

# 运行主协程
asyncio.run(main())
