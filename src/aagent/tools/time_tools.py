
from datetime import datetime

    
def get_current_date() -> str:
    """返回今天的日期，格式为 YYYY-MM-DD
    """
    return datetime.now().strftime("%Y-%m-%d")

async def get_current_time() -> str:
    """返回当前的时间，格式为 HH:MM:SS"""
    return datetime.now().strftime("%H:%M:%S")

def get_weekday(date_str: str) -> str:
    """根据输入的日期字符串返回星期几

    Args:
        date_str: %Y-%m-%d 格式的日期

    Returns:
        返回中文的星期几
    """
    weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    return weekdays[date_obj.weekday()]

