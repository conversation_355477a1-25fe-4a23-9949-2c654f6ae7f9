import httpx
import asyncio
import os
import sys

PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(PATH)


async def get_abcode(city: str):
    """根据中文的城市名，获取城市的 ID

    Args:
        city: 中文的城市名。如“武汉”

    Returns:
        城市的行政区划代码 (adcode)，如果请求失败或未找到则返回 None。
    """
    url = "https://restapi.amap.com/v3/config/district"
    params = {
        "key": "f6e7669ebc2b9c2118de9aedb98d21ee",
        "keywords": city,
        "subdistrict": 0,
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            if data.get("status") == "1" and data["districts"]:
                return data["districts"][0]["adcode"]
            return f"请求的地区不存在: {city}"

        except httpx.RequestError as e:
            return f"网络连接错误: {e}"
        except httpx.HTTPStatusError as e:
            return f"请求错误，状态码: {e.response.status_code}"


async def get_weather(adcode: str, days: int = 1):
    """根据城市代码获取未来的天气预报

    Args:
        adcode: 城市的行政区划代码 (如"420100"表示武汉)。
        days: 需要的未来天气预报天数，默认1天。

    Returns:
        包含天气信息的字典列表，如果请求失败或无数据则返回 None。
    """
    url = "https://restapi.amap.com/v3/weather/weatherInfo"
    params = {
        "key": "f6e7669ebc2b9c2118de9aedb98d21ee",
        "city": adcode,
        "extensions": "all",
        "output": "json",
    }

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, params=params)
            response.raise_for_status()
            data = response.json()

            if data.get("status") == "1" and data["forecasts"]:
                forecasts_list = data["forecasts"][0].get("casts", [])

                if not forecasts_list:
                    return f"无可用天气数据: {adcode}"
                
                # 星期数字到中文的映射
                week_map = {
                    "1": "一",
                    "2": "二",
                    "3": "三",
                    "4": "四",
                    "5": "五",
                    "6": "六",
                    "7": "天",
                }

                all_forecasts_info = []
                for forecast in forecasts_list[:days]:  # 仅获取前 days 天的预报
                    forecast_info = {
                        "日期": forecast["date"],
                        "星期": week_map[forecast["week"]],
                        "白天天气": forecast["dayweather"],
                        "夜间天气": forecast["nightweather"],
                        "白天温度": forecast["daytemp"],
                        "夜间温度": forecast["nighttemp"],
                        "白天风向": forecast["daywind"],
                        "夜间风向": forecast["nightwind"],
                        "白天风力": forecast["daypower"],
                        "夜间风力": forecast["nightpower"],
                    }
                    all_forecasts_info.append(forecast_info)
                
                return all_forecasts_info

            return f"请求的数据不存在: {adcode}"

        except httpx.RequestError as e:
            return f"网络连接错误: {e}"
        except httpx.HTTPStatusError as e:
            return f"请求错误，状态码: {e.response.status_code}"


async def main():
    abcode = await get_abcode("武汉")
    weather = await get_weather(abcode, days=2)
    print(abcode)
    print(weather)


# 运行主协程
# asyncio.run(main())
