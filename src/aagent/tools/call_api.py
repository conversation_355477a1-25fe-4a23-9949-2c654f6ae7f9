import httpx
from loguru import logger

async def call_api(
    url: str,
    method: str = "POST",
    params: dict | None = None,
    data: dict | None = None,
    headers: dict | None = None,
    timeout: float = 10.0
) -> dict | None:
    """调用API并返回响应结果。

    Args:
        url: API的请求地址。
        method: HTTP请求方法，默认为 "POST"。
        params: GET请求的查询参数，默认为 None。
        data: POST请求的 JSON 数据，默认为 None。
        headers: HTTP请求头，默认为 None。
        timeout: 请求超时时间（秒），默认为 10.0。

    Raises:
        ValueError: 当请求方法不支持时抛出此异常。

    Returns:
        返回 JSON 格式的响应数据。如果请求失败，返回 None。
    """
    try:
        async with httpx.AsyncClient() as client:
            if method.upper() == "GET":
                response = await client.get(url, params=params, headers=headers, timeout=timeout)
            elif method.upper() == "POST":
                response = await client.post(url, json=data, headers=headers, timeout=timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()  # 检查是否有 HTTP 错误
            return response.json()  # 返回 JSON 格式的数据

    except httpx.RequestError as e:
        logger.error(f"请求时出错 {e.request.url!r}: {str(e)}")
    except httpx.HTTPStatusError as e:
        logger.error(f" 请求 {e.request.url!r}: {e.response.text}，出现响应错误{e.response.status_code}")
    except Exception as e:
        logger.error(f"发生意外错误: {str(e)}")
    
    return None
