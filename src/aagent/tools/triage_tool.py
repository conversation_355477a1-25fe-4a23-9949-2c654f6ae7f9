from typing import List
from ..agent.aagent import AAgent

def create_triage_tool(agnet_list: List[AAgent]):
    agnet_dict = {}
    agent_n_d = []
    for agent in agnet_list:
        agnet_dict[agent.name] = agent
        agent_n_d.append(f"Agent name: {agent.name}\nAgent description: {agent.description}")
    agent_n_d_str = '\n---\n'.join(agent_n_d)
    
    triage_tool_doc = f"""该函数用于切换当前对话的智能体，可选的智能体的名称与描述如下所示
{agent_n_d_str}
Args:
    agnet_name: 切换的智能体名称
    reason: 解释需要切换的智能体的理由
"""
    def triage_tool(agnet_name: str, reason:str):
        return agnet_dict[agnet_name]
    
    triage_tool.__doc__=triage_tool_doc
    return triage_tool