

# docker 部署


## 后端环境
### 基础环境
构建镜像
```
docker build -f deploy/backend/base/Dockerfile -t aidb_base:v0.18.0 -t aidb_base:latest .
```

docker run -it --rm aidb_base:latest


上传至 harbor
```
docker login https://harbor.cnovit.com
docker tag aidb_base:latest  harbor.cnovit.com/prc-basic/aidb_base:latest
docker push harbor.cnovit.com/prc-basic/aidb_base:latest
```

## 以下流程已作废，使用 zadig 部署

### 开发环境
构建镜像
```
docker build -f deploy/backend/dev/Dockerfile -t aidb_dev:v2.4.0 -t aidb_dev:latest .
```

启动镜像
```
docker run -itd --name aidb_dev -p 8783:8795 -v /opt/aidb/aidb_dev/log:/aidb/log  aidb_dev:latest
```

上传至 harbor
```
docker login https://harbor.cnovit.com
docker tag aidb_dev:latest  harbor.cnovit.com/prc-dev/aidb_dev:latest
docker push harbor.cnovit.com/prc-dev/aidb_dev:latest
```

### 测试环境

构建镜像
```
docker build -f deploy/backend/test/Dockerfile -t aidb_test:v2.4.0 -t aidb_test:latest .
```

启动镜像
```
docker run -itd --name aidb_test -p 8785:8795 -v /opt/aidb/aidb_test/log:/aidb/log  aidb_test:latest
```

上传至 harbor
```
docker login https://harbor.cnovit.com
docker tag aidb_test:latest harbor.cnovit.com/other/aidb_test:atest
docker push harbor.cnovit.com/other/aidb_test:latest
```

### 正式环境
构建镜像
```
docker build -f deploy/backend/prod/Dockerfile -t aidb_prod:v2.4.0 -t aidb_prod:latest .
```

启动镜像
```
docker run -itd --name aidb_prod -p 8787:8795 -v /opt/aidb/aidb_prod/log:/aidb/log  aidb_prod:latest
```

上传至 harbor
```
docker login https://harbor.cnovit.com
docker tag aidb_prod:latest harbor.cnovit.com/ovit/aidb_prod:latest
docker push harbor.cnovit.com/ovit/aidb_prod:latest
```

## 前端环境
### 开发环境
构建镜像
```
docker build -f deploy/frontend/dev/Dockerfile -t aidb_web_dev:v2.4.1 -t aidb_web_dev:latest .
```

启动镜像
```
docker run -itd --name aidb_web_dev -p 18500:80 aidb_web_dev:latest
```

### 测试环境
构建镜像
```
docker build -f deploy/frontend/test/Dockerfile -t aidb_web_test:v2.4.1 -t aidb_web_test:latest .
```

启动镜像
```
docker run -itd --name aidb_web_test -p 18500:80 aidb_web_test:latest
```

上传至 harbor
```
docker login https://harbor.cnovit.com
docker tag aidb_web_test:latest harbor.cnovit.com/other/aidb_web_test:latest
docker push harbor.cnovit.com/other/aidb_web_test:latest
```

### 正式环境

构建镜像
```
docker build -f deploy/frontend/prod/Dockerfile -t aidb_web_prod:v2.4.1 -t aidb_web_prod:latest .
```

启动镜像
```
docker run -itd -p 18700:80 --name agent_web_prod aidb_web_prod:latest
```

上传至 harbor
```
docker login https://harbor.cnovit.com
docker tag aidb_web_prod:latest harbor.cnovit.com/ovit/aidb_web_prod:latest
docker push harbor.cnovit.com/ovit/aidb_web_prod:latest
```