apiVersion: apps/v1
kind: Deployment
metadata:
  name: aidb-web
  labels:
    cnovit.com/app: aidb-web
spec:
  replicas: 1
  selector:
    matchLabels:
      cnovit.com/app: aidb-web
  template:
    metadata:
      labels:
        cnovit.com/app: aidb-web
    spec:
      containers:
        - name: web
          image: harbor.cnovit.com/ovit/aidb_web_prod:v1.4.1 # 根据情况修改镜像名
          ports:
            - containerPort: 80
              name: wer-port
          volumeMounts:
            - mountPath: /etc/nginx/nginx.conf
              name: nginx
              subPath: nginx.conf
            - mountPath: /usr/share/nginx/html/_app.config.js
              name: vol-web
              subPath: _app.config.js
      volumes:
        - name: vol-web
          configMap:
            name: aidb-web-config
        - name: nginx
          configMap:
            name: aidb-web-nginx
---
apiVersion: v1
kind: Service
metadata:
  name: aidb
spec:
  ports:
    - name: wer-port
      port: 80
      targetPort: 80
  selector:
    cnovit.com/app: aidb-web
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: aidb-web-config
data:
  _app.config.js: |
    window.__PRODUCTION__BOWENDATABASEADMIN__CONF__={"VITE_GLOB_APP_TITLE":"博闻智数","VITE_GLOB_APP_SHORT_NAME":"BowenDatabaseAdmin","VITE_GLOB_APP_CAS_BASE_URL":"http://bowen-test.cnovit.com/auth","VITE_GLOB_APP_OPEN_SSO":"true","VITE_GLOB_APP_OPEN_QIANKUN":"true","VITE_GLOB_ONLINE_VIEW_URL":"http://bowen-test.cnovit.com/preview/onlinePreview","VITE_GLOB_API_URL":"http://**************:8785","VITE_GLOB_DOMAIN_URL":"http://**************:8785","VITE_GLOB_APP_URL":"http://**************:18443/ovitboot","VITE_GLOB_SYSTEM_URL":"http://localhost:3101/","VITE_GLOB_AGENT_URL":"http://bowen-agents-test.cnovit.com/ai/application","VITE_GLOB_API_URL_PREFIX":""};Object.freeze(window.__PRODUCTION__BOWENDATABASEADMIN__CONF__);Object.defineProperty(window,"__PRODUCTION__BOWENDATABASEADMIN__CONF__",{configurable:false,writable:false,});


# 需要修改上一行中的以下内容
#后台接口父地址： VITE_GLOB_API_URL
#后台接口全路径地址： VITE_GLOB_DOMAIN_URL
# 统一平台接口地址： VITE_GLOB_APP_URL
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    s-product: aidb
    s-service: aidb-web
  name: aidb-web-nginx
data:
  aidb-web-nginx.conf: |
    user  root;
    worker_processes  1;

    events {
        worker_connections  1024;
    }

    http {
        include       mime.types;
        default_type  application/octet-stream;

        autoindex off;
        sendfile        on;
        keepalive_timeout  65;

        server {
        listen 80;
        server_name localhost;

        location / {
          root  /usr/share/nginx/html;
          index index.html;
          try_files $uri $uri/ /index.html;
        }
      }
    }
