apiVersion: apps/v1
kind: Deployment
metadata:
  name: aidb-server
spec:
  replicas: 1
  selector:
    matchLabels:
      cnovit.com/app: aidb-server
  template:
    metadata:
      labels:
        cnovit.com/app: aidb-server
    spec:
      containers:
        - name: aidb-server
          image: harbor.cnovit.com/other/aidb-test:v1.4.2 # 镜像名称更新为 aidb-test:v1.4.2
          env:
            - name: PYTHONPATH
              value: /aidb
            - name: PYTHON_CONFIG
              value: test
          ports:
            - containerPort: 8795 # 容器端口为 8795，根据启动命令配置
              name: server-port
          volumeMounts:
            - name: config-volume
              mountPath: /aidb/config/test.yaml
              subPath: test.yaml
            - name: log-volume
              mountPath: /aidb/log
      volumes:
        - name: config-volume
          configMap:
            name: aidb-config
        - name: log-volume
          emptyDir: {} # 使用 emptyDir 存储日志，可以根据需要修改为持久化存储
---
apiVersion: v1
kind: Service
metadata:
  name: aidb-server
spec:
  selector:
    cnovit.com/app: aidb-server
  ports:
    - name: server-port
      port: 8785 # 映射的外部端口为 8785
      targetPort: 8795 # 容器内部端口为 8795
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: aidb-config
data:
  test.yaml: |
    # llm
    llm_api: http://**********:9023
    llm_sign: qwen2-5-api-sign-vllm-v5-8Gy19F2fa9T5

    # embedding
    emb_api: http://**********:8872
    emb_sign: EMB-DC60A938-89CF-1743-100C-F407A0D1B47F

    n_dims: 768
    table_emb_num: 10
    columns_emb_num: 10
    final_table_num: 5
    top_rows: 200

    # 数据库
    mongo:
      ip: **************
      port: 27017
    minio:
      endpoint: *************:9000
      access_key: minioadmin
      secret_key: minioadmin
      bucket_name: aidb-files
    mysql:
      host: **************
      port: 3306
      user: root
      password: admin
