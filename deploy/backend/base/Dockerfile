FROM docker.xuanyuan.me/continuumio/miniconda3

LABEL maintainer="wwq"

# 设置时区
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 配置apt镜像源
RUN echo 'Types: deb\n\
URIs: https://mirrors.tuna.tsinghua.edu.cn/debian\n\
Suites: bookworm bookworm-updates bookworm-backports\n\
Components: main contrib non-free non-free-firmware\n\
Signed-By: /usr/share/keyrings/debian-archive-keyring.gpg\n\
\n\
Types: deb\n\
URIs: https://security.debian.org/debian-security\n\
Suites: bookworm-security\n\
Components: main contrib non-free non-free-firmware\n\
Signed-By: /usr/share/keyrings/debian-archive-keyring.gpg' > /etc/apt/sources.list.d/debian.sources

# 安装基本工具
RUN apt-get update && \
    apt-get install -y vim nano default-jdk && \
    rm -rf /var/lib/apt/lists/*

# 配置conda清华镜像源

RUN conda install python=3.11 -y && \
    conda install thrift thrift_sasl

COPY requirements.txt /app/requirements.txt

RUN pip install --no-cache-dir -r /app/requirements.txt -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple && \
    rm -rf /root/.cache
